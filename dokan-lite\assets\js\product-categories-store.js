(()=>{"use strict";var e={n:r=>{var t=r&&r.__esModule?()=>r.default:()=>r;return e.d(t,{a:t}),t},d:(r,t)=>{for(var o in t)e.o(t,o)&&!e.o(r,o)&&Object.defineProperty(r,o,{enumerable:!0,get:t[o]})},o:(e,r)=>Object.prototype.hasOwnProperty.call(e,r),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},r={};e.r(r),e.d(r,{DOKAN_PRODUCT_CATEGORIES_STORE:()=>C,default:()=>E});const t=window.wp.data,o={categories:{},categoryQueries:{},isCategoriesLoading:!1,categoryError:null},a="SET_CATEGORIES",s="SET_CATEGORY_QUERY",g="SET_CATEGORY_ERROR",i="SET_CATEGORIES_LOADING",n={setCategories:e=>({type:a,categories:e}),setCategoryQuery:(e,r,t,o)=>({type:s,queryId:e,ids:r,totalCount:t,totalPages:o}),setCategoryError:e=>({type:g,error:e}),setCategoriesLoading:e=>({type:i,isLoading:e})},d=window.wp.url,c={getItem:(e,r)=>e.categories[r],getItems:(e,r={})=>{const t=(0,d.addQueryArgs)("/dokan/v2/categories",r),o=JSON.stringify((0,d.getQueryArgs)(t)),a=e.categoryQueries[o];if(a)return a.ids.map(r=>e.categories[r]).filter(Boolean)},getCategoryQueryTotalCount:(e,r={})=>{const t=(0,d.addQueryArgs)("/dokan/v2/categories",r),o=JSON.stringify((0,d.getQueryArgs)(t));return e.categoryQueries[o]?.totalCount},getCategoryQueryTotalPages:(e,r={})=>{const t=(0,d.addQueryArgs)("/dokan/v2/categories",r),o=JSON.stringify((0,d.getQueryArgs)(t));return e.categoryQueries[o]?.totalPages},isCategoriesLoading:e=>e.isCategoriesLoading,getCategoryError:e=>e.categoryError},u=window.wp.apiFetch;var y=e.n(u);const l={getItems:(e={})=>async({dispatch:r})=>{r(n.setCategoriesLoading(!0));try{const t=(0,d.addQueryArgs)("/dokan/v1/products/categories",e),o=JSON.stringify((0,d.getQueryArgs)(t)),a=await y()({path:t,parse:!1}),s=await a.json(),g=parseInt(a.headers.get("X-WP-Total")||"0",10),i=parseInt(a.headers.get("X-WP-TotalPages")||"0",10),c={},u=s?.map(e=>(c[e.id]=e,e?.id));r(n.setCategories(c)),r(n.setCategoryQuery(o,u,g,i))}catch(e){r(n.setCategoryError(e))}finally{r(n.setCategoriesLoading(!1))}},getItem:e=>async({dispatch:r})=>{try{const t=await y()({path:`/dokan/v1/products/categories/${e}`});return r(n.setCategories({[e]:t}))}catch(e){return r(n.setCategoryError(e))}}},C="dokan/product-categories",p=(0,t.createReduxStore)(C,{reducer:(e=o,r)=>{switch(r.type){case a:return{...e,categories:{...e.categories,...r.categories},categoryError:null};case s:return{...e,categoryQueries:{...e.categoryQueries,[r.queryId]:{ids:r.ids,totalCount:r.totalCount,totalPages:r.totalPages}},categoryError:null};case g:return{...e,categoryError:r.error};case i:return{...e,isCategoriesLoading:r.isLoading};default:return e}},actions:n,selectors:c,resolvers:l});(0,t.register)(p);const E=p;(window.dokan=window.dokan||{}).productCategoriesStore=r})();