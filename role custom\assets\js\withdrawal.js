/**
 * Para Çekme Sistemi JavaScript
 */

jQuery(document).ready(function($) {
    'use strict';

    // Para çekme formu
    $('#withdrawal-form').on('submit', function(e) {
        e.preventDefault();
        
        var form = $(this);
        var submitButton = form.find('button[type="submit"]');
        var amount = parseFloat($('#withdrawal_amount').val());
        var method = $('#withdrawal_method').val();
        
        // Form validasyonu
        if (!validateWithdrawalForm()) {
            return false;
        }
        
        // Onay mesajı
        if (!confirm(roleCustomWithdrawal.messages.confirm_withdrawal)) {
            return false;
        }
        
        // Loading durumu
        form.addClass('loading');
        submitButton.prop('disabled', true).text(roleCustomWithdrawal.messages.processing);
        
        // Önceki mesajları temizle
        $('.withdrawal-message').remove();
        
        // AJAX isteği
        $.ajax({
            url: roleCustomWithdrawal.ajax_url,
            type: 'POST',
            data: {
                action: 'role_custom_submit_withdrawal',
                withdrawal_amount: amount,
                withdrawal_method: method,
                withdrawal_note: $('#withdrawal_note').val(),
                withdrawal_nonce: $('#withdrawal_nonce').val()
            },
            success: function(response) {
                if (response.success) {
                    showMessage(response.data.message, 'success');
                    form[0].reset(); // Formu temizle
                } else {
                    showMessage(response.data.message, 'error');
                }
            },
            error: function() {
                showMessage(roleCustomWithdrawal.messages.error, 'error');
            },
            complete: function() {
                // Loading durumunu kaldır
                form.removeClass('loading');
                submitButton.prop('disabled', false).text('Para Çekme Talebi Gönder');
            }
        });
    });
    
    // Hesap detayları formu
    $('#account-details-form').on('submit', function(e) {
        e.preventDefault();
        
        var form = $(this);
        var submitButton = form.find('button[type="submit"]');
        
        // Loading durumu
        form.addClass('loading');
        submitButton.prop('disabled', true).text(roleCustomWithdrawal.messages.processing);
        
        // Önceki mesajları temizle
        $('.withdrawal-message').remove();
        
        // AJAX isteği
        $.ajax({
            url: roleCustomWithdrawal.ajax_url,
            type: 'POST',
            data: {
                action: 'role_custom_update_account_details',
                bank_name: $('#bank_name').val(),
                account_holder: $('#account_holder').val(),
                account_number: $('#account_number').val(),
                iban: $('#iban').val(),
                paypal_email: $('#paypal_email').val(),
                account_details_nonce: $('#account_details_nonce').val()
            },
            success: function(response) {
                if (response.success) {
                    showMessage(response.data.message, 'success');
                } else {
                    showMessage(response.data.message, 'error');
                }
            },
            error: function() {
                showMessage(roleCustomWithdrawal.messages.error, 'error');
            },
            complete: function() {
                // Loading durumunu kaldır
                form.removeClass('loading');
                submitButton.prop('disabled', false).text('Hesap Detaylarını Güncelle');
            }
        });
    });
    
    // Para çekme miktarı validasyonu
    $('#withdrawal_amount').on('input', function() {
        var amount = parseFloat($(this).val());
        var maxAmount = parseFloat($(this).attr('max'));
        var field = $(this);
        
        if (amount > maxAmount) {
            field.addClass('form-invalid').removeClass('form-valid');
            showFieldError(field, 'Yetersiz bakiye! Maksimum çekilebilir miktar: ' + maxAmount);
        } else if (amount <= 0) {
            field.addClass('form-invalid').removeClass('form-valid');
            showFieldError(field, 'Geçerli bir miktar giriniz.');
        } else {
            field.addClass('form-valid').removeClass('form-invalid');
            hideFieldError(field);
        }
    });
    
    // IBAN formatı kontrolü
    $('#iban').on('input', function() {
        var iban = $(this).val().replace(/\s/g, '').toUpperCase();
        var field = $(this);
        
        // IBAN formatını kontrol et (Türkiye için)
        if (iban.length > 0) {
            if (iban.length === 26 && iban.startsWith('TR')) {
                field.addClass('form-valid').removeClass('form-invalid');
                hideFieldError(field);
            } else {
                field.addClass('form-invalid').removeClass('form-valid');
                showFieldError(field, 'Geçerli bir IBAN numarası giriniz (TR ile başlamalı, 26 karakter).');
            }
        } else {
            field.removeClass('form-valid form-invalid');
            hideFieldError(field);
        }
    });
    
    // E-posta validasyonu
    $('#paypal_email').on('input', function() {
        var email = $(this).val();
        var field = $(this);
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email.length > 0) {
            if (emailRegex.test(email)) {
                field.addClass('form-valid').removeClass('form-invalid');
                hideFieldError(field);
            } else {
                field.addClass('form-invalid').removeClass('form-valid');
                showFieldError(field, 'Geçerli bir e-posta adresi giriniz.');
            }
        } else {
            field.removeClass('form-valid form-invalid');
            hideFieldError(field);
        }
    });
    
    /**
     * Para çekme formu validasyonu
     */
    function validateWithdrawalForm() {
        var isValid = true;
        var amount = parseFloat($('#withdrawal_amount').val());
        var maxAmount = parseFloat($('#withdrawal_amount').attr('max'));
        var method = $('#withdrawal_method').val();
        
        // Miktar kontrolü
        if (!amount || amount <= 0) {
            showFieldError($('#withdrawal_amount'), 'Geçerli bir miktar giriniz.');
            isValid = false;
        } else if (amount > maxAmount) {
            showFieldError($('#withdrawal_amount'), 'Yetersiz bakiye!');
            isValid = false;
        }
        
        // Ödeme yöntemi kontrolü
        if (!method) {
            showFieldError($('#withdrawal_method'), 'Ödeme yöntemi seçiniz.');
            isValid = false;
        }
        
        return isValid;
    }
    
    /**
     * Mesaj göster
     */
    function showMessage(message, type) {
        var messageHtml = '<div class="withdrawal-message ' + type + '">' + message + '</div>';
        $('.role-custom-withdrawal h1').after(messageHtml);
        
        // Mesajı otomatik gizle (başarı mesajları için)
        if (type === 'success') {
            setTimeout(function() {
                $('.withdrawal-message.success').fadeOut();
            }, 5000);
        }
    }
    
    /**
     * Alan hatası göster
     */
    function showFieldError(field, message) {
        hideFieldError(field); // Önceki hatayı temizle
        field.after('<div class="field-error" style="color: #dc3545; font-size: 12px; margin-top: 5px;">' + message + '</div>');
    }
    
    /**
     * Alan hatasını gizle
     */
    function hideFieldError(field) {
        field.next('.field-error').remove();
    }
    
    /**
     * Sayı formatı (para birimi)
     */
    function formatCurrency(amount) {
        return new Intl.NumberFormat('tr-TR', {
            style: 'currency',
            currency: 'TRY'
        }).format(amount);
    }
    
    /**
     * IBAN formatla (4'lü gruplar halinde)
     */
    function formatIBAN(iban) {
        return iban.replace(/(.{4})/g, '$1 ').trim();
    }
    
    // IBAN otomatik formatlama
    $('#iban').on('input', function() {
        var value = $(this).val().replace(/\s/g, '').toUpperCase();
        var formatted = formatIBAN(value);
        $(this).val(formatted);
    });
    
    // Sayısal alanlar için sadece sayı girişi
    $('#withdrawal_amount').on('keypress', function(e) {
        var charCode = (e.which) ? e.which : e.keyCode;
        if (charCode != 46 && charCode > 31 && (charCode < 48 || charCode > 57)) {
            return false;
        }
        return true;
    });
    
    // Hesap numarası için sadece sayı
    $('#account_number').on('keypress', function(e) {
        var charCode = (e.which) ? e.which : e.keyCode;
        if (charCode > 31 && (charCode < 48 || charCode > 57)) {
            return false;
        }
        return true;
    });
    
});
