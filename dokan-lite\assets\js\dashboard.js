"use strict";(self.webpackChunkdokan=self.webpackChunkdokan||[]).push([[945],{1415:(e,n,a)=>{a.r(n),a.d(n,{default:()=>u});var s=a(8846),r=a(6087),l=a(7723),t=a(6427),d=a(8232),o=a(790);const c=({isEmbedded:e,query:n})=>{const a=(0,d.Qk)("vendorBalance",0);return(0,o.jsx)(t.Fill,{name:"woocommerce_header_item",children:(0,o.jsx)("div",{className:"dokan-analytics-vendor-earning-section",children:(0,o.jsxs)("h4",{className:"vendor-earning-title dokan-layout",children:[(0,l.__)("Balance:","dokan-lite"),(0,o.jsx)("span",{className:"text-dokan-primary vendor-earning",dangerouslySetInnerHTML:{__html:a}})]})})})},i=(0,r.lazy)(()=>Promise.all([a.e(8139),a.e(3404)]).then(a.bind(a,7295)));class h extends r.Component{render(){const{path:e,query:n}=this.props;return(0,o.jsxs)(r.Suspense,{fallback:(0,o.jsx)(s.Spinner,{}),children:[(0,o.jsx)(c,{query:n}),(0,o.jsx)(i,{query:n,path:e})]})}}const u=h}}]);