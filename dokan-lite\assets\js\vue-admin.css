.dokan-dashboard .widgets-wrapper {
  display: block;
  overflow: hidden;
  margin-top: 15px;
  width: 100%;
}
.dokan-dashboard .widgets-wrapper .left-side,
.dokan-dashboard .widgets-wrapper .right-side {
  float: left;
  width: 48%;
}
.dokan-dashboard .widgets-wrapper .left-side {
  margin-right: 3%;
}
.dokan-dashboard .dokan-postbox .loading {
  display: block;
  width: 100%;
  margin: 15px auto;
  text-align: center;
}
.dokan-dashboard .subscribe-box {
  margin: 20px -12px -11px -12px;
  padding: 0 15px 15px;
  background: #fafafa;
  border-top: 1px solid #efefef;
  position: relative;
}
.dokan-dashboard .subscribe-box h3 {
  margin: 10px 0;
}
.dokan-dashboard .subscribe-box p {
  margin-bottom: 10px !important;
}
.dokan-dashboard .subscribe-box .thank-you {
  background: #4fa72b;
  margin-top: 10px;
  padding: 15px;
  border-radius: 3px;
  color: #fff;
}
.dokan-dashboard .subscribe-box .form-wrap {
  display: flex;
}
.dokan-dashboard .subscribe-box .form-wrap input[type="email"] {
  width: 100%;
  padding: 3px 0 3px 6px;
  margin: 0px -1px 0 0;
}
.dokan-dashboard .subscribe-box .form-wrap button.button {
  box-shadow: none;
  background: #FF5722;
  color: #fff;
  border-color: #FF5722;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.dokan-dashboard .subscribe-box .form-wrap button.button:hover {
  background: #ff6a3c;
}
.dokan-dashboard .subscribe-box .loading {
  position: absolute;
  height: 100%;
  margin: 0 0 0 -15px;
  background: rgba(0, 0, 0, 0.2);
}
.dokan-dashboard .subscribe-box .loading .dokan-loader {
  margin-top: 30px;
}
@media only screen and (max-width: 770px) {
.dokan-dashboard .widgets-wrapper .left-side {
    margin-right: 0;
}
.dokan-dashboard .widgets-wrapper .left-side,
  .dokan-dashboard .widgets-wrapper .right-side {
    width: auto;
}
}
@media only screen and (max-width: 500px) {
.dokan-dashboard .widgets-wrapper .left-side {
    margin-right: 0;
}
.dokan-dashboard .widgets-wrapper .left-side,
  .dokan-dashboard .widgets-wrapper .right-side {
    width: auto;
}
.dokan-dashboard .postbox.dokan-status ul li a .details span.up,
  .dokan-dashboard .postbox.dokan-status ul li a .details span.down {
    display: none;
}
.dokan-dashboard .postbox.dokan-status ul li a strong {
    font-size: 16px;
}
}
@media only screen and (max-width: 360px) {
.dokan-dashboard .postbox.dokan-status ul li a .details {
    display: none;
}
}

.withdraw-requests .dokan-modal .modal-body {
  min-height: 130px;
}
.withdraw-requests .dokan-modal .modal-body textarea {
  width: 100%;
}
.withdraw-requests .image {
  width: 10%;
}
.withdraw-requests .seller {
  width: 20%;
}
.withdraw-requests td.seller img {
  float: left;
  margin-right: 10px;
  margin-top: 1px;
  width: 24px;
  height: auto;
}
.withdraw-requests td.seller strong {
  display: block;
  margin-bottom: 0.2em;
  font-size: 14px;
}
.withdraw-requests td.actions,
.withdraw-requests th.actions {
  width: 120px;
}
.withdraw-requests td.status span {
  line-height: 2.5em;
  padding: 5px 8px;
  border-radius: 4px;
}
.withdraw-requests td.status .approved {
  background: #c6e1c6;
  color: #5b841b;
}
.withdraw-requests td.status .pending {
  background: #f8dda7;
  color: #94660c;
}
.withdraw-requests td.status .cancelled {
  background: #eba3a3;
  color: #761919;
}
.withdraw-requests .method_details_inner p {
  margin-bottom: 2px;
}
.withdraw-requests select#filter-payment-methods {
  width: 175px;
}
.withdraw-requests .select2.select2-container {
  width: 190px;
  vertical-align: top;
}
@media only screen and (max-width: 600px) {
.withdraw-requests table td.seller,
  .withdraw-requests td.amount,
  .withdraw-requests td.actions {
    display: table-cell !important;
}
.withdraw-requests table th:not(.check-column):not(.seller):not(.amount):not(.actions) {
    display: none;
}
.withdraw-requests table td:not(.check-column):not(.seller):not(.amount):not(.actions) {
    display: none;
}
.withdraw-requests table th.column,
  .withdraw-requests table td.column {
    width: auto;
}
.withdraw-requests table td.column.actions .dashicons {
    width: 14px;
    height: 14px;
    font-size: 18px;
}
.withdraw-requests table td.seller .row-actions {
    display: inline-block;
}
.withdraw-requests table td.seller .row-actions span {
    font-size: 11px;
}
}
@media only screen and (max-width: 376px) {
.withdraw-requests table td.seller .row-actions {
    display: inline-block;
}
.withdraw-requests table td.seller .row-actions span {
    font-size: 9px;
}
}
@media only screen and (max-width: 320px) {
.withdraw-requests table td.column.actions .dashicons {
    width: 10px;
    height: 10px;
    font-size: 14px;
}
}

.cta-section {
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  text-align: center;
  padding: 70px 0;
  color: #fff;
}
.cta-section img {
  box-shadow: 0px 3px 70px 0px rgba(126, 17, 0, 0.35);
}
.cta-section h2 {
  font-size: 30px;
  color: #FFC700;
  font-weight: 400;
  line-height: 1.333em;
  text-align: center;
  margin: 10px 0;
  text-shadow: 0px 1px 2px rgba(255, 255, 255, 0.31);
}
.cta-section p {
  font-size: 16px;
  line-height: 1.5em;
  font-weight: 300;
  max-width: 335px;
  margin: 0 auto 1rem auto;
}
.cta-section .btn {
  background-color: #A244FF;
  color: #FFFFFF;
  font-size: 1.2rem;
  line-height: 1.538em;
  font-weight: 300;
  border-radius: 14px;
  text-decoration: none;
  display: inline-flex;
  padding: 1rem 2.8rem;
  align-items: center;
}
.cta-section .btn:hover {
  box-shadow: 3px 7px 20px 0 rgba(0, 0, 0, 0.15);
}
.cta-section .btn p {
  color: #FFF;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.cta-section .btn svg {
  width: 2.5rem;
  height: 1.8rem;
}

/* Slider */
.slick-slider
{
    position: relative;

    display: block;
    box-sizing: border-box;

    -webkit-user-select: none;
       -moz-user-select: none;
            user-select: none;

    -webkit-touch-callout: none;
    -khtml-user-select: none;
    touch-action: pan-y;
    -webkit-tap-highlight-color: transparent;
}

.slick-list
{
    position: relative;

    display: block;
    overflow: hidden;

    margin: 0;
    padding: 0;
}
.slick-list:focus
{
    outline: none;
}
.slick-list.dragging
{
    cursor: pointer;
    cursor: hand;
}

.slick-slider .slick-track,
.slick-slider .slick-list
{
    transform: translate3d(0, 0, 0);
}

.slick-track
{
    position: relative;
    top: 0;
    left: 0;

    display: block;
    margin-left: auto;
    margin-right: auto;
}
.slick-track:before,
.slick-track:after
{
    display: table;

    content: '';
}
.slick-track:after
{
    clear: both;
}
.slick-loading .slick-track
{
    visibility: hidden;
}

.slick-slide
{
    display: none;
    float: left;

    height: 100%;
    min-height: 1px;
}
[dir='rtl'] .slick-slide
{
    float: right;
}
.slick-slide img
{
    display: block;
}
.slick-slide.slick-loading img
{
    display: none;
}
.slick-slide.dragging img
{
    pointer-events: none;
}
.slick-initialized .slick-slide
{
    display: block;
}
.slick-loading .slick-slide
{
    visibility: hidden;
}
.slick-vertical .slick-slide
{
    display: block;

    height: auto;

    border: 1px solid transparent;
}
.slick-arrow.slick-hidden {
    display: none;
}

.dokan-pro-features {
  font-family: 'Open Sans', sans-serif;
  margin: -10px -20px 0 -22px;
}
.dokan-pro-features .vendor-capabilities-banner {
  display: flex;
  align-items: center;
  padding: 70px 50px;
  height: auto;
  border-radius: 9px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  margin: 20px 70px;
}
.dokan-pro-features .vendor-capabilities-banner .content {
  margin: 30px;
}
.dokan-pro-features .vendor-capabilities-banner .content .title {
  font-size: 51px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  background: linear-gradient(90deg, #FFF 34.5%, #D68FFF 100%);
  background-clip: text !important;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.dokan-pro-features .vendor-capabilities-banner .content p {
  color: #FFFFFF;
  max-width: 512px;
  width: 100%;
  font-size: 16px;
}
.dokan-pro-features .vendor-capabilities-banner a {
  box-shadow: none;
  background: #825eed;
  color: #fff;
  border-color: #7047EB;
  font-size: 16px;
  padding: 0 1rem;
}
.dokan-pro-features .vendor-capabilities-banner a:hover {
  color: #fff;
  background: #7047EB;
}
.dokan-pro-features a {
  transition: all 0.2s ease;
}
.dokan-pro-features .section-title {
  text-align: center;
  color: #2e2c2c;
  font-size: 30px;
  line-height: 1.5em;
  font-weight: 400;
  position: relative;
  margin: 0 0 58px;
}
.dokan-pro-features .section-title:after {
  content: '';
  position: absolute;
  bottom: -21px;
  left: 50%;
  transform: translateX(-50%);
  width: 38px;
  height: 5px;
  border-radius: 3px;
  background-color: #f2624d;
}
.dokan-pro-features .header-section {
  background-color: #f2f2f2;
  color: #3f4143;
  font-size: 16px;
  display: flex;
  align-items: center;
  padding: 0 8%;
  border-bottom: 1px solid #eee;
}
@media (min-width: 1281px) {
.dokan-pro-features .header-section {
    padding: 0 24%;
}
}
.dokan-pro-features .header-section .feature-thumb img {
  width: 233px;
  margin: -20px 0 -30px;
}
.dokan-pro-features .header-section .content-area {
  padding-left: 10%;
}
.dokan-pro-features .header-section .content-area h1 {
  font-size: 27px;
  line-height: 1.5em;
  color: #2e2c2c;
}
.dokan-pro-features .header-section .content-area p {
  font-size: 16px;
  line-height: 1.5em;
  font-weight: 300;
  margin: 0;
  letter-spacing: 0.5px;
}
.dokan-pro-features .service-section {
  text-align: center;
  padding: 73px 30px 85px;
  background-color: #f7f8f9;
}
@media (min-width: 1281px) {
.dokan-pro-features .service-section {
    padding-left: 20%;
    padding-right: 20%;
}
}
.dokan-pro-features .service-section .service-list {
  display: flex;
  flex-wrap: wrap;
}
.dokan-pro-features .service-section .service-list .service-box {
  flex: 0 0 28.9%;
  margin: 2.2%;
  text-align: center;
  border-radius: 5px;
  background-color: #ffffff;
}
.dokan-pro-features .service-section .service-list .service-box .service-thumb img {
  border-radius: 5px 5px 0 0;
  max-width: 100%;
}
.dokan-pro-features .service-section .service-list .service-box .service-detail {
  padding: 20px 25px 25px;
}
.dokan-pro-features .service-section .service-list .service-box .service-detail h3 {
  color: #3f4143;
  font-size: 15px;
  line-height: 1.5em;
  font-weight: 300;
  margin: 0;
}
.dokan-pro-features .service-section .btn {
  font-size: 16px;
  line-height: 1.5em;
  font-weight: 300;
  color: #ffff;
  background-color: #3984b5;
  border: 1px solid #226b9b;
  border-radius: 3px;
  text-decoration: none;
  padding: 13px 28px;
  display: inline-block;
  margin-top: 52px;
  transition: all 0.2s ease;
}
.dokan-pro-features .service-section .btn:hover {
  box-shadow: 2.736px 7.518px 20px 0 rgba(0, 0, 0, 0.15);
}
.dokan-pro-features .service-section .btn svg {
  width: 15px;
  fill: #fff;
  margin-left: 5px;
}
.dokan-pro-features .comparison-section {
  background-color: #f1f1f1;
  padding: 75px;
}
@media (min-width: 1281px) {
.dokan-pro-features .comparison-section {
    padding-left: 21.2%;
    padding-right: 21.2%;
}
}
.dokan-pro-features .comparison-section .section-title {
  margin-bottom: 90px;
}
.dokan-pro-features .comparison-section .comparison-area {
  display: flex;
  justify-content: space-between;
}
.dokan-pro-features .comparison-section .comparison-area .compare-box {
  flex: 0 0 48%;
  background-color: #fff;
  border: 1px solid #E0E9EC;
  border-radius: 5px;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  padding: 50px;
  box-sizing: border-box;
}
.dokan-pro-features .comparison-section .comparison-area .compare-box .logo-section {
  margin-bottom: 58px;
  position: relative;
}
.dokan-pro-features .comparison-section .comparison-area .compare-box .logo-section:after {
  content: "";
  position: absolute;
  bottom: -23px;
  left: 0;
  width: 38px;
  height: 5px;
  border-radius: 3px;
  background-color: #d7d7d7;
}
.dokan-pro-features .comparison-section .comparison-area .compare-box .logo-section img {
  width: 120px;
}
.dokan-pro-features .comparison-section .comparison-area .compare-box .compare-list {
  margin: 0;
}
.dokan-pro-features .comparison-section .comparison-area .compare-box .compare-list li {
  font-size: 15px;
  line-height: 1.5em;
  font-weight: 300;
  color: #66676E;
  margin-bottom: 28px;
  position: relative;
  display: flex;
  align-items: center;
}
.dokan-pro-features .comparison-section .comparison-area .compare-box .compare-list li:last-child {
  margin-bottom: 0;
}
.dokan-pro-features .comparison-section .comparison-area .compare-box .compare-list li img {
  width: 21px;
  margin-right: 12px;
}
.dokan-pro-features .comparison-section .comparison-area .compare-box .compare-list li.unavailable {
  color: #ccced5;
}
.dokan-pro-features .pricing-section {
  background-color: #f7f8f9;
  padding: 85px 0 62px;
}
.dokan-pro-features .pricing-section .section-title {
  margin-bottom: 120px;
}
.dokan-pro-features .pricing-section .pricing-wrapper {
  padding: 0 4.5%;
}
@media (min-width: 1281px) {
.dokan-pro-features .pricing-section .pricing-wrapper {
    padding-left: 21%;
    padding-right: 21%;
}
}
.dokan-pro-features .pricing-section .table-row {
  display: flex;
}
.dokan-pro-features .pricing-section .table-row:first-child .table-col:first-child {
  background-color: transparent;
  border-color: transparent;
  border-radius: 3px 3px 0 0;
}
.dokan-pro-features .pricing-section .table-row:first-child .table-col:nth-child(2) {
  border-radius: 3px 0 0 0;
}
.dokan-pro-features .pricing-section .table-row:first-child .table-col:last-child {
  border-radius: 0 3px 0 0;
}
.dokan-pro-features .pricing-section .table-row:first-child .table-col.popular {
  position: relative;
}
.dokan-pro-features .pricing-section .table-row:first-child .table-col.popular:before {
  position: absolute;
  top: -28px;
  left: 0;
  width: 100%;
  content: 'Most Popular';
  color: #23282d;
  font-size: 14px;
  line-height: 1.4em;
  background-color: #ffcc00;
  border-radius: 3px 3px 0 0;
  padding: 5px;
  box-sizing: border-box;
}
.dokan-pro-features .pricing-section .table-row:nth-child(2) .table-col:first-child {
  border-radius: 3px 3px 0 0;
}
.dokan-pro-features .pricing-section .table-row:nth-last-child(2) .table-col:first-child {
  border-radius: 0 0 5px 5px;
  border-bottom: 1px solid #e7eaeb;
}
.dokan-pro-features .pricing-section .table-row:last-child .table-col {
  border-bottom: 1px solid #e7eaeb;
}
.dokan-pro-features .pricing-section .table-row:last-child .table-col:first-child {
  border-radius: 0 0 5px 5px;
  background-color: transparent;
  border-color: transparent;
}
.dokan-pro-features .pricing-section .table-row:last-child .table-col:nth-child(2) {
  border-radius: 0 0 0 5px;
}
.dokan-pro-features .pricing-section .table-row:last-child .table-col:last-child {
  border-radius: 0 0 5px 0;
}
.dokan-pro-features .pricing-section .table-row .table-col {
  width: 18.5%;
  text-align: center;
  border: 1px solid #e7eaeb;
  padding: 18px;
  background-color: #fff;
  border-bottom: 0;
  border-left: 0;
  border-right: 0;
}
.dokan-pro-features .pricing-section .table-row .table-col:first-child {
  width: 25%;
  margin-right: 2%;
  text-align: left;
  border-left: 1px solid #e7eaeb;
  border-right: 1px solid #e7eaeb;
}
.dokan-pro-features .pricing-section .table-row .table-col:nth-child(2) {
  border-left: 1px solid #e7eaeb;
}
.dokan-pro-features .pricing-section .table-row .table-col:last-child {
  border-left: 1px solid #e7eaeb;
  border-right: 1px solid #e7eaeb;
}
.dokan-pro-features .pricing-section .table-row .table-col.popular {
  z-index: 1;
  box-shadow: 1px 10px 30px 0 rgba(215, 223, 254, 0.5);
}
.dokan-pro-features .pricing-section .table-row .table-col .module-name {
  color: #157ef5;
  font-size: 14px;
  line-height: 1.5em;
  font-weight: 300;
  text-decoration: none;
}
.dokan-pro-features .pricing-section .table-row .table-col .plan-data {
  font-size: 15px;
  color: #3f4143;
}
.dokan-pro-features .pricing-section .table-row .table-col .plan-data img {
  display: block;
  margin: 0 auto;
  width: 23px;
}
.dokan-pro-features .pricing-section .table-row .table-col .buy-btn {
  font-size: 14px;
  font-weight: 300;
  border-radius: 3px;
  padding: 13px 34px;
  color: #fff;
  text-decoration: none;
  margin: 25px 0 14px;
  display: inline-block;
  transition: all 0.2s ease;
}
.dokan-pro-features .pricing-section .table-row .table-col .buy-btn:hover {
  box-shadow: 1px 10px 30px 0 rgba(215, 223, 254, 0.5);
}
.dokan-pro-features .pricing-section .table-row .table-col .buy-btn.starter {
  background-color: #00bcff;
}
.dokan-pro-features .pricing-section .table-row .table-col .buy-btn.professional {
  background-color: #2bc66d;
}
.dokan-pro-features .pricing-section .table-row .table-col .buy-btn.business {
  background-color: #795dff;
}
.dokan-pro-features .pricing-section .table-row .table-col .buy-btn.enterprise {
  background-color: #ff5956;
}
.dokan-pro-features .pricing-section .plan-name {
  display: inline-block;
  background: black;
  color: #fff;
  font-size: 14px;
  font-weight: 300;
  line-height: 1.4em;
  border-radius: 25px;
  padding: 5px 20px;
  min-width: 95px;
  box-sizing: border-box;
  margin: 26px 0 20px;
}
.dokan-pro-features .pricing-section .plan-name.starter {
  background-color: #00bcff;
}
.dokan-pro-features .pricing-section .plan-name.professional {
  background-color: #2bc66d;
}
.dokan-pro-features .pricing-section .plan-name.business {
  background-color: #795dff;
}
.dokan-pro-features .pricing-section .plan-name.enterprise {
  background-color: #ff5956;
}
.dokan-pro-features .pricing-section .price {
  margin-bottom: 15px;
}
.dokan-pro-features .pricing-section .price span {
  font-size: 14px;
  line-height: 1.2em;
  font-weight: 300;
  color: #b8bbbe;
  display: block;
}
.dokan-pro-features .pricing-section .price span:first-child {
  font-size: 28px;
  line-height: 1.5em;
  font-weight: 400;
  color: #23282d;
}
.dokan-pro-features .pricing-section .price span:first-child sup {
  font-size: 14px;
}
.dokan-pro-features .payment-section {
  display: flex;
  background: #fff;
  justify-content: space-between;
  padding: 20px 65px 15px;
  align-items: center;
}
@media (min-width: 1281px) {
.dokan-pro-features .payment-section {
    padding-left: 21%;
    padding-right: 21%;
}
}
.dokan-pro-features .payment-section .guarantee-section {
  display: flex;
  flex: 0 0 70%;
  align-content: center;
  align-items: center;
  position: relative;
}
.dokan-pro-features .payment-section .guarantee-section:after {
  content: "";
  position: absolute;
  right: -18px;
  top: 50%;
  transform: translateY(-50%);
  width: 5px;
  height: 38px;
  border-radius: 3px;
  background-color: #f1f1f1;
}
.dokan-pro-features .payment-section .guarantee-section .feature-thumb img {
  max-width: 100%;
  width: 160px;
}
.dokan-pro-features .payment-section .guarantee-section .guarantee-detail {
  padding-left: 25px;
}
.dokan-pro-features .payment-section .guarantee-section .guarantee-detail h2 {
  font-size: 24px;
  line-height: 1.5em;
  color: #2e2c2c;
  font-weight: 400;
  margin: 0;
}
.dokan-pro-features .payment-section .guarantee-section .guarantee-detail p {
  color: #66676e;
  font-size: 15px;
  line-height: 1.5em;
  font-weight: 300;
  margin: 5px 0 20px;
}
.dokan-pro-features .payment-section .guarantee-section .guarantee-detail a {
  color: #f2624d;
  font-size: 15px;
  line-height: 1.5em;
  text-decoration: none;
}
.dokan-pro-features .payment-section .guarantee-section .guarantee-detail a img {
  width: 18px;
  margin-right: 4px;
}
.dokan-pro-features .payment-section .payment-area h3 {
  font-size: 15px;
  font-weight: 300;
  color: #66676e;
  margin: 0px 0 17px;
}
.dokan-pro-features .payment-section .payment-area .option img {
  max-width: 100%;
}
.dokan-pro-features .testimonial-section {
  background-color: #f1f1f1;
  padding: 75px 0 70px;
  text-align: center;
}
.dokan-pro-features .testimonial-section .section-title {
  margin-bottom: 75px;
}
.dokan-pro-features .testimonial-section .testimonial-wrapper {
  width: 80%;
  text-align: center;
  margin: 0 auto;
}
@media only screen and (min-width: 1281px) {
.dokan-pro-features .testimonial-section .testimonial-wrapper {
    width: 55%;
}
}
.dokan-pro-features .testimonial-section .testimonial-wrapper .testimonial-box .profile-pic {
  width: 70px;
  height: 70px;
  border: 5px solid #fff;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0px 1px 16px 0px rgba(0, 0, 0, 0.16);
  margin: 0 auto -35px;
  z-index: 1;
  position: relative;
}
.dokan-pro-features .testimonial-section .testimonial-wrapper .testimonial-box .profile-pic img {
  border-radius: 50%;
  max-width: 100%;
}
.dokan-pro-features .testimonial-section .testimonial-wrapper .testimonial-box .content-detail {
  background-color: #fff;
  color: #000;
  border-radius: 5px;
  border: 1px solid #E0E9EC;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  padding: 50px 30px 30px;
  background-repeat: no-repeat;
  background-position: 40px 40px;
}
.dokan-pro-features .testimonial-section .testimonial-wrapper .testimonial-box .content-detail h4 {
  font-size: 18px;
  line-height: 1.444;
  color: #F2624D;
  font-weight: 400;
  margin-top: 0;
  margin-bottom: 5px;
}
.dokan-pro-features .testimonial-section .testimonial-wrapper .testimonial-box .content-detail span {
  font-size: 15px;
  line-height: 1.444;
  font-weight: 300;
  color: #66676e;
  margin-bottom: 20px;
  display: inline-block;
}
.dokan-pro-features .testimonial-section .testimonial-wrapper .testimonial-box .content-detail p {
  color: #2e2c2c;
  font-size: 14px;
  line-height: 1.9em;
  font-weight: 300;
}
.dokan-pro-features .testimonial-section .testimonial-wrapper .slick-dots {
  text-align: center;
  margin: 40px 0 25px;
}
.dokan-pro-features .testimonial-section .testimonial-wrapper .slick-dots li {
  position: relative;
  display: inline-block;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}
.dokan-pro-features .testimonial-section .testimonial-wrapper .slick-dots li.slick-active button:before {
  background-color: #3f4143;
}
.dokan-pro-features .testimonial-section .testimonial-wrapper .slick-dots li button {
  position: relative;
  font-size: 0;
  line-height: 0;
  display: block;
  width: 20px;
  height: 20px;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}
.dokan-pro-features .testimonial-section .testimonial-wrapper .slick-dots li button:before {
  position: absolute;
  top: 0;
  left: 0;
  width: 21px;
  height: 5px;
  content: '';
  text-align: center;
  background-color: #d6d6d6;
  border-radius: 30px;
}
.dokan-pro-features .testimonial-section p {
  color: #262626;
  font-size: 16px;
  line-height: 1.5em;
  font-weight: 300;
}
.dokan-pro-features .testimonial-section p a {
  color: #8263f7;
  text-decoration: none;
}

.dokan-help-page .section-wrapper {
  margin-top: 15px;
}
.dokan-help-page .section-wrapper .dokan-postbox {
  width: calc(33% - 2em);
  margin: 0 2% 15px 0;
  float: left;
}
.dokan-help-page .section-wrapper .dokan-postbox:nth-child(3n+1) {
  clear: both;
}
.dokan-help-page .section-wrapper .dokan-postbox .dashicons {
  color: #ccc;
}
.dokan-help-page .section-wrapper .dokan-postbox a {
  text-decoration: none;
}
.dokan-help-page .section-wrapper .dokan-postbox .inside,
.dokan-help-page .section-wrapper .dokan-postbox ul {
  margin-bottom: 0;
}
.dokan-help-page .loading {
  width: 100%;
  text-align: center;
  margin-top: 100px;
}

.fade-enter-active[data-v-38d3c45e],
.fade-leave-active[data-v-38d3c45e] {
  transition: opacity 0.3s ease;
}
.fade-enter[data-v-38d3c45e],
.fade-leave-to[data-v-38d3c45e] {
  opacity: 0;
}
.slide-enter-active[data-v-38d3c45e],
.slide-leave-active[data-v-38d3c45e] {
  transition-duration: 0.1s;
  transition-timing-function: linear;
}
.slide-enter-to[data-v-38d3c45e],
.slide-leave[data-v-38d3c45e] {
  max-height: 100px;
  overflow: hidden;
}
.slide-enter[data-v-38d3c45e],
.slide-leave-to[data-v-38d3c45e] {
  overflow: hidden;
  max-height: 0;
}
ul[data-v-38d3c45e] {
  cursor: pointer;
}
.dokan-help-page .section-wrapper h2[data-v-38d3c45e] {
  margin: 0;
  color: transparent;
}
.dokan-help-page .section-wrapper .dokan-notice[data-v-38d3c45e] {
  background: #efeaff;
  margin: -15px -20px 0;
  padding: 15px 15px 0;
}
.dokan-help-page .section-wrapper .change-log[data-v-38d3c45e] {
  background: #efeaff;
  margin: -15px -20px 0;
}
.dokan-help-page .section-wrapper .change-log.lite-change-log[data-v-38d3c45e] {
  height: 340px;
}
.dokan-help-page .section-wrapper .change-log.pro-change-log[data-v-38d3c45e] {
  height: 400px;
}
.dokan-help-page .section-wrapper .change-log h3[data-v-38d3c45e] {
  color: #000000;
  font-size: 30px;
  text-align: center;
  padding: 45px 0 0;
  font-weight: 800;
  font-family: "SF Pro Text", sans-serif;
  margin: 0 0 28px;
}
.dokan-help-page .section-wrapper .change-log .switch-button-wrap[data-v-38d3c45e] {
  width: 147px;
  height: 33px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 auto;
  background: #ffffff;
  border-radius: 53px;
  position: relative;
  border: 1px solid #e2e2e2;
}
.dokan-help-page .section-wrapper .change-log .switch-button-wrap .switch-button[data-v-38d3c45e] {
  width: 48%;
  height: 100%;
  background: none;
  border-radius: 27px;
  border: none;
  color: #5C626A;
  display: inline-block;
  position: relative;
  transition: all 0.2s ease;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  font-family: "SF Pro Text", sans-serif;
}
.dokan-help-page .section-wrapper .change-log .switch-button-wrap .active[data-v-38d3c45e] {
  background: #7047EB;
  border-radius: 30px;
  position: absolute;
  top: 0;
  width: 50%;
  height: 100%;
  transition: all 0.2s ease-out;
}
.dokan-help-page .section-wrapper .change-log .switch-button-wrap .lite[data-v-38d3c45e] {
  text-transform: capitalize;
}
.dokan-help-page .section-wrapper .change-log .switch-button-wrap .pro[data-v-38d3c45e] {
  text-transform: uppercase;
}
.dokan-help-page .section-wrapper .change-log .switch-button-wrap .active-case[data-v-38d3c45e] {
  color: #ffffff;
}
.dokan-help-page .section-wrapper .change-log .jump-version[data-v-38d3c45e] {
  width: 178px;
  margin: 24px auto 0;
  position: relative;
}
.dokan-help-page .section-wrapper .change-log .jump-version p[data-v-38d3c45e] {
  color: #000;
  font-size: 13px;
  text-align: center;
  cursor: pointer;
  font-weight: 500;
  font-family: "SF Pro Text", sans-serif;
}
.dokan-help-page .section-wrapper .change-log .jump-version .dashicons[data-v-38d3c45e] {
  font-size: 16px;
  line-height: 1.4;
  transition: all 0.2s ease;
}
.dokan-help-page .section-wrapper .change-log .jump-version:hover .dashicons[data-v-38d3c45e] {
  transform: rotate(-180deg);
}
.dokan-help-page .section-wrapper .change-log .jump-version .version-menu[data-v-38d3c45e] {
  position: absolute;
  top: 50px;
  left: 0;
  width: 100%;
  z-index: 1;
  background: #fff;
  border: 1px solid #dddddd;
  padding: 20px 10px 20px 20px;
  box-sizing: border-box;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.09);
}
.dokan-help-page .section-wrapper .change-log .jump-version .version-menu[data-v-38d3c45e]:before {
  content: "";
  position: absolute;
  border: 11px solid transparent;
  border-bottom-color: white;
  margin-left: -10px;
  top: -19px;
  right: 27px;
  z-index: 1;
}
.dokan-help-page .section-wrapper .change-log .jump-version .version-menu .version-dropdown[data-v-38d3c45e] {
  max-height: 300px;
  text-align: left;
  background: #ffffff;
  overflow-y: auto;
}
.dokan-help-page .section-wrapper .change-log .jump-version .version-menu .version-dropdown ul[data-v-38d3c45e] {
  cursor: context-menu;
}
.dokan-help-page .section-wrapper .change-log .jump-version .version-menu .version-dropdown ul li[data-v-38d3c45e] {
  margin-bottom: 25px;
  color: #000000;
  font-size: 14px;
  font-weight: 400;
  font-family: "SF Pro Text", sans-serif;
  transition: all 0.2s linear;
  cursor: pointer;
}
.dokan-help-page .section-wrapper .change-log .jump-version .version-menu .version-dropdown ul li[data-v-38d3c45e]:hover {
  color: #7047EB;
}
.dokan-help-page .section-wrapper .change-log .jump-version .version-menu .version-dropdown ul li[data-v-38d3c45e]:last-child {
  margin-bottom: 0;
}
.dokan-help-page .section-wrapper .change-log .jump-version .version-menu .version-dropdown ul li.current[data-v-38d3c45e] {
  color: #7047EB;
  font-weight: bold;
  font-family: "SF Pro Text", sans-serif;
}
.dokan-help-page .section-wrapper .change-log .jump-version .version-menu .version-dropdown ul li span[data-v-38d3c45e] {
  display: block;
  font-size: 12px;
  color: #758598;
  font-weight: 400;
  font-family: "SF Pro Text", sans-serif;
}
.dokan-help-page .section-wrapper .change-log .jump-version .version-menu .version-dropdown[data-v-38d3c45e]::-webkit-scrollbar {
  width: 4px;
}
.dokan-help-page .section-wrapper .change-log .jump-version .version-menu .version-dropdown[data-v-38d3c45e]::-webkit-scrollbar-track {
  background: #f5f5f5;
}
.dokan-help-page .section-wrapper .change-log .jump-version .version-menu .version-dropdown[data-v-38d3c45e]::-webkit-scrollbar-thumb {
  background: #878787;
}
.dokan-help-page .section-wrapper .change-log .jump-version .version-menu .version-dropdown[data-v-38d3c45e]::-webkit-scrollbar-thumb:hover {
  background: #575757;
}
.dokan-help-page .section-wrapper .change-log .jump-version:hover .version-menu[data-v-38d3c45e] {
  top: 30px;
  opacity: 1;
  visibility: visible;
}
.dokan-help-page .section-wrapper .version-list .version .card-version[data-v-38d3c45e] {
  background: #ffffff;
  border: 1px solid #e2e2e2;
  border-radius: 3px;
  padding: 25px;
  box-sizing: border-box;
}
.dokan-help-page .section-wrapper .version-list .version .card-version div[data-v-38d3c45e] {
  overflow: hidden;
}
.dokan-help-page .section-wrapper .version-list .version .card-version div .feature-list[data-v-38d3c45e] {
  margin-bottom: 40px;
}
.dokan-help-page .section-wrapper .version-list .version .card-version div .feature-list[data-v-38d3c45e]:last-child {
  margin-bottom: 0;
}
.dokan-help-page .section-wrapper .version-list .version .card-version div .feature-list .feature-badge[data-v-38d3c45e] {
  color: #ffffff;
  font-size: 15px;
  font-weight: 600;
  padding: 6px 14px;
  border-radius: 3px;
  display: inline-block;
  font-family: "SF Pro Text", sans-serif;
}
.dokan-help-page .section-wrapper .version-list .version .card-version div .feature-list .badge-green[data-v-38d3c45e] {
  background: #00B728;
}
.dokan-help-page .section-wrapper .version-list .version .card-version div .feature-list .badge-blue[data-v-38d3c45e] {
  background: #028AFB;
}
.dokan-help-page .section-wrapper .version-list .version .card-version div .feature-list .badge-red[data-v-38d3c45e] {
  background: #F83838;
}
.dokan-help-page .section-wrapper .version-list .version .card-version div .feature-list .feature[data-v-38d3c45e] {
  margin: 11px 0;
}
.dokan-help-page .section-wrapper .version-list .version .card-version div .feature-list .feature[data-v-38d3c45e]:last-child {
  margin-bottom: 0;
}
.dokan-help-page .section-wrapper .version-list .version .card-version div .feature-list .feature h5[data-v-38d3c45e] {
  color: #000000;
  margin: 0;
  font-size: 14px;
  font-weight: bold;
  font-family: "SF Pro Text", sans-serif;
}
.dokan-help-page .section-wrapper .version-list .version .card-version div .feature-list .feature div[data-v-38d3c45e] {
  color: #000000;
  font-size: 14px;
  font-weight: 400;
  opacity: 0.8;
  font-family: "SF Pro Text", sans-serif;
}
.dokan-help-page .section-wrapper .version-list .version .card-version div .feature-list .feature img[data-v-38d3c45e] {
  width: 100%;
  height: auto;
  margin-top: 10px;
}
.dokan-help-page .section-wrapper .version-list .version .card-version div .feature-list .feature ul[data-v-38d3c45e] {
  list-style: disc outside;
  opacity: 0.7;
  font-size: 14px;
  font-weight: 400;
  margin-left: 18px;
}
.dokan-help-page .section-wrapper .version-list .version .card-version .continue-reading[data-v-38d3c45e] {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
}
.dokan-help-page .section-wrapper .version-list .version .card-version .continue-reading a[data-v-38d3c45e] {
  font-size: 13px;
  font-weight: normal;
  text-decoration: none;
  padding: 6px 14px;
  display: inline-block;
  color: #000000;
  background: #ffffff;
  border-radius: 3px;
  border: 1px solid #E2E2E2;
  font-family: "SF Pro Text", sans-serif;
}
.dokan-help-page .section-wrapper .version-list .version .card-version .continue-reading a[data-v-38d3c45e]:focus {
  box-shadow: none;
}
.dokan-help-page .section-wrapper .version-list .version .version-number h4[data-v-38d3c45e] {
  font-weight: 700;
  font-size: 23px;
  color: #000000;
  margin-bottom: 7px;
  font-family: "SF Pro Text", sans-serif;
}
.dokan-help-page .section-wrapper .version-list .version .version-number p[data-v-38d3c45e] {
  font-weight: 400;
  font-size: 13px;
  color: #5C626A;
  font-family: "SF Pro Text", sans-serif;
}
.dokan-help-page .section-wrapper .version-list .version .version-number label[data-v-38d3c45e] {
  font-size: 12px;
  color: #fff;
  background: #8e44ad;
  border-radius: 53px;
  margin-left: 5px;
  padding: 2px 12px;
  font-weight: 400;
  font-family: "SF Pro Text", sans-serif;
}
@media only screen and (min-width: 1200px) {
.dokan-help-page .section-wrapper .version-list .version[data-v-38d3c45e] {
    display: flex;
    width: 900px;
    margin: 0 auto;
    justify-content: space-between;
}
.dokan-help-page .section-wrapper .version-list .version .card-version[data-v-38d3c45e] {
    width: 700px;
}
.dokan-help-page .section-wrapper .version-list .latest-version[data-v-38d3c45e] {
    margin-top: -200px;
}
.dokan-help-page .section-wrapper .version-list .old-version[data-v-38d3c45e] {
    margin-top: 25px;
}
}
@media screen and (min-width: 992px) and (max-width: 1199px) {
.dokan-help-page .section-wrapper .version-list .version[data-v-38d3c45e] {
    display: flex;
    width: 720px;
    margin: 0 auto;
    justify-content: space-between;
}
.dokan-help-page .section-wrapper .version-list .version .card-version[data-v-38d3c45e] {
    width: 520px;
}
.dokan-help-page .section-wrapper .version-list .latest-version[data-v-38d3c45e] {
    margin-top: -200px;
}
.dokan-help-page .section-wrapper .version-list .old-version[data-v-38d3c45e] {
    margin-top: 20px;
}
}
@media only screen and (max-width: 991px) {
.dokan-help-page .section-wrapper .dokan-notice[data-v-38d3c45e] {
    background: #f7f8fa;
    margin: -15px -10px 0;
    padding: 15px 15px 0;
}
.dokan-help-page .section-wrapper .change-log[data-v-38d3c45e] {
    background: #f7f8fa;
    margin: -15px -10px 0;
}
.dokan-help-page .section-wrapper .change-log.lite-change-log[data-v-38d3c45e] {
    height: 220px;
}
.dokan-help-page .section-wrapper .change-log.pro-change-log[data-v-38d3c45e] {
    height: 280px;
}
.dokan-help-page .section-wrapper .version-list .version .card-version[data-v-38d3c45e] {
    margin: 0 -10px;
    border: 0;
    box-shadow: none;
    border-radius: 0;
}
.dokan-help-page .section-wrapper .version-list .version .card-version .continue-reading[data-v-38d3c45e] {
    justify-content: start;
}
.dokan-help-page .section-wrapper .version-list .latest-version[data-v-38d3c45e] {
    margin-top: -112px;
}
.dokan-help-page .section-wrapper .version-list .latest-version .version-number[data-v-38d3c45e] {
    padding-bottom: 15px;
    text-align: center;
}
.dokan-help-page .section-wrapper .version-list .old-version[data-v-38d3c45e] {
    margin-top: 15px;
}
.dokan-help-page .section-wrapper .version-list .old-version .version-number[data-v-38d3c45e] {
    background: #fff;
    padding: 25px 25px 0 25px;
    margin: 0 -10px;
    box-sizing: border-box;
}
.dokan-help-page .section-wrapper .version-list .old-version h4[data-v-38d3c45e] {
    margin-top: 0;
}
.dokan-help-page .section-wrapper .version-list .old-version p[data-v-38d3c45e] {
    margin-bottom: 0;
}
}
.dokan-help-page .scroll-to-top[data-v-38d3c45e] {
  width: 40px;
  height: 40px;
  color: #ffffff;
  background: #7047EB;
  border: 0;
  position: fixed;
  right: 10px;
  bottom: 35px;
  z-index: 1;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.09);
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
}
.dokan-help-page .loading[data-v-38d3c45e] {
  width: 100%;
  text-align: center;
  margin-top: 100px;
}


.vc-editable-input {
  position: relative;
}
.vc-input__input {
  padding: 0;
  border: 0;
  outline: none;
}
.vc-input__label {
  text-transform: capitalize;
}


.vc-saturation,
.vc-saturation--white,
.vc-saturation--black {
  cursor: pointer;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.vc-saturation--white {
  background: linear-gradient(to right, #fff, rgba(255,255,255,0));
}
.vc-saturation--black {
  background: linear-gradient(to top, #000, rgba(0,0,0,0));
}
.vc-saturation-pointer {
  cursor: pointer;
  position: absolute;
}
.vc-saturation-circle {
  cursor: pointer;
  width: 4px;
  height: 4px;
  box-shadow: 0 0 0 1.6px #fff, inset 0 0 1px 1px rgba(0,0,0,.3), 0 0 1px 2px rgba(0,0,0,.4);
  border-radius: 50%;
  transform: translate(-2px, -2px);
}


.vc-hue {
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  border-radius: 2px;
}
.vc-hue--horizontal {
  background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);
}
.vc-hue--vertical {
  background: linear-gradient(to top, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);
}
.vc-hue-container {
  cursor: pointer;
  margin: 0 2px;
  position: relative;
  height: 100%;
}
.vc-hue-pointer {
  z-index: 2;
  position: absolute;
}
.vc-hue-picker {
  cursor: pointer;
  margin-top: 1px;
  width: 4px;
  border-radius: 1px;
  height: 8px;
  box-shadow: 0 0 2px rgba(0, 0, 0, .6);
  background: #fff;
  transform: translateX(-2px) ;
}


.vc-checkerboard {
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  background-size: contain;
}


.vc-alpha {
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
}
.vc-alpha-checkboard-wrap {
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  overflow: hidden;
}
.vc-alpha-gradient {
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
}
.vc-alpha-container {
  cursor: pointer;
  position: relative;
  z-index: 2;
  height: 100%;
  margin: 0 3px;
}
.vc-alpha-pointer {
  z-index: 2;
  position: absolute;
}
.vc-alpha-picker {
  cursor: pointer;
  width: 4px;
  border-radius: 1px;
  height: 8px;
  box-shadow: 0 0 2px rgba(0, 0, 0, .6);
  background: #fff;
  margin-top: 1px;
  transform: translateX(-2px);
}


.vc-sketch {
  position: relative;
  width: 200px;
  padding: 10px 10px 0;
  box-sizing: initial;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, .15), 0 8px 16px rgba(0, 0, 0, .15);
}
.vc-sketch-saturation-wrap {
  width: 100%;
  padding-bottom: 75%;
  position: relative;
  overflow: hidden;
}
.vc-sketch-controls {
  display: flex;
}
.vc-sketch-sliders {
  padding: 4px 0;
  flex: 1;
}
.vc-sketch-sliders .vc-hue,
.vc-sketch-sliders .vc-alpha-gradient {
  border-radius: 2px;
}
.vc-sketch-hue-wrap {
  position: relative;
  height: 10px;
}
.vc-sketch-alpha-wrap {
  position: relative;
  height: 10px;
  margin-top: 4px;
  overflow: hidden;
}
.vc-sketch-color-wrap {
  width: 24px;
  height: 24px;
  position: relative;
  margin-top: 4px;
  margin-left: 4px;
  border-radius: 3px;
}
.vc-sketch-active-color {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 2px;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, .15), inset 0 0 4px rgba(0, 0, 0, .25);
  z-index: 2;
}
.vc-sketch-color-wrap .vc-checkerboard {
  background-size: auto;
}
.vc-sketch-field {
  display: flex;
  padding-top: 4px;
}
.vc-sketch-field .vc-input__input {
  width: 90%;
  padding: 4px 0 3px 10%;
  border: none;
  box-shadow: inset 0 0 0 1px #ccc;
  font-size: 10px;
}
.vc-sketch-field .vc-input__label {
  display: block;
  text-align: center;
  font-size: 11px;
  color: #222;
  padding-top: 3px;
  padding-bottom: 4px;
  text-transform: capitalize;
}
.vc-sketch-field--single {
  flex: 1;
  padding-left: 6px;
}
.vc-sketch-field--double {
  flex: 2;
}
.vc-sketch-presets {
  margin-right: -10px;
  margin-left: -10px;
  padding-left: 10px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}
.vc-sketch-presets-color {
  border-radius: 3px;
  overflow: hidden;
  position: relative;
  display: inline-block;
  margin: 0 10px 10px 0;
  vertical-align: top;
  cursor: pointer;
  width: 16px;
  height: 16px;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, .15);
}
.vc-sketch-presets-color .vc-checkerboard {
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, .15);
  border-radius: 3px;
}
.vc-sketch__disable-alpha .vc-sketch-color-wrap {
  height: 10px;
}

.color-picker-container[data-v-5506900a] {
  position: relative;
}
.color-picker-container .color-picker-button[data-v-5506900a] {
  border: 1px solid #E2E2E2;
  padding: 3px 10px;
  display: flex;
  background: #FFF;
  box-sizing: unset;
  align-items: center;
  margin-left: auto;
  border-radius: 3px;
}
.color-picker-container .color-picker-button .color[data-v-5506900a] {
  width: 23px;
  height: 23px;
  border: 0.3px solid rgba(149, 165, 166, 0.5);
  box-sizing: border-box;
  margin-right: 3px;
  border-radius: 23px;
}
.color-picker-container .color-picker-button span[data-v-5506900a] {
  color: #95A5A6;
  display: block;
  padding: 0;
  font-size: 12px;
  text-align: center;
  line-height: 22px;
  margin-right: -5px;
}
.color-picker-container .button-group[data-v-5506900a] {
  top: 260px;
  right: 11px;
  z-index: 1;
  position: absolute;
}
.color-picker-container .button-group .button-small[data-v-5506900a] {
  color: #fff;
  border: 0;
  padding: 15px;
  font-size: 16px;
  font-weight: bold;
  border-radius: 5px 0 0 5px;
  background-color: #1A9ED4;
}
.color-picker-container .button-group .button-small[data-v-5506900a]:before {
  position: absolute;
  transform: translate(-50%, -50%);
}
.color-picker-container .button-group .button-small[data-v-5506900a]:last-child {
  border-radius: 0 5px 5px 0;
}
.color-picker-container .button-group .button-small[data-v-5506900a]:last-child:after {
  top: 20%;
  left: 50%;
  width: 1px;
  height: 60%;
  content: '';
  position: absolute;
  transform: translateX(50%);
  background: #fff;
}
.color-picker-container .button-group .button-small[data-v-5506900a]:hover {
  background-color: #1A9ED4;
}
.color-picker-container .vc-sketch[data-v-5506900a] {
  top: 120%;
  right: 0;
  z-index: 1;
  position: absolute;
  padding-bottom: 40px;
}
.color-picker-container .hex-input[data-v-5506900a] {
  top: 260px;
  width: 75px;
  right: 132px;
  padding: 3px 10px 4px;
  z-index: 1;
  position: absolute;
  font-size: 12px;
  min-height: 30px !important;
  box-shadow: none !important;
  font-family: monospace;
  line-height: 1.4;
  vertical-align: top;
}

.switch {
  position: relative;
  display: inline-block;
  width: 42px;
  height: 20px;
}
.switch input {
  display: none;
}
.switch input.enabled + .slider {
  background-color: var(--dokan-toggle-active-color);
}
.switch input.enabled + .slider:before {
  -webkit-transform: translateX(22px);
  -ms-transform: translateX(22px);
  transform: translateX(22px);
}
.switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--dokan-toggle-inactive-color);
  -webkit-transition: 0.2s;
  transition: 0.2s;
}
.switch .slider:before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 3px;
  bottom: 3px;
  background-color: var(--dokan-toggle-color);
  -webkit-transition: 0.2s;
  transition: 0.2s;
}
.switch .slider.round {
  border-radius: 34px;
}
.switch .slider.round:before {
  border-radius: 50%;
}

.secret-box-wraper[data-v-2a9a6c5c] {
  display: flex;
  flex-direction: row-reverse;
}
.secret-box-wraper .secret-input-box[data-v-2a9a6c5c] {
  position: relative;
  display: flex;
  width: 25em;
}
.secret-box-wraper .secret-input-box div button[data-v-2a9a6c5c] {
  cursor: pointer;
  height: 20px;
  min-height: 32px;
  min-width: 32px;
  border: 1px solid #f3f4f6;
  box-shadow: 0px 3.82974px 3.82974px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: white;
  color: #686666;
}
.secret-box-wraper .secret-input-box .secret-input[data-v-2a9a6c5c] {
  width: 100%;
}
.secret-box-wraper .secret-input-box .secret-input.blurry-input[data-v-2a9a6c5c] {
  color: transparent;
  text-shadow: 0 0 7px #333;
}
.secret-box-wraper .secret-input-box .secret-input-placeholder[data-v-2a9a6c5c] {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #686666;
}
@media only screen and (max-width: 768px) {
.secret-box-wraper .secret-input-box[data-v-2a9a6c5c] {
    max-width: 125px !important;
}
}

.wm-box-container[data-v-7e4e2d4c] {
  display: flex;
  flex-direction: column;
}
.wm-box-container .wm-methods-box-container[data-v-7e4e2d4c] {
  margin-top: 15px;
}
.wm-box-container .wm-methods-box-container .wm-methods-box[data-v-7e4e2d4c] {
  border-bottom: 1px solid #f3f4f6;
  padding: 0 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.wm-box-container .wm-methods-box-container .wm-methods-box[data-v-7e4e2d4c]:last-child {
  border-bottom: 0;
}
.wm-box-container .wm-methods-box-container .wm-methods-box .wm-method[data-v-7e4e2d4c] {
  display: flex;
  align-items: center;
}
.wm-box-container .wm-methods-box-container .wm-methods-box .wm-charges[data-v-7e4e2d4c] {
  display: flex;
  align-items: center;
}
.wm-box-container .wm-methods-box-container .wm-methods-box .wm-charges .wm-automated[data-v-7e4e2d4c] {
  border: 1px solid #dbdbdb;
  color: #838181;
  padding: 5px 8px;
  border-radius: 12px;
  background: #f5f5f6;
}
@media only screen and (max-width: 782px) {
.wm-box-container .wm-methods-box-container .wm-methods-box[data-v-7e4e2d4c] {
    flex-direction: column;
    justify-content: start;
    align-items: start;
}
.wm-box-container .wm-methods-box-container .wm-methods-box .wm-charges[data-v-7e4e2d4c] {
    margin-left: -20px;
    margin-bottom: 20px;
}
}

.dokan-radio-fields[data-v-49a35897] {
  display: flex;
  flex: 2;
  align-self: center;
}
.dokan-radio-fields label[data-v-49a35897] {
  border: 1px solid #b0a7a7;
  padding: 10px 15px;
  display: inline-block;
  overflow: hidden;
  font-size: 12px;
  font-family: Roboto, sans-serif;
  font-weight: 400;
  line-height: 14px;
  border-right: 0;
}
.dokan-radio-fields label .dashicons-yes[data-v-49a35897] {
  color: #fff;
  width: 15px;
  height: 15px;
  margin: -1px 3px 0 0;
  cursor: pointer;
  display: none;
  font-size: 15px;
  background: #1aa0f7;
  padding-top: 0;
  border-radius: 50%;
}
.dokan-radio-fields label input[type=radio][data-v-49a35897] {
  display: none;
}
.dokan-radio-fields label[data-v-49a35897]:hover {
  color: rgba(3, 58, 163, 0.85);
  background: rgba(182, 206, 254, 0.38);
  box-sizing: border-box;
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.1);
}
.dokan-radio-fields label.checked[data-v-49a35897] {
  color: rgba(3, 58, 163, 0.85);
  border: 1px solid rgba(3, 58, 163, 0.81) !important;
  background: rgba(182, 206, 254, 0.38);
  box-sizing: border-box;
}
.dokan-radio-fields label.checked .dashicons-yes[data-v-49a35897] {
  display: inline-block;
}
.dokan-radio-fields label[data-v-49a35897]:first-child {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}
.dokan-radio-fields label[data-v-49a35897]:last-child {
  border-right: 1px solid #b0a7a7;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}

span.repeatable-item-description {
  color: #999;
  font-size: 11px;
  font-style: italic;
}
ul.dokan-settings-repeatable-list {
  display: flex;
  padding: 20px 0 0 20px;
  flex-wrap: wrap;
  text-align: right;
  justify-content: right;
}
ul.dokan-settings-repeatable-list li {
  color: rgba(0, 0, 0, 0.87);
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 5px 12px;
  display: flex;
  font-size: 13px;
  box-sizing: border-box;
  background: rgba(182, 206, 254, 0.38);
  margin-top: 6px;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.1);
  align-items: center;
  margin-left: 12px;
  font-family: 'Roboto', sans-serif;
  line-height: 1;
  border-radius: 8px;
  justify-content: center;
}
ul.dokan-settings-repeatable-list li span.remove-item {
  color: #fff;
  width: 15px;
  margin: 0;
  height: 15px;
  cursor: pointer;
  font-size: 15px;
  background: #1aa0f7;
  padding-top: 0;
  margin-left: 5px;
  border-radius: 50%;
}
.dokan-repetable-add-item-btn {
  font-size: 16px !important;
  font-weight: bold !important;
  height: 25px !important;
  line-height: 22px !important;
}
.percent_fee,
.fixed_fee {
  display: inline-block;
}
.percent_fee input,
.fixed_fee input {
  width: 60px;
}
.additional_fee .description {
  margin-left: 10px;
  margin-top: -10px;
}
.dokan-error {
  color: red;
  margin-top: 0.5em;
  font-style: italic;
  margin-bottom: 0;
}
.dokan-input-validation-error {
  border-color: red !important;
}
.dokan-error.combine-commission {
  margin-left: 10px;
}
.dokan-settings-sub-section {
  padding: 20px;
  border: 1px solid #f3f4f6;
  border-bottom: 0;
  background: #f9fafb;
}
.dokan-settings-sub-section .sub-section-title {
  margin: 0;
  font-size: 14px;
  font-family: Roboto, sans-serif;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 8px;
}
.dokan-settings-sub-section .sub-section-description {
  margin: 0;
  font-size: 13px;
  font-weight: 300;
  line-height: 21px;
  font-family: Roboto, sans-serif;
  color: #6B7280;
}
.dokan-settings-sub-section .sub-section-description .learn-more-btn {
  cursor: pointer;
  text-decoration: none;
}
.field_contents.data_clear {
  background-color: #FFFBF3;
}
.field_contents.data_clear .field_desc,
.field_contents.data_clear .fa-exclamation-triangle {
  color: #E67E22 !important;
}
.field_contents {
  border: 1px solid #f3f4f6;
  padding: 15px 20px 15px 20px;
  border-top: 0;
  background: #fff;
}
.field_contents fieldset {
  display: flex;
  justify-content: space-between;
}
.field_contents fieldset .field_data {
  flex: 2;
}
.field_contents fieldset .field_data .field_heading {
  color: #111827;
  margin: 0;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 1.25;
  font-family: 'Roboto', sans-serif;
}
.field_contents fieldset .field_data .field_heading span i {
  margin: -3px 0 0 5px;
}
.field_contents fieldset .field_data .field_heading span .tooltip {
  font-size: 14px;
}
.field_contents fieldset .field_data .field_desc {
  color: #6B7280;
  margin: 0;
  margin-top: 5px;
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: 1.2;
  font-family: 'Roboto', sans-serif;
}
.field_contents fieldset .field_data .field_desc a {
  display: inline-block;
  text-decoration: underline;
}
.field_contents fieldset .field_data .field_desc a:hover {
  box-shadow: 0 0 0 1px transparent;
}
.field_contents fieldset .field_data .field_desc a:active {
  box-shadow: 0 0 0 1px transparent;
}
.field_contents fieldset .field_data .field_desc a:focus {
  box-shadow: 0 0 0 1px transparent;
}
.field_contents fieldset .social-switch-wraper {
  display: flex;
  align-items: center;
}
.field_contents .combine_fields {
  display: flex;
  justify-content: right;
}
.field_contents .combine_fields .percent_fee {
  padding-right: 10px;
}
.field_contents .combine_fields .fixed_fee input,
.field_contents .combine_fields .percent_fee input {
  width: 100px;
}
.field_contents .multicheck_fields > div {
  display: flex;
  align-items: center;
  justify-content: right;
}
.field_contents .multicheck_fields > div label {
  color: #000;
  cursor: inherit;
  margin: 9px 0 9px 15px;
  display: inline-block;
  font-size: 12px;
  font-style: normal;
  line-height: 14px;
  font-family: 'Roboto', sans-serif;
  border-radius: 20px !important;
  border-radius: 8px;
}
.field_contents .editor_field {
  margin-top: 20px;
}
.field_contents .radio_fields label {
  border: 0.882967px solid #f3f4f6;
  padding: 10px 15px;
  display: inline-block;
  overflow: hidden;
  font-size: 12px;
  box-shadow: 0px 3.53187px 3.53187px rgba(0, 0, 0, 0.1);
  font-family: 'Roboto', sans-serif;
  font-weight: 400;
  line-height: 14px;
  border-right: 0;
}
.field_contents .radio_fields label:first-child {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}
.field_contents .radio_fields label:last-child {
  border-right: 0.882967px solid #f3f4f6;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}
.field_contents .radio_fields label:hover {
  color: rgba(3, 58, 163, 0.85);
  background: rgba(182, 206, 254, 0.38);
  box-sizing: border-box;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.1);
  border-color: rgba(3, 58, 163, 0.41);
}
.field_contents .radio_fields .checked {
  color: rgba(3, 58, 163, 0.85);
  border: 1px solid rgba(3, 58, 163, 0.21);
  background: rgba(182, 206, 254, 0.38);
  box-sizing: border-box;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.1);
}
.field_contents .repeatable_fields {
  display: flex;
  align-items: center;
  justify-content: right;
}
.field_contents .repeatable_fields .dokan-repetable-add-item-btn {
  color: #fff;
  width: 25px;
  border: 0;
  padding: 0;
  position: relative;
  background: #2196F3;
  min-height: 25px;
  margin-left: 8px;
  border-radius: 50%;
}
.field_contents .repeatable_fields .dokan-repetable-add-item-btn .dashicons-plus-alt2 {
  top: 50%;
  left: 50%;
  position: absolute;
  transform: translate(-50%, -50%);
  font-size: 18px;
}
.field_contents .dokan-setting-warning {
  padding: 10px 10px 10px 0;
}
.field_contents .dokan-setting-warning .dokan-setting-warning-label {
  color: #d63638;
  font-weight: bold;
  margin-right: 10px;
}
.field_contents .dokan-setting-warning .dokan-setting-warning-label span {
  margin-top: 6px !important;
}
.field_contents .dokan-setting-warning a.dokan-setting-warning-link {
  display: block;
  margin-top: 8px;
  text-decoration: none;
}
.field_contents .dokan-setting-warning a.dokan-setting-warning-link:hover,
.field_contents .dokan-setting-warning a.dokan-setting-warning-link:active,
.field_contents .dokan-setting-warning a.dokan-setting-warning-link:focus {
  outline: none;
  box-shadow: none;
}
.field_contents .dokan-setting-warning a.dokan-setting-warning-link i.dashicons {
  font-size: 18px;
}
.field_contents .dokan-setting-warning .dashicons {
  margin: 0px;
  padding: 0px;
}
.field_contents .add_files {
  display: flex;
  align-items: center;
  justify-content: right;
}
.field_contents .field {
  flex: 2;
  align-self: center;
  text-align: right;
}
.field_contents .field .switch {
  display: inline-block;
}
.field_contents .field input[type='radio'],
.field_contents .field input[type='checkbox'] {
  display: none;
}
.field_contents .field select,
.field_contents .field textarea,
.field_contents .field input[type='text'],
.field_contents .field input[type='number'],
.field_contents .field input[type='button'] {
  border: 0.957434px solid #E9E9E9;
  min-height: 32px;
  box-shadow: 0px 3.82974px 3.82974px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
}
.field_contents .field select,
.field_contents .field textarea {
  width: 100%;
}
.field_contents .field .small {
  max-width: 35% !important;
}
.field_contents .field .medium {
  max-width: 70% !important;
}
.field_contents .field .large {
  max-width: 100% !important;
}
.field_contents .field label.checked {
  color: rgba(3, 58, 163, 0.85);
  border: 1px solid rgba(3, 58, 163, 0.41);
  background: rgba(182, 206, 254, 0.38);
  box-sizing: border-box;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.1);
}
.field_contents .field label.checked .dashicons-yes {
  display: inline-block;
}
.field_contents .field .dashicons-yes {
  color: #fff;
  width: 15px;
  height: 15px;
  margin: -1px 3px 0 0;
  cursor: pointer;
  display: none;
  font-size: 15px;
  background: #1aa0f7;
  padding-top: 0;
  border-radius: 50%;
}
.field_contents .scl_fields_disable {
  filter: grayscale(1);
}
.field_contents .scl_fields {
  margin: 15px 0 4px 0px;
  border: 0.82px solid #E5E5E5;
  padding: 10px 25px;
  background: rgba(220, 232, 254, 0.38);
  border-radius: 6.56px;
}
.field_contents .scl_fields .scl_header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.field_contents .scl_fields .scl_header .scl_contents {
  display: flex;
  align-items: center;
}
.field_contents .scl_fields .scl_header .scl_contents .scl_icon {
  flex: 1.3;
  text-align: left;
  align-self: center;
}
.field_contents .scl_fields .scl_header .scl_contents .scl_icon img {
  width: 48px;
  height: 48px;
}
.field_contents .scl_fields .scl_header .scl_contents .scl_icon span {
  font-size: 50px;
}
.field_contents .scl_fields .scl_header .scl_contents .scl_desc {
  flex: 6;
  color: #000000;
  font-size: 14px;
  text-align: left;
  font-style: normal;
  font-weight: 300;
  line-height: 20px;
  font-family: 'Roboto', sans-serif;
}
.field_contents .scl_fields .scl_header .expand_btn {
  flex: 2;
}
.field_contents .scl_fields .scl_header .expand_btn span {
  color: #fff;
  width: 30px;
  cursor: pointer;
  margin: 0;
  border: 0;
  padding: 0;
  position: relative;
  font-size: 20px;
  background: #2196f3;
  min-height: 30px;
  border-radius: 50%;
}
.field_contents .scl_fields .scl_header .expand_btn span:before {
  top: 50%;
  left: 50%;
  position: absolute;
  transform: translate(-50%, -50%);
}
.field_contents .scl_fields .scl_header .expand_btn .active-social-expend-btn {
  background: #4CAF4F;
}
.field_contents .scl_fields .scl_info {
  background: #fff;
}
.field_contents .scl_fields .scl_info .scl_text,
.field_contents .scl_fields .scl_info .scl_html {
  border: 1px solid #f3f4f6;
  display: flex;
  padding: 10px 30px 15px 27px;
  border-top: 0;
  background: rgba(244, 246, 250, 0.17);
  justify-content: space-between;
}
.field_contents .scl_fields .scl_info .scl_text fieldset,
.field_contents .scl_fields .scl_info .scl_html fieldset {
  width: 100%;
}
.field_contents .scl_fields .scl_info .scl_text fieldset .html_contents,
.field_contents .scl_fields .scl_info .scl_html fieldset .html_contents {
  width: 50%;
  text-align: left;
}
.field_contents .scl_fields .scl_info .scl_text fieldset .html_contents .field_heading,
.field_contents .scl_fields .scl_info .scl_html fieldset .html_contents .field_heading {
  color: #000;
  margin: 0;
  font-size: 15px;
  font-style: normal;
  font-weight: 600;
  line-height: 30px;
  font-family: Roboto, sans-serif;
}
.field_contents .scl_fields .scl_info .scl_text fieldset .html_contents .field_heading span i,
.field_contents .scl_fields .scl_info .scl_html fieldset .html_contents .field_heading span i {
  margin: 2.5px 0 0 5px;
}
.field_contents .scl_fields .scl_info .scl_text fieldset .html_contents .field_heading span .tooltip,
.field_contents .scl_fields .scl_info .scl_html fieldset .html_contents .field_heading span .tooltip {
  font-size: 14px;
}
.field_contents .scl_fields .scl_info .scl_text fieldset .html_contents .field_desc,
.field_contents .scl_fields .scl_info .scl_html fieldset .html_contents .field_desc {
  color: #000;
  margin: 0;
  font-size: 13px;
  font-style: normal;
  font-weight: 300;
  line-height: 17px;
  font-family: Roboto, sans-serif;
}
.field_contents .scl_fields .scl_info .scl_text fieldset .fields,
.field_contents .scl_fields .scl_info .scl_html fieldset .fields {
  width: 50%;
  align-self: center;
  text-align: right;
}
.field_contents .scl_fields .scl_info .scl_text fieldset .fields .checked,
.field_contents .scl_fields .scl_info .scl_html fieldset .fields .checked {
  color: rgba(3, 58, 163, 0.85);
  border: 1px solid rgba(3, 58, 163, 0.81);
  background: rgba(182, 206, 254, 0.38);
  box-sizing: border-box;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.1);
}
.field_contents .scl_fields .scl_info .scl_text fieldset .fields .checked .dashicons-yes,
.field_contents .scl_fields .scl_info .scl_html fieldset .fields .checked .dashicons-yes {
  display: inline-block;
}
.field_contents .scl_fields .scl_info:nth-child(2) {
  margin-top: 15px;
  border-top: 1px solid #f3f4f6;
}
.field_contents .scl_fields .scl_info:last-child {
  margin-bottom: 10px;
}
.field_contents .gmap-field {
  text-align: left;
}
.field_contents .gmap-field .mapbox-wrapper .address-input {
  color: #000;
  margin: 20px 0;
  font-size: 15px;
}
.field_contents .gmap-field .mapbox-wrapper .address-input input {
  width: 100%;
  margin: 5px 0 3px;
  display: block;
  max-width: 320px;
  font-weight: 400;
}
.field_contents .gmap-field .gmap-wrap .search-address {
  color: #000;
  margin: 20px 0;
  max-width: 320px;
}
.dokan-settings-field-type-radio fieldset > label:not(:last-child) {
  margin-right: 12px !important;
}
.dokan-settings-field-type-radio fieldset > label:not(:last-child) > input[type="radio"] {
  margin-right: 2px;
}
.col-3 {
  width: 24.5%;
  display: inline-block;
}
.col-3 select {
  width: 100%;
}
@media only screen and (max-width: 430px) {
.field_contents {
    padding: 14px 14px 18px 14px;
}
.field_contents fieldset {
    display: block;
}
.field_contents fieldset .field_data .field_heading {
    font-size: 10px;
    line-height: 24px;
}
.field_contents fieldset .field_data .field_desc {
    font-size: 8px;
}
.field_contents fieldset .field {
    margin-top: 15px;
    text-align: left;
}
.field_contents fieldset .field select,
  .field_contents fieldset .field textarea,
  .field_contents fieldset .field input[type=text] {
    min-height: 28px;
    font-size: 8px;
}
.field_contents fieldset .field .small {
    max-width: 35% !important;
}
.field_contents fieldset .field .medium {
    max-width: 70% !important;
}
.field_contents fieldset .field .large {
    max-width: 100% !important;
}
.field_contents .scl_fields {
    padding: 10px 15px;
}
.field_contents .scl_fields .scl_header {
    display: block;
}
.field_contents .scl_fields .scl_header .scl_contents {
    display: block;
}
.field_contents .scl_fields .scl_header .scl_contents .scl_desc {
    font-size: 8px;
}
.field_contents .scl_fields .scl_header .expand_btn {
    text-align: left;
}
.field_contents .scl_fields .scl_info .scl_html,
  .field_contents .scl_fields .scl_info .scl_text {
    padding: 10px;
}
.field_contents .scl_fields .scl_info .scl_html .field_html,
  .field_contents .scl_fields .scl_info .scl_text .field_html {
    font-size: 10px;
    line-height: 20px;
}
.field_contents .scl_fields .scl_info .scl_html .field_desc,
  .field_contents .scl_fields .scl_info .scl_text .field_desc {
    font-size: 8px;
}
.field_contents .scl_fields .scl_info .scl_html select,
  .field_contents .scl_fields .scl_info .scl_text select,
  .field_contents .scl_fields .scl_info .scl_html textarea,
  .field_contents .scl_fields .scl_info .scl_text textarea,
  .field_contents .scl_fields .scl_info .scl_html input[type=text],
  .field_contents .scl_fields .scl_info .scl_text input[type=text] {
    font-size: 8px;
    min-height: 28px;
}
}
@media only screen and (max-width: 768px) {
.field select,
  .field textarea,
  .field input[type=text] {
    max-width: 125px !important;
}
.field .small {
    max-width: 35% !important;
}
.field .medium {
    max-width: 70% !important;
}
.field .large {
    max-width: 100% !important;
}
}

#dokan-settings-banner {
  margin: 20px 0px;
  padding: 40px;
  background: #fff;
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
}
#dokan-settings-banner a {
  box-shadow: none;
  background: #FF5722;
  color: #fff;
  border-color: #FF5722;
}
#dokan-settings-banner a:hover {
  background: #ff6a3c;
}
#dokan-settings-banner img {
  flex: 1;
}
#dokan-settings-banner .content {
  flex: 4;
  padding: 20px 50px;
}
#dokan-settings-banner .content p {
  margin: 30px 0px;
  font-size: 14px;
}
#dokan-settings-banner .content a {
  margin-right: 8px;
}
#dokan-settings-banner .content .custom-button {
  background: #fff;
  color: #565656;
  border-color: #DFDADF;
  -webkit-box-shadow: 0px 0px 11px 1px #ebebeb;
  -moz-box-shadow: 0px 0px 11px 1px #ebebeb;
  box-shadow: 0px 0px 11px 1px #ebebeb;
}
#dokan-settings-banner .content .custom-button:hover {
  background: #f2f2f2;
}

.dokan-settings-wrap {
  border: 1px solid #c8d7e1;
  display: flex;
  padding: 20px;
  position: relative;
  background: #fff;
  padding-bottom: 100px;
  scroll-margin-top: 65px;
}
.dokan-settings-wrap .dokan-settings-menu-toggle-btn {
  border: 1px solid #e9e9ea;
}
.dokan-settings-wrap .loading {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
}
.dokan-settings-wrap .loading .dokan-loader {
  top: 40%;
  left: 45%;
}
.dokan-settings-wrap .dashicons {
  padding-top: 2px;
  margin-right: 15px;
}
.dokan-settings-wrap div.nav-tab-wrapper {
  background: #F9FAFB;
  box-sizing: border-box;
  margin-right: 12px;
  border-bottom: none;
  border-top-color: #cecaca85;
}
.dokan-settings-wrap div.nav-tab-wrapper .nav-section {
  padding: 14px 16px 30px 24px;
}
.dokan-settings-wrap div.nav-tab-wrapper .nav-tab {
  color: #052B74;
  float: none;
  margin: 0;
  border: none;
  cursor: pointer;
  display: flex;
  padding: 18px;
  font-size: 15px;
  transition: none;
  background: transparent;
  font-weight: bold;
  border-bottom: 1px solid #e9e9ea;
  transition-property: none;
  white-space: normal;
}
.dokan-settings-wrap div.nav-tab-wrapper .nav-tab img {
  width: 20px;
  height: 20px;
  margin: 3px 15px 0 0;
}
.dokan-settings-wrap div.nav-tab-wrapper .nav-tab .nav-content .nav-title {
  line-height: 22px;
  text-transform: uppercase;
}
.dokan-settings-wrap div.nav-tab-wrapper .nav-tab .nav-content .nav-description {
  color: #686666;
  font-size: 10px;
  line-height: 14px;
  font-weight: 500;
  text-transform: uppercase;
}
.dokan-settings-wrap div.nav-tab-wrapper .nav-tab:focus,
.dokan-settings-wrap div.nav-tab-wrapper .nav-tab:active {
  box-shadow: none;
  outline: 0;
}
.dokan-settings-wrap div.nav-tab-wrapper .nav-tab.nav-tab-active {
  width: 100%;
  color: rgba(3, 58, 163, 0.81);
  position: relative;
  transition: 0.3s linear;
  background: #fff !important;
  transition-property: none;
}
.dokan-settings-wrap div.nav-tab-wrapper .nav-tab.nav-tab-active:before {
  content: '';
  position: absolute;
  left: 0px;
  width: 4px;
  background-color: #246EFE;
  height: 100%;
  top: 0;
}
.dokan-settings-wrap div.nav-tab-wrapper .nav-tab:last-child {
  border-bottom: 0;
}
.dokan-settings-wrap .metabox-holder {
  flex: 3;
  padding: 0 6px 75px 3% !important;
  position: relative;
}
.dokan-settings-wrap .metabox-holder .settings-header {
  display: flex;
  margin-bottom: 50px;
  justify-content: space-between;
}
.dokan-settings-wrap .metabox-holder .settings-header .settings-content {
  flex: 4;
}
.dokan-settings-wrap .metabox-holder .settings-header .settings-content .settings-title {
  margin: 30px 0 20px 0;
  font-size: 22px;
  line-height: 26px;
  font-family: Roboto, sans-serif;
  margin-bottom: 12px;
}
.dokan-settings-wrap .metabox-holder .settings-header .settings-content .settings-description {
  color: #000;
  margin: 0;
  font-size: 16px;
  font-weight: 300;
  line-height: 24px;
  font-family: Roboto, sans-serif;
}
.dokan-settings-wrap .metabox-holder .settings-header .settings-document-button {
  flex: 2.5;
  text-align: right;
  margin-top: 35px;
}
.dokan-settings-wrap .metabox-holder .settings-header .settings-document-button a.doc-link {
  color: #033AA3D9;
  border: 1px solid #f3f4f6;
  padding: 10px 15px;
  font-size: 12px;
  background: #FFF;
  box-sizing: border-box;
  box-shadow: 2px 2px 3px 0px rgba(0, 0, 0, 0.1);
  font-family: Roboto, sans-serif;
  line-height: 15px;
  border-radius: 6.56px;
  text-decoration: none;
}
.dokan-settings-wrap .metabox-holder .settings-header .settings-document-button a.doc-link:hover {
  background: #033aa30f;
}
.dokan-settings-wrap .metabox-holder .group .form-table .dokan-settings-fields .dokan-settings-field-type-sub_section,
.dokan-settings-wrap .metabox-holder .group .form-table .dokan-settings-fields .dokan-settings-field-type-disbursement_sub_section {
  border-bottom: 1px solid #f3f4f6;
}
.dokan-settings-wrap .metabox-holder .group .form-table .dokan-settings-fields .dokan-settings-field-type-sub_section .sub-section-styles,
.dokan-settings-wrap .metabox-holder .group .form-table .dokan-settings-fields .dokan-settings-field-type-disbursement_sub_section .sub-section-styles {
  margin-top: 20px;
  margin-bottom: 0;
  padding: 20px;
  background: #f9fafb;
}
.dokan-settings-wrap .metabox-holder .group .form-table .dokan-settings-fields div:not(.dokan-settings-field-type-sub_section) .field_contents {
  border: 1px solid #f3f4f6;
  border-top: none;
}
.dokan-settings-wrap .metabox-holder .group .form-table .dokan-settings-fields > div:not(.dokan-settings-field-type-sub_section):first-child {
  border-top: 1px solid #f3f4f6;
}
.dokan-settings-wrap .metabox-holder .back-to-top {
  width: 44px;
  right: 75px;
  height: 44px;
  bottom: 150px;
  cursor: pointer;
  position: fixed;
  transition: 0.1s linear;
  transform: scale(0);
  box-shadow: 0px 0px 10px 0px #0000001F;
  border-radius: 50%;
  background-color: #fff;
}
.dokan-settings-wrap .metabox-holder .back-to-top img {
  top: 50%;
  left: 50%;
  position: absolute;
  transform: translate(-50%, -50%);
}
.dokan-settings-wrap .metabox-holder .back-to-top:hover {
  transform: scale(1.05);
}
.dokan-settings-wrap .metabox-holder:before {
  top: 0;
  left: 0;
  width: 1px;
  height: 100%;
  content: "";
  position: absolute;
  background: #fff;
}
.dokan-settings-wrap .radio-image-container {
  padding: 20px 0;
  display: grid;
  grid-row-gap: 2.6%;
  grid-column-gap: 3.2%;
}
.dokan-settings-wrap .radio-image-container .radio-image {
  display: block;
  width: 50%;
  width: 100%;
  background: #fff;
  -webkit-box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.1);
  margin: 0 0 15px;
  position: relative;
  line-height: 0;
  border: 1px solid #ededed;
  padding: 4px;
}
.dokan-settings-wrap .radio-image-container .radio-image img {
  max-width: 100%;
  z-index: 1;
}
.dokan-settings-wrap .radio-image-container .radio-image .current-option-indicator {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #4CAF50;
  color: #fff;
  padding: 4px;
  z-index: 2;
  line-height: 1.4;
}
.dokan-settings-wrap .radio-image-container .radio-image .active-option {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 3;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.45);
  transition: opacity 0.4s ease;
}
.dokan-settings-wrap .radio-image-container .radio-image .active-option button {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -23px;
  margin-left: -58px;
}
.dokan-settings-wrap .radio-image-container .radio-image:hover .active-option {
  opacity: 1;
}
.dokan-settings-wrap .radio-image-container .radio-image.active .active-option {
  display: none;
}
.dokan-settings-wrap .radio-image-container .radio-image.not-active .current-option-indicator {
  display: none;
}
.dokan-settings-wrap .radio-image-container label {
  display: block;
  margin-bottom: 5px;
}
.dokan-settings-wrap .radio-image-container label > input[type='radio'] {
  visibility: hidden;
  /* Makes input not-clickable */
  position: absolute;
  /* Remove input from document flow */
}
.dokan-settings-wrap .radio-image-container label > img {
  max-width: 100%;
}
.dokan-settings-wrap .radio-image-container {
  grid-template-columns: repeat(2, 1fr);
}
.dokan-settings-wrap .search-section {
  color: rgba(60, 60, 67, 0.6);
  filter: drop-shadow(0px 0.0869484px 0.260845px rgba(0, 0, 0, 0.1)) drop-shadow(0px 0.869484px 1.73897px rgba(0, 0, 0, 0.2));
  margin: 8px 0px 14px;
  display: flex;
  position: relative;
  background: #FFF;
  align-items: center;
  border-radius: 5px;
}
.dokan-settings-wrap .search-section .search-box input {
  width: 100%;
  height: 48px;
  border: none;
  display: block;
  padding: 0 45px 0 0;
  background: transparent;
  font-weight: 400;
  font-family: Roboto, sans-serif;
}
.dokan-settings-wrap .search-section .search-box input:focus {
  outline: none;
  box-shadow: none;
  border-color: transparent;
}
.dokan-settings-wrap .search-section .search-box .dashicons.dashicons-search {
  font-size: 1.5rem;
}
.dokan-settings-wrap .search-section .search-box .dashicons.dashicons-no-alt {
  cursor: pointer;
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
}
.dokan-settings-wrap .search-section .search-box .dashicons.dashicons-no-alt:hover {
  color: #d43f3a;
}
.dokan-settings-wrap .search-section .dashicons.dashicons-search {
  font-size: 26px;
  margin-left: 15px;
  line-height: 20px;
  letter-spacing: 0.434742px;
}
.dokan-settings-wrap .search-section .dashicons.dashicons-no-alt {
  position: absolute;
  top: 50%;
  right: 5px;
  cursor: pointer;
  color: #000;
  font-size: 25px;
  transform: translate(0%, -60%);
}
.dokan-settings-wrap .search-section .dashicons.dashicons-no-alt:hover {
  color: #d43f3a;
}
.dokan-settings-wrap .search-section .dokan-admin-search-settings {
  width: 100%;
  border: 0;
  height: 48px;
  display: block;
  padding: 0 45px 0 0;
  background: #FFF;
  border-top: 0;
  font-weight: 400;
  font-family: Roboto, sans-serif;
}
.dokan-settings-wrap .search-section input[type="text"]:focus {
  border-color: transparent;
  outline: none;
  box-shadow: none;
}
.form-table th.dokan-settings-sub-section-title {
  border-bottom: 1px solid #cccccc;
  padding: 0 0 10px;
}
.form-table th.dokan-settings-sub-section-title label {
  display: block;
  margin-top: 20px;
  color: #0073aa;
  font-weight: 500;
  font-size: 1.3em;
}
.form-table .dokan-settings-field-type-sub_section:first-child th.dokan-settings-sub-section-title label {
  margin-top: 0;
}
tr.data_clear_on_uninstall td fieldset label {
  background: #e00;
  padding: 5px;
  color: white;
  border-radius: 3px;
}
.submit {
  margin-top: 40px !important;
  text-align: right !important;
}
.submit .button {
  color: #FFFFFF;
  padding: 10px 15px;
  font-size: 15px;
  transition: 0.3s;
  background: #5a92ff;
  font-style: normal;
  font-family: 'Roboto', sans-serif;
  font-weight: 800;
  line-height: 17px;
  border-color: transparent;
  border-radius: 4.46803px;
}
@media only screen and (max-width: 768px) {
.dokan-settings-wrap .nav-tab-wrapper .nav-tab .nav-content .nav-title {
    font-size: 10px;
}
.dokan-settings-wrap .nav-tab-wrapper .nav-tab .nav-content .nav-description {
    font-size: 8px !important;
}
.dokan-settings-wrap .metabox-holder {
    width: 100%;
    padding: 0 !important;
}
.dokan-settings-wrap .metabox-holder .settings-header .settings-content .settings-title {
    padding-left: 0;
}
.dokan-settings-wrap .slide-enter-active,
  .dokan-settings-wrap .slide-leave-active {
    transition: transform 0.3s ease-out;
}
.dokan-settings-wrap .slide-enter,
  .dokan-settings-wrap .slide-leave-to {
    transform: translateX(-100%);
}
.dokan-settings-wrap .dokan-settings-wrap .nav-tab-wrapper .mobile-settings-list {
    margin-top: 1rem;
}
.dokan-settings-wrap .dokan-settings-wrap .nav-tab-wrapper .mobile-settings-list .setting-section.active {
    border-left: 4px solid #246EFE;
}
.dokan-settings-wrap .dokan-settings-wrap .nav-tab-wrapper .mobile-settings-list .setting-section:hover {
    background-color: #f9fafb;
}
}

.dokan-upload-image {
  width: 100%;
}
.dokan-upload-image img {
  cursor: pointer;
}


.dokan-form-input.dokan-store-category{
    width: 103% !important;
    border: 0 !important;
    padding: 0 !important;
}
#store-category{
    border: 0;
}

.address-multiselect input.multiselect__input {
  border: none;
}

.checkbox-group {
  margin-top: 20px;
  padding: 0 10px;
}
.checkbox-group .checkbox-left {
  display: inline-block;
}
.checkbox-group .checkbox-left .switch {
  margin-right: 10px;
  display: inline-block;
}
.payment-info.edit-mode .checkbox-group {
  padding: 0;
}
.payment-info.edit-mode .dokan-form-select {
  margin-top: 5px;
  margin-bottom: 5px;
}

.swal2-container {
  z-index: 9999999 !important;
}
.swal2-container .swal2-popup .swal2-title {
  line-height: 35px;
  font-size: 30px;
  font-weight: 400;
}
.dokan-vendor-edit h1 {
  font-size: 23px;
  font-weight: 400;
}
.dokan-vendor-edit .tab-header .tab-list {
  overflow: hidden;
  display: flex;
  justify-content: space-between;
}
.dokan-vendor-edit .tab-header .tab-list .tab-title {
  height: 50px;
  list-style-type: none;
  position: relative;
  background-color: #1a9ed4;
  display: flex;
  justify-content: center;
  align-items: center;
}
.dokan-vendor-edit .tab-header .tab-list .tab-title .icon {
  position: relative;
  top: 1px;
}
.dokan-vendor-edit .tab-header .tab-list .tab-title a {
  color: #fff;
  text-decoration: none;
  padding: 75px;
}
.dokan-vendor-edit .tab-header .tab-list .tab-title a:active,
.dokan-vendor-edit .tab-header .tab-list .tab-title a:focus {
  outline: none;
  outline-style: none;
  border-color: transparent;
  box-shadow: none;
}
.dokan-vendor-edit .tab-header .tab-list .tab-title a span {
  position: relative;
  top: -1px;
  left: -3px;
}
.dokan-vendor-edit .tab-header .tab-list .tab-title:first-child {
  padding-left: 5px;
}
.dokan-vendor-edit .tab-header .tab-list .tab-title:nth-child(n+2)::before {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  border-left: 25px solid white;
  /* width: arrow width, color: background of document */
  border-top: 25px solid transparent;
  /* width: half height */
  border-bottom: 25px solid transparent;
  /* width: half height */
  width: 0;
  height: 0;
  content: " ";
}
.dokan-vendor-edit .tab-header .tab-list .tab-title:after {
  z-index: 1;
  /* need to bring this above the next item */
  position: absolute;
  top: 0;
  right: -25px;
  /* arrow width (negated) */
  display: block;
  border-left: 25px solid #f5f5f5;
  /* width: arrow width */
  border-top: 25px solid transparent;
  /* width: half height */
  border-bottom: 25px solid transparent;
  /* width: half height */
  width: 0;
  height: 0;
  content: " ";
  border-left-color: #1a9ed4;
}
.dokan-vendor-edit .tab-header .tab-list .tab-title.active {
  background-color: #2C70A3;
}
.dokan-vendor-edit .tab-header .tab-list .tab-title.active a {
  color: #fff;
}
.dokan-vendor-edit .tab-header .tab-list .tab-title.active:after {
  border-left-color: #2C70A3;
}
.dokan-vendor-edit .tab-header .tab-list .tab-title.last:after {
  border-left: 0;
}
.dokan-vendor-edit .tab-header .tab-list .tab-title.active ~ .tab-title {
  background-color: #f5f5f5;
}
.dokan-vendor-edit .tab-header .tab-list .tab-title.active ~ .tab-title:after {
  border-left-color: #f5f5f5;
}
.dokan-vendor-edit .tab-header .tab-list .tab-title.active ~ .tab-title a {
  color: #000;
}
.dokan-vendor-edit .tab-contents {
  border: 1px solid #e5e5e5;
  border-radius: 3px;
  min-height: 400px;
}
.dokan-vendor-edit .tab-contents .loading {
  position: relative;
  left: 46%;
  top: 160px;
}
.dokan-vendor-edit .tab-contents .content-header {
  background: #F9F9F9;
  margin: 0;
  padding: 10px;
}
.dokan-vendor-edit .tab-contents .content-body {
  padding-top: 20px;
  padding-bottom: 20px;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group {
  margin: 0 10px;
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group:after,
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group:before {
  display: table;
  content: " ";
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group .column {
  float: left;
  width: 50%;
  padding: 0 10px;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group .column .store-avaibility-info {
  display: flex;
  justify-content: space-between;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group .column .store-avaibility-info .store-url,
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group .column .store-avaibility-info span {
  margin: 0;
  padding: 0;
  position: relative;
  bottom: 10px;
  font-style: italic;
  color: #a09f9f;
  font-size: 12px;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group .column .store-avaibility-info .is-available {
  color: green;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group .column .store-avaibility-info .not-available {
  color: red;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group .column .password-generator {
  margin-top: 6px;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group .column .password-generator .regen-button {
  margin-right: 5px;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group .column .password-generator .regen-button span {
  line-height: 26px;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group .column .checkbox-left.notify-vendor {
  margin-top: 6px;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group .column .multiselect {
  margin-top: 5px;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group .column .multiselect__option--highlight {
  background: #3c9fd4;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group .column .multiselect__tags {
  min-height: 45px;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group .column .multiselect__single {
  padding-top: 3px;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group .column .multiselect__select:before {
  top: 70%;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group .column .multiselect__input:focus {
  box-shadow: none;
  border: none;
  outline: none;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-group .bank-info {
  padding-left: 10px;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-input {
  width: 100%;
  /* Full width */
  padding: 7px 12px;
  /* Some padding */
  border: 1px solid #ccc;
  /* Gray border */
  border-radius: 4px;
  /* Rounded borders */
  box-sizing: border-box;
  /* Make sure that padding and width stays in place */
  margin-top: 6px;
  /* Add a top margin */
  margin-bottom: 16px;
  /* Bottom margin */
  resize: vertical;
  /* Allow the user to vertically resize the textarea (not horizontally) */
  height: auto;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-input::placeholder {
  color: #bcbcbc;
}
.dokan-vendor-edit .tab-contents .content-body .dokan-form-input.has-error::placeholder {
  color: red;
}
.dokan-vendor-edit .tab-contents .content-body .vendor-image {
  display: flex;
  padding-bottom: 20px;
}
.dokan-vendor-edit .tab-contents .content-body .vendor-image .picture {
  background: #fcfcfc;
  border-radius: 3px;
  padding: 5px 10px;
  border: 2px dashed #d2d2d2;
  text-align: center;
  flex-grow: 1;
  width: 150px;
  margin-left: 20px;
}
.dokan-vendor-edit .tab-contents .content-body .vendor-image .picture .profile-image {
  max-width: 100px;
  margin: 0 auto;
}
.dokan-vendor-edit .tab-contents .content-body .vendor-image .picture .profile-image img {
  border: 1px solid #E5E5E5;
  padding: 15px 10px 0;
  cursor: pointer;
  width: 100%;
  padding: 5px;
}
.dokan-vendor-edit .tab-contents .content-body .vendor-image .picture.banner {
  padding: 0;
  flex-grow: 10;
  margin-right: 20px;
  height: 228px;
  padding-top: 5%;
}
.dokan-vendor-edit .tab-contents .content-body .vendor-image .picture.banner .banner-image img {
  width: 100%;
  height: 223px;
  padding: 0;
}
.dokan-vendor-edit .tab-contents .content-body .vendor-image .picture.banner .banner-image button {
  background: #007cba;
  color: white;
  padding: 10px 15px;
  border-radius: 3px;
  margin: 20px 0;
  cursor: pointer;
}
.dokan-vendor-edit .tab-contents .content-body .vendor-image .picture.banner.has-banner {
  padding-top: 0;
}
.dokan-vendor-edit .tab-contents .content-body .vendor-image .picture-footer {
  color: #808080;
  font-weight: 300;
}
.dokan-vendor-edit .dokan-btn {
  background: #1a9ed4;
  padding: 10px 20px;
  color: white;
  border-radius: 3px;
  cursor: pointer;
}
.dokan-vendor-edit .dokan-btn:active,
.dokan-vendor-edit .dokan-btn:focus {
  outline: none;
  outline-style: none;
  border-color: transparent;
  box-shadow: none;
}
.dokan-vendor-edit .dokan-modal .dokan-modal-content {
  height: 640px !important;
}
.dokan-vendor-edit .dokan-modal .dokan-modal-content .modal-body {
  max-height: 500px;
  min-height: 200px;
}
.dokan-vendor-edit .dokan-modal .dokan-modal-content .modal-footer {
  padding: 15px;
  bottom: 0;
  border-top: none;
  box-shadow: none;
}
.dokan-vendor-edit .component-fade-enter-active,
.dokan-vendor-edit .component-fade-leave-active {
  transition: opacity 0.2s ease;
}
.dokan-vendor-edit .component-fade-enter,
.dokan-vendor-edit .component-fade-leave-to {
  opacity: 0;
}
@media only screen and (max-width: 600px) {
.dokan-vendor-edit .dokan-modal .dokan-modal-content {
    height: 400px;
}
.dokan-vendor-edit .dokan-modal .dokan-modal-content .modal-body {
    max-height: 300px;
}
}
@media only screen and (max-width: 500px) {
.dokan-vendor-edit .tab-list .tab-title .tab-link {
    display: flex;
}
.dokan-vendor-edit .tab-list .tab-title .tab-link a {
    padding: 12px;
    margin-left: 17px;
}
.dokan-vendor-edit .tab-list .tab-title .tab-link a span {
    display: block;
    margin: 0 auto;
}
.dokan-vendor-edit .tab-contents p,
  .dokan-vendor-edit .tab-contents input,
  .dokan-vendor-edit .tab-contents button {
    font-size: 13px;
}
.dokan-vendor-edit .tab-contents .vendor-image {
    display: block !important;
}
.dokan-vendor-edit .tab-contents .vendor-image .picture {
    margin-right: 20px !important;
    width: auto !important;
}
.dokan-vendor-edit .tab-contents .vendor-image .picture.banner {
    margin-top: 15px;
}
}
@media only screen and (max-width: 375px) {
.dokan-vendor-edit .tab-list .tab-title .tab-link {
    display: flex;
}
.dokan-vendor-edit .tab-list .tab-title .tab-link a {
    padding: 5px;
    margin-left: 20px;
    font-size: 12px;
}
.dokan-vendor-edit .tab-contents p,
  .dokan-vendor-edit .tab-contents input,
  .dokan-vendor-edit .tab-contents button {
    font-size: 12px;
}
}
@media only screen and (max-width: 320px) {
.dokan-vendor-edit .tab-list .tab-title .tab-link {
    display: flex;
}
.dokan-vendor-edit .tab-list .tab-title .tab-link a {
    padding: 2px;
    margin-left: 20px;
    font-size: 10px;
}
}

.vendor-list .dokan-btn {
  padding: 5px 10px;
  font-size: 15px;
  border-radius: 3px;
  color: #2873aa;
}
.vendor-list .image {
  width: 10%;
}
.vendor-list .store_name {
  width: 30%;
}
.vendor-list td.store_name img {
  float: left;
  margin-right: 10px;
  margin-top: 1px;
  width: 24px;
  height: auto;
}
.vendor-list td.store_name strong {
  display: block;
  margin-bottom: 0.2em;
  font-size: 14px;
}
@media only screen and (max-width: 600px) {
.vendor-list table td.store_name,
  .vendor-list table td.enabled {
    display: table-cell !important;
}
.vendor-list table th:not(.check-column):not(.store_name):not(.enabled) {
    display: none;
}
.vendor-list table td:not(.check-column):not(.store_name):not(.enabled) {
    display: none;
}
.vendor-list table th.column,
  .vendor-list table table td.column {
    width: auto;
}
.vendor-list table td.manage-column.column-cb.check-column {
    padding-right: 15px;
}
.vendor-list table th.column.enabled {
    width: 25% !important;
}
}
@media only screen and (max-width: 320px) {
.vendor-list table .row-actions span {
    font-size: 11px;
}
}

.dokan-vendor-single .dokan-hide {
  display: none;
}
.dokan-vendor-single .vendor-profile .action-links.edit-mode .button span {
  line-height: 27px;
}
.dokan-vendor-single .vendor-profile .action-links.footer.edit-mode {
  float: right;
  margin-top: 20px;
}
.dokan-vendor-single .dokan-form-input {
  width: 100%;
  padding: 6px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  margin-top: 6px;
  margin-bottom: 16px;
  resize: vertical;
  height: auto;
}
.dokan-vendor-single .dokan-form-input::placeholder {
  color: #bcbcbc;
}
.dokan-vendor-single * {
  box-sizing: border-box;
}
.dokan-vendor-single .modal-body {
  min-height: 150px;
  max-height: 350px;
}
.dokan-vendor-single .modal-body .form-row {
  padding-bottom: 10px;
}
.dokan-vendor-single .modal-body .form-row input {
  width: 90%;
}
.dokan-vendor-single .modal-body label {
  display: block;
  padding-bottom: 3px;
}
.dokan-vendor-single .vendor-header {
  display: flex;
}
.dokan-vendor-single .vendor-header .profile-info {
  background: #fff;
  border: 1px solid #D9E4E7;
  padding: 20px;
  width: 285px;
  margin-right: 30px;
  border-radius: 3px;
  position: relative;
}
.dokan-vendor-single .vendor-header .profile-info .featured-vendor {
  position: absolute;
  top: 10px;
  right: 15px;
  color: #FF9800;
}
.dokan-vendor-single .vendor-header .profile-banner {
  position: relative;
  width: calc(100% - 285px + 30px);
  height: 350px;
  border: 1px solid #dfdfdf;
  background: #496a94;
  overflow: hidden;
}
.dokan-vendor-single .vendor-header .profile-banner img {
  height: 350px;
  width: 100%;
}
.dokan-vendor-single .vendor-header .profile-banner .action-links {
  position: absolute;
  right: 20px;
  top: 20px;
}
.dokan-vendor-single .vendor-header .profile-banner .action-links .button {
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.2);
}
.dokan-vendor-single .vendor-header .profile-banner .action-links .button.visit-store {
  background: #0085ba;
  border-color: #0085ba;
  color: #fff;
}
.dokan-vendor-single .vendor-header .profile-banner .action-links .button.visit-store:hover {
  background: #008ec2;
  border-color: #006799;
}
.dokan-vendor-single .vendor-header .profile-banner .action-links .button.visit-store .dashicons {
  font-size: 17px;
  margin-top: 5px;
}
.dokan-vendor-single .vendor-header .profile-banner .action-links .button.edit-store {
  color: #B8BAC2;
  background: #fff;
  border-color: #fff;
  margin-left: 5px;
}
.dokan-vendor-single .vendor-header .profile-banner .action-links .button.edit-store:hover {
  background: #eee;
  border-color: #eee;
}
.dokan-vendor-single .vendor-header .profile-banner .action-links .button.edit-store .dashicons {
  margin-top: 3px;
}
.dokan-vendor-single .vendor-header .profile-icon {
  position: relative;
  text-align: center;
  margin: 0 auto;
}
.dokan-vendor-single .vendor-header .profile-icon .edit-photo {
  position: absolute;
  left: 33%;
  top: 46px;
  color: white;
  width: 80px;
  cursor: pointer;
}
.dokan-vendor-single .vendor-header .profile-icon img {
  height: 120px;
  width: 120px;
  border-radius: 50%;
}
.dokan-vendor-single .vendor-header .profile-icon.edit-mode .dokan-upload-image {
  max-width: 120px;
  margin: 0 auto;
}
.dokan-vendor-single .vendor-header .profile-icon.edit-mode .dokan-upload-image .dokan-upload-image-container:hover img {
  padding: 5px;
  background-color: #f1f1f1;
  transition: padding 0.2s;
}
.dokan-vendor-single .vendor-header .profile-icon.edit-mode img {
  border: 5px solid #1a9ed4;
  cursor: pointer;
  opacity: 0.8;
}
.dokan-vendor-single .vendor-header .profile-banner.edit-mode {
  cursor: pointer;
}
.dokan-vendor-single .vendor-header .profile-banner.edit-mode .banner-wrap {
  display: flex;
  justify-content: center;
}
.dokan-vendor-single .vendor-header .profile-banner.edit-mode .banner-wrap img {
  border: 5px solid #5ca9d3;
  opacity: 0.5;
}
.dokan-vendor-single .vendor-header .profile-banner.edit-mode .banner-wrap img:hover {
  padding: 5px;
  transition: padding 0.2s;
}
.dokan-vendor-single .vendor-header .profile-banner.edit-mode .banner-wrap .edit-banner {
  position: absolute;
  left: 33%;
  top: 50%;
  font-size: 30px;
  font-weight: 400;
  color: white;
}
.dokan-vendor-single .vendor-header .profile-banner.edit-mode .banner-wrap .edit-banner i.change-banner {
  font-size: 50px;
  margin-top: -70px;
  position: relative;
  left: 140px;
}
.dokan-vendor-single .vendor-header .store-info .star-rating {
  text-align: center;
}
.dokan-vendor-single .vendor-header .store-info .star-rating span:before {
  content: "\f154";
  color: #999;
}
.dokan-vendor-single .vendor-header .store-info .star-rating span.active:before {
  content: "\f155";
  color: #FF9800;
}
.dokan-vendor-single .vendor-header .store-info h2 {
  text-align: center;
  font-size: 2em;
  margin-bottom: 0.5em;
}
.dokan-vendor-single .vendor-header .store-info .store-details {
  color: #AEB0B3;
}
.dokan-vendor-single .vendor-header .store-info .store-details .dashicons {
  color: #BABCC3;
}
.dokan-vendor-single .vendor-header .store-info .store-details li {
  margin-bottom: 8px;
  padding-left: 30px;
}
.dokan-vendor-single .vendor-header .store-info .store-details li:before {
  display: inline-block;
  width: 20px;
  height: 20px;
  font-size: 20px;
  line-height: 1;
  font-family: dashicons;
  text-decoration: inherit;
  font-weight: 400;
  font-style: normal;
  vertical-align: top;
  text-align: center;
  transition: color 0.1s ease-in 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin-left: -30px;
  width: 30px;
}
.dokan-vendor-single .vendor-header .store-info .store-details li.address:before {
  content: "\f230";
}
.dokan-vendor-single .vendor-header .store-info .store-details li.phone:before {
  content: "\f525";
  transform: scale(-1, 1);
}
.dokan-vendor-single .vendor-header .store-info .store-details.edit-mode .content-header {
  display: none;
}
.dokan-vendor-single .vendor-header .store-info .store-details.edit-mode li {
  padding-left: 0;
}
.dokan-vendor-single .vendor-header .store-info .actions {
  margin-top: 25px;
  text-align: center;
}
.dokan-vendor-single .vendor-header .store-info .actions .dashicons {
  color: #fff;
  border-radius: 50%;
  font-size: 16px;
  width: 16px;
  height: 16px;
  vertical-align: middle;
  margin-top: -2px;
}
.dokan-vendor-single .vendor-header .store-info .actions .message {
  background: #1FB18A;
  border-color: #1FB18A;
  color: #fff;
  box-shadow: none;
  font-size: 0.9em;
  margin-right: 7px;
}
.dokan-vendor-single .vendor-header .store-info .actions .message:hover {
  background: #1b9b79;
  border-color: #1b9b79;
}
.dokan-vendor-single .vendor-header .store-info .actions .status {
  background-color: #fff;
  box-shadow: none;
  font-size: 0.9em;
  border-color: #ddd;
}
.dokan-vendor-single .vendor-header .store-info .actions .status:hover {
  background-color: #eee;
}
.dokan-vendor-single .vendor-header .store-info .actions .status.enabled .dashicons {
  background-color: #19c11f;
}
.dokan-vendor-single .vendor-header .store-info .actions .status.enabled .dashicons:before {
  content: "\f147";
  margin-left: -2px;
}
.dokan-vendor-single .vendor-header .store-info .actions .status.disabled .dashicons {
  background-color: #f44336;
}
.dokan-vendor-single .vendor-header .store-info .actions .status.disabled .dashicons:before {
  content: "\f158";
}
.dokan-vendor-single .vendor-header .store-info a.store-categoy-names {
  text-align: center;
  font-weight: 500;
  font-size: 14px;
  margin: 8px 0 14px;
  color: #444;
  text-decoration: none;
  display: block;
  line-height: 1.6;
}
.dokan-vendor-single .vendor-header .store-info a.store-categoy-names:hover {
  color: #0073aa;
}
.dokan-vendor-single .vendor-header .store-info .store-categories-editing h4 {
  font-size: 15px;
  font-weight: 700;
  margin-bottom: 5px;
}
.dokan-vendor-single .vendor-header .store-info .store-categories-editing .button-link {
  text-decoration: none;
}
.dokan-vendor-single .vendor-header .store-info.edit-mode .account-info .content-header {
  display: none;
}
.dokan-vendor-single .vendor-header .store-info.edit-mode .account-info .column label {
  float: left;
  clear: both;
  margin-top: 10px;
  margin-left: -4px;
}
.dokan-vendor-single .vendor-header .store-info.edit-mode .account-info .column .dokan-form-input {
  width: 60%;
  padding: 5px;
  float: right;
  margin-right: -4px;
}
.dokan-vendor-single .vendor-header .store-info.edit-mode .account-info .column .store-url {
  margin: 0;
  padding: 0;
  bottom: 10px;
  font-style: italic;
  color: #a09f9f;
  font-size: 12px;
}
.dokan-vendor-single .vendor-summary {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}
.dokan-vendor-single .vendor-summary .summary-wrap {
  width: 72%;
  background: #fff;
  border: 1px solid #D9E4E7;
  border-radius: 3px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  margin: 0 15px;
}
.dokan-vendor-single .vendor-summary .stat-summary {
  width: 32%;
}
.dokan-vendor-single .vendor-summary .stat-summary h3 {
  margin: 0 0 1em 0;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts {
  border: 1px solid #dfdfdf;
  margin-bottom: 0;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li {
  margin-bottom: 10px;
  border-top: 1px solid #dfdfdf;
  position: relative;
  padding: 15px 10px 5px 75px;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li:first-child {
  border-top: none;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li .count {
  font-size: 1.5em;
  line-height: 130%;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li .count a {
  text-decoration: none;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li .subhead {
  color: #999;
  display: block;
  margin-top: 3px;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li:after {
  display: inline-block;
  width: 22px;
  height: 22px;
  font-size: 22px;
  line-height: 1;
  font-family: dashicons;
  text-decoration: inherit;
  font-weight: 400;
  font-style: normal;
  vertical-align: top;
  text-align: center;
  transition: color 0.1s ease-in 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  left: 31px;
  top: 26px;
  color: #fff;
  position: absolute;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li:before {
  position: absolute;
  width: 41px;
  height: 41px;
  border-radius: 50%;
  left: 20px;
  top: 18px;
  content: " ";
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.products {
  color: #FB094C;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.products a {
  color: #FB094C;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.products:before {
  background-color: #FB094C;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.products:after {
  font-family: WooCommerce!important;
  content: '\e006';
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.items {
  color: #2CC55E;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.items:before {
  background-color: #2CC55E;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.items:after {
  content: "\f233";
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.visitors {
  color: #0F72F9;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.visitors:before {
  background-color: #0F72F9;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.visitors:after {
  content: "\f307";
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.orders {
  color: #323ABF;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.orders a {
  color: #323ABF;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.orders:before {
  background-color: #323ABF;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.orders:after {
  content: "\f174";
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.gross {
  color: #80be0f;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.gross:before {
  background-color: #99E412;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.gross:after {
  content: "\f239";
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.earning {
  color: #8740A7;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.earning:before {
  background-color: #8740A7;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.earning:after {
  content: "\f524";
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.commision {
  color: #FB0A4C;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.commision:before {
  background-color: #FB0A4C;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.commision:after {
  content: "\f524";
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.balance {
  color: #FD553B;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.balance:before {
  background-color: #FD553B;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.balance:after {
  content: "\f184";
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.reviews {
  color: #EE8A12;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.reviews:before {
  background-color: #EE8A12;
}
.dokan-vendor-single .vendor-summary .stat-summary ul.counts li.reviews:after {
  content: "\f125";
}
.dokan-vendor-single .vendor-summary .badge-info {
  background: #fff;
  border: 1px solid #D9E4E7;
  border-radius: 3px;
  width: 25%;
}
.dokan-vendor-single .vendor-summary .vendor-info {
  background: #fff;
  border: 1px solid #D9E4E7;
  border-radius: 3px;
  padding: 20px;
  width: 27%;
}
.dokan-vendor-single .vendor-summary .vendor-info .subhead {
  color: #999;
  display: block;
  margin-bottom: 10px;
}
.dokan-vendor-single .vendor-summary .vendor-info ul {
  margin: 0;
}
.dokan-vendor-single .vendor-summary .vendor-info li {
  border-top: 1px solid #dfdfdf;
  padding: 10px 15px;
}
.dokan-vendor-single .vendor-summary .vendor-info li:first-child {
  border-top: none;
}
.dokan-vendor-single .vendor-summary .vendor-info li.registered {
  padding-top: 15px;
}
.dokan-vendor-single .vendor-summary .vendor-info .social-profiles .profiles a {
  text-decoration: none;
  color: #ddd;
  margin-right: 5px;
  font-size: 21px;
}
.dokan-vendor-single .vendor-summary .vendor-info .social-profiles .profiles a.active .fa-facebook-square {
  color: #3C5998;
}
.dokan-vendor-single .vendor-summary .vendor-info .social-profiles .profiles a.active .fa-twitter {
  color: #1496F1;
}
.dokan-vendor-single .vendor-summary .vendor-info .social-profiles .profiles a.active .fa-youtube {
  color: #CD2120;
}
.dokan-vendor-single .vendor-summary .vendor-info .social-profiles .profiles a.active .fa-instagram {
  color: #B6224A;
}
.dokan-vendor-single .vendor-summary .vendor-info .social-profiles .profiles a.active .fa-linkedin {
  color: #0C61A8;
}
.dokan-vendor-single .vendor-summary .vendor-info .social-profiles .profiles a.active .fa-pinterest-square {
  color: #BD091E;
}
.dokan-vendor-single .vendor-summary .vendor-info .social-profiles .profiles a.active .fa-flickr {
  color: #FB0072;
}
.dokan-vendor-single .vendor-summary .vendor-info li.payments .payment-methods {
  font-size: 35px;
  color: #ddd;
  display: flex;
  flex-flow: row wrap;
  gap: 10px;
}
.dokan-vendor-single .vendor-summary .vendor-info li.payments .payment-methods .tooltip {
  font-size: 12px;
}
.dokan-vendor-single .vendor-summary .vendor-info li.payments .payment-methods .payment-chip {
  filter: grayscale(1);
  opacity: 0.5;
  display: inline-block;
  font-size: 1em;
}
.dokan-vendor-single .vendor-summary .vendor-info li.payments .payment-methods .payment-chip.active {
  filter: grayscale(0);
  opacity: 1;
}
.dokan-vendor-single .vendor-summary .vendor-info li.payments .payment-methods .dokan-custom-payment {
  display: flex;
  align-items: center;
  padding: 0 10px;
  height: 26px;
  background: #FFF2FF;
  border-radius: 3px;
}
.dokan-vendor-single .vendor-summary .vendor-info li.payments .payment-methods .dokan-custom-payment span {
  color: #7C327C;
  font-size: 10px;
  font-weight: 600;
  line-height: 14.34px;
  margin-left: 5px;
}
.dokan-vendor-single .vendor-other-info .content-header {
  font-size: 14px !important;
  font-weight: 600 !important;
  padding-left: 12px !important;
}
.dokan-vendor-single .vendor-other-info .address-social-info {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}
.dokan-vendor-single .vendor-other-info .address-social-info .content-header {
  font-size: 18px;
  margin: 0;
  padding: 10px;
  border-bottom: 1px solid #f1f1f1;
}
.dokan-vendor-single .vendor-other-info .address-social-info .social-info,
.dokan-vendor-single .vendor-other-info .address-social-info .account-info {
  width: 48%;
  background-color: white;
}
.dokan-vendor-single .vendor-other-info .address-social-info .social-info .content-body,
.dokan-vendor-single .vendor-other-info .address-social-info .account-info .content-body {
  padding: 10px 20px;
}
.dokan-vendor-single .vendor-other-info .address-social-info .account-info .store-url {
  margin: 0;
  padding: 0;
  position: relative;
  bottom: 10px;
  font-style: italic;
  color: #a09f9f;
  font-size: 12px;
}
.dokan-vendor-single .vendor-other-info .payment-info {
  background-color: white;
  margin-top: 30px;
}
.dokan-vendor-single .vendor-other-info .payment-info .content-header {
  font-size: 18px;
  margin: 0;
  padding: 10px;
  border-bottom: 1px solid #f1f1f1;
}
.dokan-vendor-single .vendor-other-info .payment-info .content-body {
  display: flex;
  justify-content: space-between;
}
.dokan-vendor-single .vendor-other-info .payment-info .content-body .dokan-form-group {
  width: 48%;
  padding: 10px 20px;
}
.dokan-vendor-single .vendor-other-info .commission-info {
  background-color: white;
  margin-top: 30px;
}
.dokan-vendor-single .vendor-other-info .commission-info .content-header {
  font-size: 18px;
  margin: 0;
  padding: 10px;
  border-bottom: 1px solid #f1f1f1;
}
.dokan-vendor-single .vendor-other-info .commission-info .content-body {
  display: flex;
  justify-content: space-between;
}
.dokan-vendor-single .vendor-other-info .multiselect {
  margin-top: 5px;
}
.dokan-vendor-single .vendor-other-info .multiselect__select:before {
  top: 55%;
}
.dokan-vendor-single .vendor-other-info .multiselect__tags {
  min-height: 34px;
}
.dokan-vendor-single .vendor-other-info .multiselect__single {
  font-size: 14px;
  padding-left: 0px;
  margin-bottom: 4px;
  margin-top: -2px;
}
.dokan-vendor-single .vendor-other-info .multiselect__input:focus {
  box-shadow: none;
  border: none;
  outline: none;
}
@media only screen and (max-width: 600px) {
.dokan-vendor-single .vendor-profile .vendor-header {
    display: block;
}
.dokan-vendor-single .vendor-profile .vendor-header .profile-info {
    width: 100% !important;
    margin-bottom: 10px;
    padding: 0;
}
.dokan-vendor-single .vendor-profile .vendor-header .profile-info .profile-icon {
    padding: 10px 0 20px;
}
.dokan-vendor-single .vendor-profile .vendor-header .profile-banner {
    width: 100% !important;
}
.dokan-vendor-single .vendor-profile .vendor-header .profile-banner .banner-wrap .dokan-upload-image .dokan-upload-image-container span.edit-banner {
    width: 100%;
    left: 0;
    text-align: center;
}
.dokan-vendor-single .vendor-profile .vendor-summary {
    display: block;
}
.dokan-vendor-single .vendor-profile .vendor-summary .summary-wrap {
    display: block;
    width: 100% !important;
}
.dokan-vendor-single .vendor-profile .vendor-summary .summary-wrap .stat-summary {
    width: 100% !important;
    padding-bottom: 20px;
}
.dokan-vendor-single .vendor-profile .vendor-summary .vendor-info {
    width: 100% !important;
    margin-top: 20px;
}
.dokan-vendor-single .vendor-profile .vendor-other-info .address-social-info {
    flex-flow: column wrap;
}
.dokan-vendor-single .vendor-profile .vendor-other-info .address-social-info .account-info {
    width: 100%;
    margin: 0 auto 30px;
}
.dokan-vendor-single .vendor-profile .vendor-other-info .address-social-info .social-info {
    width: 100%;
    margin: 0 auto 30px;
}
.dokan-vendor-single .vendor-profile .vendor-other-info .payment-info .content-body {
    flex-flow: column wrap;
}
.dokan-vendor-single .vendor-profile .vendor-other-info .payment-info .content-body .dokan-form-group {
    width: 100%;
    margin: 0 auto 20px;
}
.dokan-vendor-single .vendor-profile .vendor-other-info .payment-info .content-body .dokan-form-group .column label {
    display: block;
}
}

.dokan-importer-wrapper[data-v-9bf2e882] {
  text-align: center;
  max-width: 700px;
  margin: 40px auto;
}
.dokan-importer-wrapper .skeleton-loader[data-v-9bf2e882] {
  width: 100%;
  display: block;
  background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 80%), lightgray;
  background-repeat: repeat-y;
  background-size: 50px 500px;
  background-position: 0 0;
  animation: shine-9bf2e882 1s infinite;
  border-radius: 5px;
}
@keyframes shine-9bf2e882 {
to {
    background-position: 100% 0, /* move highlight to right */ 0 0;
}
}
.dokan-importer-wrapper .error[data-v-9bf2e882] {
  border-radius: 5px;
}
.dokan-importer-wrapper .dokan-importer-progress-steps[data-v-9bf2e882] {
  padding: 0 0 24px;
  margin: 0;
  list-style: none outside;
  overflow: hidden;
  color: #ccc;
  width: 100%;
  display: inline-flex;
}
.dokan-importer-wrapper .dokan-importer-progress-steps li[data-v-9bf2e882] {
  width: 50%;
  float: left;
  padding: 0 0 0.8em;
  margin: 0;
  text-align: center;
  position: relative;
  border-bottom: 4px solid #ccc;
  line-height: 1.4em;
}
.dokan-importer-wrapper .dokan-importer-progress-steps li[data-v-9bf2e882]::before {
  content: "";
  border: 4px solid #ccc;
  border-radius: 100%;
  width: 4px;
  height: 4px;
  position: absolute;
  bottom: 0;
  left: 50%;
  margin-left: -6px;
  margin-bottom: -8px;
  background: #fff;
}
.dokan-importer-wrapper .dokan-importer-progress-steps li.active[data-v-9bf2e882] {
  border-color: #7047EB;
  color: #7047EB;
}
.dokan-importer-wrapper .dokan-importer-progress-steps li.active[data-v-9bf2e882]::before {
  border-color: #7047EB;
}
.dokan-importer-wrapper .dokan-importer[data-v-9bf2e882] {
  background: #fff;
  overflow: hidden;
  padding: 0;
  margin: 0 0 16px;
  color: #555;
  text-align: left;
}
.dokan-importer-wrapper .dokan-importer header[data-v-9bf2e882] {
  margin: 0;
  padding: 24px 24px 0;
}
.dokan-importer-wrapper .dokan-importer header .loader-title[data-v-9bf2e882] {
  width: 50%;
  height: 20px;
  margin-bottom: 24px;
}
.dokan-importer-wrapper .dokan-importer header .loader-description[data-v-9bf2e882] {
  height: 10px;
  margin-bottom: 10px;
}
.dokan-importer-wrapper .dokan-importer section[data-v-9bf2e882] {
  padding: 10px 24px 0;
}
.dokan-importer-wrapper .dokan-importer section div[data-v-9bf2e882] {
  margin: 0;
  position: relative;
  table-layout: fixed;
  border-collapse: collapse;
  width: 100%;
  clear: both;
}
.dokan-importer-wrapper .dokan-importer section div .dokan-dummy-data-progress-bar[data-v-9bf2e882] {
  width: 100%;
  height: 35px;
  -webkit-appearance: none;
}
.dokan-importer-wrapper .dokan-importer section div .dokan-dummy-data-progress-bar[value][data-v-9bf2e882]::-webkit-progress-bar {
  background-color: #EEEEEE;
  border: 1px solid #BCBCBC;
  border-radius: 5px;
}
.dokan-importer-wrapper .dokan-importer section div .dokan-dummy-data-progress-bar[value][data-v-9bf2e882]::-webkit-progress-value {
  background-color: #7047EB;
  border-radius: 5px;
  transition: width 0.5s;
}
.dokan-importer-wrapper .dokan-importer section div .loader-loader[data-v-9bf2e882] {
  height: 35px;
}
.dokan-importer-wrapper .dokan-importer .import-done[data-v-9bf2e882] {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 24px 24px 0 24px;
}
.dokan-importer-wrapper .dokan-importer .import-done p[data-v-9bf2e882] {
  font-size: 1.2rem;
  color: #7047EB;
}
.dokan-importer-wrapper .dokan-importer .import-done .links[data-v-9bf2e882] {
  text-align: center;
}
.dokan-importer-wrapper .dokan-importer .dokan-importer-action[data-v-9bf2e882] {
  overflow: hidden;
  margin: 0;
  padding: 24px;
  line-height: 3em;
}
.dokan-importer-wrapper .dokan-importer .dokan-importer-action .loader-btn[data-v-9bf2e882] {
  height: 35px;
  width: 150px;
  float: right;
}
.dokan-importer-wrapper .dokan-importer .dokan-importer-action .dokan-import-continue-btn[data-v-9bf2e882] {
  float: right;
  font-size: 1.25em;
  padding: 6px 12px;
  line-height: 1.5em;
  height: auto;
  border-radius: 4px;
  background-color: #7047EB;
  border-color: #7047EB;
  border: none;
  margin: 0;
  opacity: 1;
  color: #FFF;
  cursor: pointer;
}
.dokan-importer-wrapper .dokan-importer .dokan-importer-action .dokan-import-continue-btn.is-loading[data-v-9bf2e882] {
  background-color: rgba(112, 71, 235, 0.5);
  border-color: rgba(112, 71, 235, 0.5);
}
.dokan-importer-wrapper .dokan-importer .dokan-importer-action .dokan-import-continue-btn.is-busy[data-v-9bf2e882] {
  animation: components-button__busy-animation 2500ms infinite linear;
  opacity: 1;
  background-size: 100px 100%;
  background-image: linear-gradient(-45deg, #f7f6f6 33%, #e0e0e0 33%, #e0e0e0 70%, #f7f6f6 70%);
  color: #848484;
}
.dokan-importer-wrapper .dokan-importer .dokan-importer-action .cancel-btn[data-v-9bf2e882] {
  background-color: #FFFFFF;
  border-color: #E2E2E2;
  color: #72777C;
  border: 1px solid #E2E2E2;
  margin-top: 15px;
}

#dokan-vendor-capabilities {
  padding: 20px 70px;
}
#dokan-vendor-capabilities .grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-gap: 16px;
  margin-bottom: 30px;
}
#dokan-vendor-capabilities .col-6 {
  grid-column: auto / span 6;
}
#dokan-vendor-capabilities #dokan-capability-image-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #000000;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 99900;
}
#dokan-vendor-capabilities #dokan-capability-image-popup .modal-content {
  background-color: transparent;
  z-index: 100000;
  margin: 8% auto;
  max-width: 850px;
  text-align: center;
}
#dokan-vendor-capabilities .capability-card {
  background: #fff;
  padding: 50px;
  border-radius: 9px;
  border: 1px solid #E2E2E2;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}
#dokan-vendor-capabilities .capability-card .capability-image {
  position: relative;
  width: 100%;
}
#dokan-vendor-capabilities .capability-card .capability-image .image {
  opacity: 1;
  width: 100%;
  height: 270px;
  max-height: 370px;
  transition: 0.5s ease;
  backface-visibility: hidden;
}
#dokan-vendor-capabilities .capability-card .capability-image .middle {
  transition: 0.5s ease;
  opacity: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  text-align: center;
}
#dokan-vendor-capabilities .capability-card .capability-image:hover .image {
  opacity: 0.3;
}
#dokan-vendor-capabilities .capability-card .capability-image:hover .middle {
  opacity: 1;
}
#dokan-vendor-capabilities .capability-card .capability-image .zoom {
  background-color: #7047EB;
  border-radius: 9px;
  color: white;
  font-size: 24px;
  padding: 16px 24px;
  cursor: pointer;
}
#dokan-vendor-capabilities .capability-card .title {
  font-size: 18px;
}
#dokan-vendor-capabilities .capability-card .content {
  min-height: 40px;
}
#dokan-vendor-capabilities .capability-card p {
  margin-bottom: 0px;
}
#dokan-vendor-capabilities .vendor-capabilities-banner {
  display: flex;
  align-items: center;
  padding: 50px;
  height: auto;
  border-radius: 9px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  margin: 20px 0;
}
#dokan-vendor-capabilities .vendor-capabilities-banner .content {
  margin: 30px;
}
#dokan-vendor-capabilities .vendor-capabilities-banner .content .title {
  font-size: 51px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  background: linear-gradient(90deg, #FFF 34.5%, #D68FFF 100%);
  background-clip: text !important;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
#dokan-vendor-capabilities .vendor-capabilities-banner .content p {
  color: #FFFFFF;
  max-width: 512px;
  width: 100%;
  font-size: 16px;
}

.dokan-rw-footer {
  display: flex;
  flex-direction: row-reverse;
}
.dokan-rw-footer .dokan-rw-footer-btn {
  display: flex;
  align-items: center;
}
.dokan-rw-footer .dokan-rw-footer-btn img.not-loading {
  height: 0px;
  transition: height 200ms;
}
.dokan-rw-footer .dokan-rw-footer-btn img.is-loading {
  height: 20px;
  margin-right: 10px;
  transition: height 200ms;
}
.dokan-rw-multiselect-container .multiselect .multiselect__tags {
  border: 1px solid #b0a7a7;
}
.dokan-rw-multiselect-container .multiselect .multiselect__tags .multiselect__placeholder {
  color: #b0a7a7;
}
.dokan-rw-section .dokan-rw-section-heading {
  display: flex;
  align-items: center;
  column-gap: 5px;
}
.dokan-rw-section .dokan-rw-section-body .dokan-rw-note-area textarea {
  width: 100%;
  padding: 0.2rem;
  border: 1px solid #b0a7a7;
  min-height: 100px;
  padding-left: 0.2rem;
  padding-right: 0.2rem;
}
.dokan-rw-section .dokan-rw-section-body .dokan-rw-note-area textarea:focus {
  border-color: #b0a7a7;
  box-shadow: 0 0 0 0px #b0a7a7;
  outline: none;
}
.dokan-rw-section .dokan-rw-section-body .dokan-rw-note-area textarea::placeholder {
  color: #b0a7a7;
}
.dokan-rw-multiselect {
  margin-top: 1rem;
}
.dokan-rw-multiselect input {
  padding: 0;
  line-height: 0;
  min-height: 0;
  box-shadow: none;
  border-radius: 0;
  border: none;
}
.dokan-rw-multiselect input:focus {
  border-color: transparent;
  box-shadow: none;
  outline: none;
}
.dokan-rw-input input {
  width: 100%;
  padding: 0.2rem;
  border: 1px solid #b0a7a7;
}
.dokan-rw-input input:focus {
  border-color: #b0a7a7;
  box-shadow: 0 0 0 0px #b0a7a7;
  outline: none;
}
.dokan-rw-input input::placeholder {
  color: #b0a7a7;
}
@media only screen and (max-width: 800px) {
.dokan-add-new-rw .dokan-modal-content {
    width: 80% !important;
}
}
@media only screen and (max-width: 500px) {
.dokan-add-new-rw .dokan-modal-content {
    width: 400px !important;
    top: 50% !important;
}
}
@media only screen and (max-width: 376px) {
.dokan-add-new-rw .dokan-modal-content {
    width: 90% !important;
}
}
.dokan-add-new-rw .dokan-modal-content {
  height: 70% !important;
  overflow: scroll;
}
.dokan-add-new-rw .modal-footer {
  bottom: -55px;
  position: relative;
}
.dokan-add-new-rw .modal-body {
  overflow-y: hidden !important;
}

.animate-enter-active {
  animation: animate 150ms;
}
.animate-leave-active {
  animation: animate 150ms reverse;
}
@keyframes animate {
0% {
    opacity: 0;
}
50% {
    opacity: 0.5;
}
100% {
    opacity: 1;
}
}
.swal2-actions button {
  margin-right: 10px !important;
}
.dokan-reverse-withdrawal .dokan-reverse-withdrawal-fact-card {
  display: flex;
  flex-wrap: wrap;
}
#dokan_reverse_withdrawal_list_table .dokan-reverse-withdrawal-filters {
  display: flex;
}
#dokan_reverse_withdrawal_list_table .dokan-reverse-withdrawal-filters .multiselect .multiselect__select {
  height: 28px;
}
#dokan_reverse_withdrawal_list_table .dokan-reverse-withdrawal-filters .multiselect .multiselect__tags input.multiselect__input {
  max-height: 28px;
}
#dokan_reverse_withdrawal_list_table .dokan-reverse-withdrawal-filters .multiselect .multiselect__tags span.multiselect__single {
  margin: 0 auto;
  min-height: 28px;
  line-height: 28px;
}
@media only screen and (max-width: 500px) {
#dokan_reverse_withdrawal_list_table .dokan-reverse-withdrawal-filters {
    flex-direction: column;
}
}
#dokan_reverse_withdrawal_list_table input.multiselect__input {
  border: none;
}
#dokan_reverse_withdrawal_list_table .label {
  display: inline-block;
  padding: 0px 6px;
  color: #fff;
  font-size: 10px;
  font-weight: bold;
  border-radius: 10px;
}
#dokan_reverse_withdrawal_list_table .expired {
  background-color: #5cb85c;
}
#dokan_reverse_withdrawal_list_table .not_published {
  background-color: #fb7369;
}
#dokan_reverse_withdrawal_list_table .search-by-product {
  display: inline;
  margin-left: 5px;
}
#dokan_reverse_withdrawal_list_table .search-by-product .search-box #post-search-input {
  border-radius: 3px;
  border: 1px solid #aaaaaa;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  padding-left: 8px !important;
}
#dokan_reverse_withdrawal_list_table .search-by-product .search-box #post-search-input::placeholder {
  color: #999 !important;
}
#dokan_reverse_withdrawal_list_table div.actions {
  display: flex;
}
#dokan_reverse_withdrawal_list_table .multiselect {
  display: inline-block !important;
  width: 250px !important;
  font-size: 12px !important;
}
#dokan_reverse_withdrawal_list_table .multiselect .multiselect__tags {
  font-size: 12px !important;
  min-height: 30px !important;
  max-height: 33px !important;
  padding-top: 0px !important;
}
#dokan_reverse_withdrawal_list_table .multiselect .multiselect__input {
  border: none;
  box-shadow: none;
}
#dokan_reverse_withdrawal_list_table .multiselect .multiselect__input:focus {
  border: none;
  box-shadow: none;
  outline: none;
}
#dokan_reverse_withdrawal_list_table .widefat .store {
  width: 15em;
}
#dokan_reverse_withdrawal_list_table .widefat .product_title {
  width: 20em;
}
#dokan_reverse_withdrawal_list_table .negative-balance > div {
  display: inline;
}

.swal2-actions button {
  margin-right: 10px !important;
}
.dokan-reverse-withdrawal-transactions .dokan-reverse-withdrawal-fact-card {
  display: flex;
  flex-wrap: wrap;
}
#dokan_reverse_withdrawal_transactions_list_table input.multiselect__input {
  border: none;
}
#dokan_reverse_withdrawal_transactions_list_table div.actions {
  display: flex;
}
#dokan_reverse_withdrawal_transactions_list_table .multiselect {
  display: inline-block !important;
  width: 250px !important;
  font-size: 12px !important;
}
#dokan_reverse_withdrawal_transactions_list_table .multiselect .multiselect__tags {
  font-size: 12px !important;
  min-height: 30px !important;
  max-height: 33px !important;
  padding-top: 0px !important;
}
#dokan_reverse_withdrawal_transactions_list_table .multiselect .multiselect__input {
  border: none;
  box-shadow: none;
}
#dokan_reverse_withdrawal_transactions_list_table .multiselect .multiselect__input:focus {
  border: none;
  box-shadow: none;
  outline: none;
}
#dokan_reverse_withdrawal_transactions_list_table .widefat .store {
  width: 15em;
}
#dokan_reverse_withdrawal_transactions_list_table .widefat .product_title {
  width: 20em;
}
#dokan_reverse_withdrawal_transactions_list_table .negative-balance > div {
  display: inline;
}

