{"packages": [{"name": "appsero/client", "version": "v2.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Appsero/client.git", "reference": "12ff65b9770286d21edf314e7acfcd26fdde3315"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Appsero/client/zipball/12ff65b9770286d21edf314e7acfcd26fdde3315", "reference": "12ff65b9770286d21edf314e7acfcd26fdde3315", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.2", "phpcompatibility/phpcompatibility-wp": "dev-master", "phpunit/phpunit": "^8.5.31", "squizlabs/php_codesniffer": "^3.7", "tareq1988/wp-php-cs-fixer": "dev-master", "wp-coding-standards/wpcs": "dev-develop"}, "time": "2024-11-25T05:58:23+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Appsero\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Appsero Client", "keywords": ["analytics", "plugin", "theme", "wordpress"], "support": {"issues": "https://github.com/Appsero/client/issues", "source": "https://github.com/Appsero/client/tree/v2.0.4"}, "install-path": "../appsero/client"}, {"name": "appsero/updater", "version": "v2.3.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Appsero/updater.git", "reference": "0c899f2e6b5894b695751d738151a98461cdb198"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Appsero/updater/zipball/0c899f2e6b5894b695751d738151a98461cdb198", "reference": "0c899f2e6b5894b695751d738151a98461cdb198", "shasum": ""}, "time": "2025-03-10T03:42:43+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Appsero\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Appsero client updater module", "support": {"issues": "https://github.com/Appsero/updater/issues", "source": "https://github.com/Appsero/updater/tree/v2.3.3"}, "install-path": "../appsero/updater"}, {"name": "jakeasmith/http_build_url", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/jakeasmith/http_build_url.git", "reference": "93c273e77cb1edead0cf8bcf8cd2003428e74e37"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jakeasmith/http_build_url/zipball/93c273e77cb1edead0cf8bcf8cd2003428e74e37", "reference": "93c273e77cb1edead0cf8bcf8cd2003428e74e37", "shasum": ""}, "time": "2017-05-01T15:36:40+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/http_build_url.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality for http_build_url() to environments without pecl_http.", "support": {"issues": "https://github.com/jakeasmith/http_build_url/issues", "source": "https://github.com/jakeasmith/http_build_url"}, "install-path": "../jakeasmith/http_build_url"}], "dev": false, "dev-package-names": []}