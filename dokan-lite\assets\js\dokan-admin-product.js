!function(t){const n={vendorHtmlElement:null,searchVendors(e){t(e).each(function(){let e=t(this),o=t(e).data();n.vendorHtmlElement=t(e).selectWoo({closeOnSelect:!!o.close_on_select,minimumInputLength:o.minimum_input_length?o.minimum_input_length:"0",ajax:{url:dokan_admin_product.ajaxurl,dataType:"json",delay:250,data:function(t){return{action:o.action,_wpnonce:dokan_admin_product.nonce,s:t.term}},processResults:function(t,n){return n.page=n.page||1,{results:t.data.vendors,pagination:{more:!1}}},cache:!1},language:{errorLoading:function(){return dokan_admin_product.i18n.error_loading},searching:function(){return dokan_admin_product.i18n.searching+"..."},inputTooShort:function(){return dokan_admin_product.i18n.input_too_short+"..."}},escapeMarkup:function(t){return t},templateResult:function(t){return t.loading?t.text:"<div class='dokan_product_author_override-results clearfix'><div class='dokan_product_author_override__avatar'><img src='"+t.avatar+"' /></div><div class='dokan_product_author_override__title'>"+t.text+"</div></div>"},templateSelection:function(t){return t.text}})})},hideVendorIfSubscriptionProduct(){let e=t("#product-type").val(),o=t(".dokan_product_author_override").closest("div.postbox");if("product_pack"===e){let e=t(".dokan_product_author_override").data("data")[0];o.slideUp(),n.vendorHtmlElement.val(e.id?String(e.id):"0").trigger("change")}else o.slideDown()},init(){this.searchVendors(".dokan_product_author_override"),this.hideVendorIfSubscriptionProduct(),t("#product-type").on("change",this.hideVendorIfSubscriptionProduct)}};t(function(){n.init()})}(jQuery);