!function(e){let a=e("#dokan-product-category-modal"),t=e("#dokan-cat-search-res"),o=e("#dokan-cat-search-res-ul"),n=e(".dokan-single-categories-right"),c=e(".dokan-single-categories-left"),d=[],s=[],i="",l=0,r="";var g={init(){e("body").on("click",".dokan-product-category-li",this.categoryLiClick),e("body").on("click",".dokan-cat-search-res-li",this.clickSearchResLi),e("body").on("keyup","#dokan-single-cat-search-input",g.debounce(this.typeToSearch,500)),e("body").on("scroll","#dokan-single-categories",this.categoryScroll),e("body").on("click",".dokan-single-categories-right-box",g.indicatorScrollTo),e("body").on("click",".dokan-single-categories-left-box",function(){g.indicatorScrollTo(!1)}),e("body").on("click",".dokan-single-cat-select-btn",g.chooseCatButton),e("body").on("click",".dokan-category-open-modal",g.initModal),e("body").on("click","#dokan-category-close-modal",g.hideCategoryModal),e("body").on("click",".dokan-single-cat-add-btn",g.addANewCatBox),e("body").on("click",".dokan-select-product-category-remove-container",g.removeCatBox)},initModal(){i=e(this).data("dokansclevel"),l=e(this).data("selectfor");let a=e(this).siblings(".dokan-cat-inputs-holder").find(".dokan_chosen_product_cat");e(this).parent().attr("data-activate","yes"),g.openModal(a)},removeCatBox(){e(this).closest(".dokan-select-product-category-container")[0].remove(),wp.hooks.doAction("dokan_removed_multistep_category")},categoryLiClick(){let{catlevel:a,termId:t,name:o,haschild:n}=e(this).data();r=t,g.removeAfterClickedUls(a,t),g.loadChildCategories(a,t,o,n)},clickSearchResLi(){let{termid:a,index:t,name:o}=e(this).data();a&&(r=a,g.setCatUiBasedOnOneCat(a,s[t]))},typeToSearch(){let a=e(this).val();e("#dokan-cat-search-text-limit").html(a.length),a.length>0?(g.hideSearchResults(!1),g.doSearchCates(a)):g.hideSearchResults()},categoryScroll(){let a=e("#dokan-single-categories").get(0).scrollWidth-e("#dokan-single-categories").innerWidth(),t=e(this).scrollLeft(),o=a-t;g.showIndicators(c,t),g.showIndicators(n,o)},chooseCatButton(){let a=`.dokan-select-product-category-container.dokan_select_cat_for_${l}_${i}[data-activate='yes']`;e(a).parent().children(".dokan-select-product-category-container").children(".dokan-cat-inputs-holder").find(`.dokan_chosen_product_cat_${r}`).length?dokan_sweetalert(dokan_product_category_data.i18n.duplicate_category,{icon:"warning"}):(g.setCatName(g.getSelectedLabel(),e(a)),g.setCatId(r,e(a)),g.hideCategoryModal(),wp.hooks.doAction("dokan_selected_multistep_category",r),e(a).attr("data-activate","no"))},setCatUiBasedOnOneCat:function(e,a){let t=void 0!==a.children.length&&a.children.length>0;g.disableDoneBtn(t);let o=[...a.parents],n=[...a.parents];o.unshift(0),n.push(Number(e));let c=o.map((e,a)=>g.getCategoriesWithParentId(e,a+1,n[a]));d=c,g.updateCategoryUi(),g.hideSearchResults(),g.scrollTo(c.length)},async doSearchCates(e){let a=[];for(const t in dokan_product_category_data.categories){let o=dokan_product_category_data.categories[t],n=o.name;e=e.toLowerCase(),n.toLowerCase().indexOf(e)>=0&&a.push(o)}s=a,g.updateSearchResultUi()},hideSearchResults(e=!0){e?t.addClass("dokan-hide"):t.removeClass("dokan-hide")},showIndicators(e,a){a>5?e.removeClass("dokan-hide"):e.addClass("dokan-hide")},showCategoryModal(){r="",g.disableDoneBtn(),a.css("display","flex"),g.hideSearchResults(),e("#dokan-single-cat-search-input").val(""),d=[],g.loadAllParentCategories()},disableDoneBtn(a=!0){e(".dokan-single-cat-select-btn").prop("disabled",a)},hideCategoryModal(){a.css("display","none"),e(".dokan-select-product-category-container").attr("data-activate","no")},loadAllParentCategories(){d.push(g.getCategoriesWithParentId()),g.updateCategoryUi()},getCategoriesWithParentId(e=0,a=1,t=!1){let o=[];for(const a in dokan_product_category_data.categories){let n=dokan_product_category_data.categories[a];n.parent_id==e&&(n.uiActivaion=Number(n.term_id)===t&&"dokan-product-category-li-active",o.push(n))}return o.sort((e,a)=>e.name.toLowerCase()>a.name.toLowerCase()?1:a.name.toLowerCase()>e.name.toLowerCase()?-1:0),{categories:o,level:a,term_id:e}},loadChildCategories(e,a,t,o){const n=dokan_product_category_data.any_category_selection;if(o&&!0!==Boolean(n)?g.disableDoneBtn():g.disableDoneBtn(!1),o){let t=g.getCategoriesWithParentId(a,e+1);d.push(t),g.updateCategoryUi(),g.scrollTo(e)}},updateSearchResultUi(){let e="";e=s.map((e,a)=>`<li data-name="${e.name}" data-termid="${e.term_id}" data-index="${a}" class="dokan-cat-search-res-li">\n                        <div class="dokan-cat-search-res-item">\n                            ${e.name}\n                        </div>\n                        <div class="dokan-cat-search-res-history">\n                            ${g.getSearchedParentHistory(e.parents,e.name)}\n                        </div>\n                    </li>`),0==s.length&&(e=`<li data-name="" data-termid="" data-index="" class="dokan-cat-search-res-li">\n                        <div class="dokan-cat-search-res-item">\n                            ${window.dokan.i18n_no_result_found}\n                        </div>\n                        <div class="dokan-cat-search-res-history">\n                        </div>\n                    </li>`),o.html(e)},getSearchedParentHistory(e,a){let t="";return t=e.map((e,a)=>`<span class="dokan-cat-search-res-suggestion">${g.findCategory(e).name}</span>\n                    <span class="dokan-cat-search-res-indicator"><i class="fas fa-caret-right"></i></span>`).join(""),t+=`<span class="dokan-cat-search-res-suggestion-selected">${g.highlight(a)}</span>`,t},highlight(a){let t=e("#dokan-single-cat-search-input").val().toLowerCase(),o=a.toLowerCase().indexOf(t);if(o>=0)return`<span>${a.substring(0,o)}</span>\n                    <span class='dokan-cat-highlight'>${a.substring(o,o+t.length)}</span>\n                    <span>${a.substring(o+t.length)}</span>`},updateCategoryUi(){let a=g.getCatUlHtml();e("#dokan-single-categories").html(a),g.updateSelectedLabel(),g.adjustCategoryPosition()},updateSelectedLabel(){e("#dokan-selected-category-span").html(g.getSelectedLabel())},adjustCategoryPosition(){e.each(e(".dokan-product-category-ul").find(".dokan-product-category-li-active"),function(a,t){let{catlevel:o,indexli:n}=e(t).data();e(`#${o}-level-cat-ul`).scrollTop(36.38*n)})},getSelectedLabel(){let a=e(".dokan-product-category-li-active"),t=a.length,o="";return a.each((e,a)=>{var n=a.dataset;o+=`<span class="dokan-selected-category-product ${t==e+1?"dokan-cat-selected":""}">${n.name}</span>\n                ${t!=e+1?'<span class="dokan-selected-category-icon"><i class="fas fa-chevron-right"></i></span>':""}`}),o},updateCategorySelection(e,a){let t=d[e-1].categories.map(e=>(e.term_id==a?e.uiActivaion="dokan-product-category-li-active":e.uiActivaion="",e));d[e-1].categories=t,g.updateCategoryUi()},getCatUlHtml:()=>d.map((e,a)=>{let t=g.getCatLiHtml(e.categories,e.level);return`<ul id="${e.level}-level-cat-ul" class="dokan-product-category-ul ${e.level}-level-cat" data-level="${e.level}">${t}</ul>`}),getCatLiHtml(e,a){let t="";return e.forEach((e,o)=>{t+=`<li data-indexli="${o}" data-haschild="${e.children.length>0}" data-name="${e.name}" data-catLevel="${a}" class="${e.uiActivaion?e.uiActivaion:""} dokan-product-category-li ${e.children.length>0?"dokan-cat-has-child":""}" data-term-id="${e.term_id}" data-taxonomy="product_cat">\n                        <span class="dokan-product-category">${e.name}</span>\n                        <span class="dokan-product-category-icon"><i class="fas fa-chevron-right"></i></span>\n                    </li>`}),t},removeAfterClickedUls(e,a){let t=d.filter(a=>{if(a.level<=e)return a});d=t,g.updateCategorySelection(e,a)},scrollTo(a=0){e("#dokan-single-categories").animate({scrollLeft:300*a},800)},indicatorScrollTo(a=!0){e("#dokan-single-categories").animate({scrollLeft:(a?"+":"-")+"=350px"},800)},setCatId(e,a){let t=`<input data-field-name="chosen_product_cat" type="hidden" class="dokan_chosen_product_cat dokan_chosen_product_cat_${e}" name="chosen_product_cat[]" value="${e}"></input>`;t+=`<input type="hidden" name="chosen_product_cat_bulk[]" value="${e}"></input>`,a.children(`.dokan-cih-level-${i}`).html(t)},setCatName(e,a){a.children(".dokan-select-product-category").children(`.dokan-ssct-level-${i}`).html(e)},addANewCatBox(){let a=e(this)[0],t=e(a).data("selectfor");l=t;let o=e(this).parent().siblings(".dokan-add-new-cat-box").children(".dokan-select-product-category-container").length,n=e(this).parent().siblings(".dokan-add-new-cat-box").children(".dokan-select-product-category-container")[o-1],c=e(n).find("#dokan-category-open-modal").data("dokansclevel")+1;isNaN(c)&&(c=0);let d=`\n                <div data-activate="no" class="dokan-select-product-category-container dokan_select_cat_for_${t}_${c}">\n                    <div class="dokan-form-group dokan-select-product-category dokan-category-open-modal" data-dokansclevel="${c}" id="dokan-category-open-modal" data-selectfor="${t}">\n                        <span id="dokan_product_cat_res" class="dokan-select-product-category-title dokan-ssct-level-${c}">- ${dokan_product_category_data.i18n.select_a_category} -</span>\n                        <span class="dokan-select-product-category-icon"><i class="fas fa-edit"></i></span>\n                    </div>\n                        ${dokan_product_category_data.is_single?"":'\n                        <div class="dokan-select-product-category-remove-container">\n                            <span class="dokan-select-product-category-remove"><i class="fas fa-times"></i></span>\n                        </div>'}\n                    <span class="dokan-cat-inputs-holder dokan-cih-level-${c}" ></span>\n                </div>\n                `;e(this).parent().parent().children(`.cat_box_for_${t}`).append(d)},findCategory:e=>dokan_product_category_data.categories[e],debounce(e,a,t){var o;return function(){var n=this,c=arguments,d=t&&!o;clearTimeout(o),o=setTimeout(function(){o=null,t||e.apply(n,c)},a),d&&e.apply(n,c)}},openModal(e){if(g.showCategoryModal(),e.length>0){let a=e.val();r=a;let t=dokan_product_category_data.categories[a];g.setCatUiBasedOnOneCat(a,t)}}};e(document).ready(function(){g.init()})}(jQuery);