[type=text],input:where(:not([type])),[type=email],[type=url],[type=password],[type=number],[type=date],[type=datetime-local],[type=month],[type=search],[type=tel],[type=time],[type=week],[multiple],textarea,select{-webkit-appearance:none;-moz-appearance:none;appearance:none;background-color:#fff;border-color:var(--colors-gray-500);border-width:1px;border-radius:0;padding:.5rem .75rem;font-size:1rem;line-height:1.5rem;--tw-shadow: 0 0 #0000}[type=text]:focus,input:where(:not([type])):focus,[type=email]:focus,[type=url]:focus,[type=password]:focus,[type=number]:focus,[type=date]:focus,[type=datetime-local]:focus,[type=month]:focus,[type=search]:focus,[type=tel]:focus,[type=time]:focus,[type=week]:focus,[multiple]:focus,textarea:focus,select:focus{outline:2px solid transparent;outline-offset:2px;--tw-ring-inset: var(--tw-empty, );--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: var(--colors-blue-600);--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);border-color:var(--colors-blue-600)}input::-moz-placeholder, textarea::-moz-placeholder{color:var(--colors-gray-500);opacity:1}input::placeholder,textarea::placeholder{color:var(--colors-gray-500);opacity:1}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-date-and-time-value{min-height:1.5em;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field{padding-top:0;padding-bottom:0}select{background-image:url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 20 20%27%3e%3cpath stroke=%27var%28--colors-gray-500%29%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%271.5%27 d=%27M6 8l4 4 4-4%27/%3e%3c/svg%3e");background-position:right .5rem center;background-repeat:no-repeat;background-size:1.5em 1.5em;padding-right:2.5rem;-webkit-print-color-adjust:exact;print-color-adjust:exact}[multiple],[size]:where(select:not([size="1"])){background-image:initial;background-position:initial;background-repeat:unset;background-size:initial;padding-right:.75rem;-webkit-print-color-adjust:unset;print-color-adjust:unset}[type=checkbox],[type=radio]{-webkit-appearance:none;-moz-appearance:none;appearance:none;padding:0;-webkit-print-color-adjust:exact;print-color-adjust:exact;display:inline-block;vertical-align:middle;background-origin:border-box;-webkit-user-select:none;-moz-user-select:none;user-select:none;flex-shrink:0;height:1rem;width:1rem;color:var(--colors-blue-600);background-color:#fff;border-color:var(--colors-gray-500);border-width:1px;--tw-shadow: 0 0 #0000}[type=checkbox]{border-radius:0}[type=radio]{border-radius:100%}[type=checkbox]:focus,[type=radio]:focus{outline:2px solid transparent;outline-offset:2px;--tw-ring-inset: var(--tw-empty, );--tw-ring-offset-width: 2px;--tw-ring-offset-color: #fff;--tw-ring-color: var(--colors-blue-600);--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}[type=checkbox]:checked,[type=radio]:checked{border-color:transparent;background-color:currentColor;background-size:100% 100%;background-position:center;background-repeat:no-repeat}[type=checkbox]:checked{background-image:url("data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3e%3c/svg%3e")}@media (forced-colors: active){[type=checkbox]:checked{-webkit-appearance:auto;-moz-appearance:auto;appearance:auto}}[type=radio]:checked{background-image:url("data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e")}@media (forced-colors: active){[type=radio]:checked{-webkit-appearance:auto;-moz-appearance:auto;appearance:auto}}[type=checkbox]:checked:hover,[type=checkbox]:checked:focus,[type=radio]:checked:hover,[type=radio]:checked:focus{border-color:transparent;background-color:currentColor}[type=checkbox]:indeterminate{background-image:url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 16%27%3e%3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27M4 8h8%27/%3e%3c/svg%3e");border-color:transparent;background-color:currentColor;background-size:100% 100%;background-position:center;background-repeat:no-repeat}@media (forced-colors: active){[type=checkbox]:indeterminate{-webkit-appearance:auto;-moz-appearance:auto;appearance:auto}}[type=checkbox]:indeterminate:hover,[type=checkbox]:indeterminate:focus{border-color:transparent;background-color:currentColor}[type=file]{background:unset;border-color:inherit;border-width:0;border-radius:0;padding:0;font-size:unset;line-height:inherit}[type=file]:focus{outline:1px solid ButtonText;outline:1px auto -webkit-focus-ring-color}:root{--colors-primary-50: #EFEAFF;--colors-primary-100: #CEBEFC;--colors-primary-200: #BFACF9;--colors-primary-300: #AB92F6;--colors-primary-400: #997AF3;--colors-primary-500: #8460EF;--colors-primary-600: #7047EB;--colors-primary-700: #5B32DA;--colors-primary-800: #461ACA;--colors-primary-900: #370EB1;--colors-danger-50: #F8E3E6;--colors-danger-100: #F0C5C9;--colors-danger-200: #E2A1A9;--colors-danger-300: #E68F99;--colors-danger-400: #DE6F7A;--colors-danger-500: #E64B5F;--colors-danger-600: #CD414C;--colors-danger-700: #B92F37;--colors-danger-800: #9F2225;--colors-danger-900: #7F191C;--colors-gray-50: #F8F9F8;--colors-gray-100: #F1F1F4;--colors-gray-200: #E9E9E9;--colors-gray-300: #D3D3D3;--colors-gray-400: #A5A5AA;--colors-gray-500: #828282;--colors-gray-600: #5B5B60;--colors-gray-700: #575757;--colors-gray-800: #393939;--colors-gray-900: #25252D;--colors-yellow-50: #FAEDCD;--colors-yellow-100: #F7E5B6;--colors-yellow-200: #F3DCA3;--colors-yellow-300: #F1D690;--colors-yellow-400: #EDCD7E;--colors-yellow-500: #EBC569;--colors-yellow-600: #EFBB40;--colors-yellow-700: #E4B54C;--colors-yellow-800: #DBA940;--colors-yellow-900: #D29E37;--colors-green-50: #E8F5F3;--colors-green-100: #D4FBEF;--colors-green-200: #BBF7D0;--colors-green-300: #88E2C5;--colors-green-400: #22C55E;--colors-green-500: #35B535;--colors-green-600: #00BC8B;--colors-green-700: #008864;--colors-green-800: #00563F;--colors-green-900: #013220;--colors-red-50: #F8E3E6;--colors-red-100: #F0C5C9;--colors-red-200: #E2A1A9;--colors-red-300: #E68F99;--colors-red-400: #DE6F7A;--colors-red-500: #E64B5F;--colors-red-600: #CD414C;--colors-red-700: #B92F37;--colors-red-800: #9F2225;--colors-red-900: #7F191C;--colors-blue-50: #EFF6FF;--colors-blue-100: #DBEAFE;--colors-blue-200: #BFDBFE;--colors-blue-300: #93C5FD;--colors-blue-400: #60A5FA;--colors-blue-500: #3B82F6;--colors-blue-600: #2563EB;--colors-blue-700: #1D4ED8;--colors-blue-800: #2947BF;--colors-blue-900: #1E3A8A;--colors-pink-50: #FDF2F8;--colors-pink-100: #FCE7F3;--colors-pink-200: #FBCFE8;--colors-pink-300: #F9A8D4;--colors-pink-400: #ECA6C0;--colors-pink-500: #EC4899;--colors-pink-600: #DB2777;--colors-pink-700: #BE185D;--colors-pink-800: #9D174D;--colors-pink-900: #831843;--colors-amber-50: #fffbeb;--colors-amber-100: #fef3c7;--colors-amber-200: #fde68a;--colors-amber-300: #fcd34d;--colors-amber-400: #fbbf24;--colors-amber-500: #f59e0b;--colors-amber-600: #d97706;--colors-amber-700: #b45309;--colors-amber-800: #92400e;--colors-amber-900: #78350f;--colors-amber-950: #451a03;--colors-cyan-50: #ecfeff;--colors-cyan-100: #cffafe;--colors-cyan-200: #a5f3fc;--colors-cyan-300: #67e8f9;--colors-cyan-400: #22d3ee;--colors-cyan-500: #06b6d4;--colors-cyan-600: #0891b2;--colors-cyan-700: #0e7490;--colors-cyan-800: #155e75;--colors-cyan-900: #164e63;--colors-cyan-950: #083344;--colors-emerald-50: #ecfdf5;--colors-emerald-100: #d1fae5;--colors-emerald-200: #a7f3d0;--colors-emerald-300: #6ee7b7;--colors-emerald-400: #34d399;--colors-emerald-500: #10b981;--colors-emerald-600: #059669;--colors-emerald-700: #047857;--colors-emerald-800: #065f46;--colors-emerald-900: #064e3b;--colors-emerald-950: #022c22;--colors-fuchsia-50: #fdf4ff;--colors-fuchsia-100: #fae8ff;--colors-fuchsia-200: #f5d0fe;--colors-fuchsia-300: #f0abfc;--colors-fuchsia-400: #e879f9;--colors-fuchsia-500: #d946ef;--colors-fuchsia-600: #c026d3;--colors-fuchsia-700: #a21caf;--colors-fuchsia-800: #86198f;--colors-fuchsia-900: #701a75;--colors-fuchsia-950: #4a044e;--colors-indigo-50: #eef2ff;--colors-indigo-100: #e0e7ff;--colors-indigo-200: #c7d2fe;--colors-indigo-300: #a5b4fc;--colors-indigo-400: #818cf8;--colors-indigo-500: #6366f1;--colors-indigo-600: #4f46e5;--colors-indigo-700: #4338ca;--colors-indigo-800: #3730a3;--colors-indigo-900: #312e81;--colors-indigo-950: #1e1b4b;--colors-lime-50: #f7fee7;--colors-lime-100: #ecfccb;--colors-lime-200: #d9f99d;--colors-lime-300: #bef264;--colors-lime-400: #a3e635;--colors-lime-500: #84cc16;--colors-lime-600: #65a30d;--colors-lime-700: #4d7c0f;--colors-lime-800: #3f6212;--colors-lime-900: #365314;--colors-lime-950: #1a2e05;--colors-orange-50: #fff7ed;--colors-orange-100: #ffedd5;--colors-orange-200: #fed7aa;--colors-orange-300: #fdba74;--colors-orange-400: #fb923c;--colors-orange-500: #f97316;--colors-orange-600: #ea580c;--colors-orange-700: #c2410c;--colors-orange-800: #9a3412;--colors-orange-900: #7c2d12;--colors-orange-950: #431407;--colors-purple-50: #faf5ff;--colors-purple-100: #f3e8ff;--colors-purple-200: #e9d5ff;--colors-purple-300: #d8b4fe;--colors-purple-400: #c084fc;--colors-purple-500: #a855f7;--colors-purple-600: #9333ea;--colors-purple-700: #7e22ce;--colors-purple-800: #6b21a8;--colors-purple-900: #581c87;--colors-purple-950: #3b0764;--colors-rose-50: #fff1f2;--colors-rose-100: #ffe4e6;--colors-rose-200: #fecdd3;--colors-rose-300: #fda4af;--colors-rose-400: #fb7185;--colors-rose-500: #f43f5e;--colors-rose-600: #e11d48;--colors-rose-700: #be123c;--colors-rose-800: #9f1239;--colors-rose-900: #881337;--colors-rose-950: #4c0519;--colors-sky-50: #f0f9ff;--colors-sky-100: #e0f2fe;--colors-sky-200: #bae6fd;--colors-sky-300: #7dd3fc;--colors-sky-400: #38bdf8;--colors-sky-500: #0ea5e9;--colors-sky-600: #0284c7;--colors-sky-700: #0369a1;--colors-sky-800: #075985;--colors-sky-900: #0c4a6e;--colors-sky-950: #082f49;--colors-teal-50: #f0fdfa;--colors-teal-100: #ccfbf1;--colors-teal-200: #99f6e4;--colors-teal-300: #5eead4;--colors-teal-400: #2dd4bf;--colors-teal-500: #14b8a6;--colors-teal-600: #0d9488;--colors-teal-700: #0f766e;--colors-teal-800: #115e59;--colors-teal-900: #134e4a;--colors-teal-950: #042f2e;--colors-violet-50: #f5f3ff;--colors-violet-100: #ede9fe;--colors-violet-200: #ddd6fe;--colors-violet-300: #c4b5fd;--colors-violet-400: #a78bfa;--colors-violet-500: #8b5cf6;--colors-violet-600: #7c3aed;--colors-violet-700: #6d28d9;--colors-violet-800: #5b21b6;--colors-violet-900: #4c1d95;--colors-violet-950: #2e1065}*,:before,:after{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(147 197 253 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: }::backdrop{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(147 197 253 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: }.container{width:100%}@media (min-width: 640px){.container{max-width:640px}}@media (min-width: 768px){.container{max-width:768px}}@media (min-width: 1024px){.container{max-width:1024px}}@media (min-width: 1280px){.container{max-width:1280px}}@media (min-width: 1536px){.container{max-width:1536px}}.dokan-layout :is(.sr-only){position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}.dokan-layout :is(.pointer-events-none){pointer-events:none}.dokan-layout :is(.pointer-events-auto){pointer-events:auto}.dokan-layout :is(.visible){visibility:visible}.dokan-layout :is(.collapse){visibility:collapse}.dokan-layout :is(.fixed){position:fixed}.dokan-layout :is(.absolute){position:absolute}.dokan-layout :is(.relative){position:relative}.dokan-layout :is(.inset-0){top:0;right:0;bottom:0;left:0}.dokan-layout :is(.inset-y-0){top:0;bottom:0}.dokan-layout :is(.-left-12){left:-3rem}.dokan-layout :is(.-right-12){right:-3rem}.dokan-layout :is(.left-0){left:0}.dokan-layout :is(.left-2){left:.5rem}.dokan-layout :is(.right-0){right:0}.dokan-layout :is(.right-2){right:.5rem}.dokan-layout :is(.right-2\.5){right:.625rem}.dokan-layout :is(.top-0){top:0}.dokan-layout :is(.top-1\/2){top:50%}.dokan-layout :is(.top-2){top:.5rem}.dokan-layout :is(.z-10){z-index:10}.dokan-layout :is(.z-20){z-index:20}.dokan-layout :is(.z-40){z-index:40}.dokan-layout :is(.z-50){z-index:50}.dokan-layout :is(.-mx-1){margin-left:-.25rem;margin-right:-.25rem}.dokan-layout :is(.-mx-1\.5){margin-left:-.375rem;margin-right:-.375rem}.dokan-layout :is(.-my-1){margin-top:-.25rem;margin-bottom:-.25rem}.dokan-layout :is(.-my-1\.5){margin-top:-.375rem;margin-bottom:-.375rem}.dokan-layout :is(.mx-4){margin-left:1rem;margin-right:1rem}.dokan-layout :is(.mx-auto){margin-left:auto;margin-right:auto}.dokan-layout :is(.my-1){margin-top:.25rem;margin-bottom:.25rem}.dokan-layout :is(.my-2){margin-top:.5rem;margin-bottom:.5rem}.dokan-layout :is(.-ml-px){margin-left:-1px}.dokan-layout :is(.mb-1){margin-bottom:.25rem}.dokan-layout :is(.mb-2){margin-bottom:.5rem}.dokan-layout :is(.me-3){margin-inline-end:.75rem}.dokan-layout :is(.ml-1){margin-left:.25rem}.dokan-layout :is(.ml-2){margin-left:.5rem}.dokan-layout :is(.ml-3){margin-left:.75rem}.dokan-layout :is(.ml-auto){margin-left:auto}.dokan-layout :is(.mr-1){margin-right:.25rem}.dokan-layout :is(.ms-0){margin-inline-start:0px}.dokan-layout :is(.ms-0\.5){margin-inline-start:.125rem}.dokan-layout :is(.ms-1){margin-inline-start:.25rem}.dokan-layout :is(.ms-2){margin-inline-start:.5rem}.dokan-layout :is(.ms-2\.5){margin-inline-start:.625rem}.dokan-layout :is(.ms-3){margin-inline-start:.75rem}.dokan-layout :is(.mt-1){margin-top:.25rem}.dokan-layout :is(.mt-1\.5){margin-top:.375rem}.dokan-layout :is(.mt-2){margin-top:.5rem}.dokan-layout :is(.mt-2\.5){margin-top:.625rem}.dokan-layout :is(.mt-3){margin-top:.75rem}.dokan-layout :is(.mt-4){margin-top:1rem}.dokan-layout :is(.mt-5){margin-top:1.25rem}.dokan-layout :is(.mt-6){margin-top:1.5rem}.dokan-layout :is(.block){display:block}.dokan-layout :is(.inline-block){display:inline-block}.dokan-layout :is(.flex){display:flex}.dokan-layout :is(.inline-flex){display:inline-flex}.dokan-layout :is(.contents){display:contents}.dokan-layout :is(.hidden){display:none}.dokan-layout :is(.h-10){height:2.5rem}.dokan-layout :is(.h-3){height:.75rem}.dokan-layout :is(.h-3\.5){height:.875rem}.dokan-layout :is(.h-4){height:1rem}.dokan-layout :is(.h-5){height:1.25rem}.dokan-layout :is(.h-6){height:1.5rem}.dokan-layout :is(.h-96){height:24rem}.dokan-layout :is(.h-\[30px\]){height:30px}.dokan-layout :is(.h-\[var\(--radix-select-trigger-height\)\]){height:var(--radix-select-trigger-height)}.dokan-layout :is(.h-full){height:100%}.dokan-layout :is(.h-px){height:1px}.dokan-layout :is(.h-screen){height:100vh}.dokan-layout :is(.max-h-60){max-height:15rem}.dokan-layout :is(.min-h-screen){min-height:100vh}.dokan-layout :is(.w-0){width:0px}.dokan-layout :is(.w-10){width:2.5rem}.dokan-layout :is(.w-3){width:.75rem}.dokan-layout :is(.w-3\.5){width:.875rem}.dokan-layout :is(.w-32){width:8rem}.dokan-layout :is(.w-4){width:1rem}.dokan-layout :is(.w-5){width:1.25rem}.dokan-layout :is(.w-6){width:1.5rem}.dokan-layout :is(.w-72){width:18rem}.dokan-layout :is(.w-80){width:20rem}.dokan-layout :is(.w-\[--radix-select-trigger-width\]){width:var(--radix-select-trigger-width)}.dokan-layout :is(.w-full){width:100%}.dokan-layout :is(.min-w-\[8rem\]){min-width:8rem}.dokan-layout :is(.min-w-\[var\(--radix-select-trigger-width\)\]){min-width:var(--radix-select-trigger-width)}.dokan-layout :is(.max-w-2xl){max-width:42rem}.dokan-layout :is(.max-w-md){max-width:28rem}.dokan-layout :is(.max-w-xl){max-width:36rem}.dokan-layout :is(.max-w-xs){max-width:20rem}.dokan-layout :is(.flex-1){flex:1 1 0%}.dokan-layout :is(.flex-none){flex:none}.dokan-layout :is(.flex-shrink-0){flex-shrink:0}.dokan-layout :is(.flex-grow){flex-grow:1}.dokan-layout :is(.-translate-x-full){--tw-translate-x: -100%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.dokan-layout :is(.-translate-y-1\/2){--tw-translate-y: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.dokan-layout :is(.translate-x-0){--tw-translate-x: 0px;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.dokan-layout :is(.translate-x-1){--tw-translate-x: .25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.dokan-layout :is(.translate-x-6){--tw-translate-x: 1.5rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.dokan-layout :is(.translate-x-full){--tw-translate-x: 100%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.dokan-layout :is(.rotate-0){--tw-rotate: 0deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.dokan-layout :is(.scale-100){--tw-scale-x: 1;--tw-scale-y: 1;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.dokan-layout :is(.scale-95){--tw-scale-x: .95;--tw-scale-y: .95;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.dokan-layout :is(.transform){transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}@keyframes spin{to{transform:rotate(360deg)}}.dokan-layout :is(.animate-spin){animation:spin 1s linear infinite}.dokan-layout :is(.cursor-default){cursor:default}.dokan-layout :is(.cursor-not-allowed){cursor:not-allowed}.dokan-layout :is(.cursor-pointer){cursor:pointer}.dokan-layout :is(.select-none){-webkit-user-select:none;-moz-user-select:none;user-select:none}.dokan-layout :is(.appearance-none){-webkit-appearance:none;-moz-appearance:none;appearance:none}.dokan-layout :is(.flex-col){flex-direction:column}.dokan-layout :is(.flex-wrap){flex-wrap:wrap}.dokan-layout :is(.items-start){align-items:flex-start}.dokan-layout :is(.items-center){align-items:center}.dokan-layout :is(.items-stretch){align-items:stretch}.dokan-layout :is(.justify-start){justify-content:flex-start}.dokan-layout :is(.justify-end){justify-content:flex-end}.dokan-layout :is(.justify-center){justify-content:center}.dokan-layout :is(.justify-between){justify-content:space-between}.dokan-layout :is(.gap-1){gap:.25rem}.dokan-layout :is(.gap-2){gap:.5rem}.dokan-layout :is(.space-x-1>:not([hidden])~:not([hidden])){--tw-space-x-reverse: 0;margin-right:calc(.25rem * var(--tw-space-x-reverse));margin-left:calc(.25rem * calc(1 - var(--tw-space-x-reverse)))}.dokan-layout :is(.space-x-1\.5>:not([hidden])~:not([hidden])){--tw-space-x-reverse: 0;margin-right:calc(.375rem * var(--tw-space-x-reverse));margin-left:calc(.375rem * calc(1 - var(--tw-space-x-reverse)))}.dokan-layout :is(.space-x-4>:not([hidden])~:not([hidden])){--tw-space-x-reverse: 0;margin-right:calc(1rem * var(--tw-space-x-reverse));margin-left:calc(1rem * calc(1 - var(--tw-space-x-reverse)))}.dokan-layout :is(.space-y-0>:not([hidden])~:not([hidden])){--tw-space-y-reverse: 0;margin-top:calc(0px * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0px * var(--tw-space-y-reverse))}.dokan-layout :is(.space-y-1>:not([hidden])~:not([hidden])){--tw-space-y-reverse: 0;margin-top:calc(.25rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.25rem * var(--tw-space-y-reverse))}.dokan-layout :is(.space-y-5>:not([hidden])~:not([hidden])){--tw-space-y-reverse: 0;margin-top:calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1.25rem * var(--tw-space-y-reverse))}.dokan-layout :is(.self-center){align-self:center}.dokan-layout :is(.overflow-auto){overflow:auto}.dokan-layout :is(.overflow-hidden){overflow:hidden}.dokan-layout :is(.overflow-y-auto){overflow-y:auto}.dokan-layout :is(.whitespace-nowrap){white-space:nowrap}.dokan-layout :is(.rounded){border-radius:5px}.dokan-layout :is(.rounded-\[4px\]){border-radius:4px}.dokan-layout :is(.rounded-full){border-radius:9999px}.dokan-layout :is(.rounded-lg){border-radius:.5rem}.dokan-layout :is(.rounded-md){border-radius:.375rem}.dokan-layout :is(.rounded-none){border-radius:0}.dokan-layout :is(.rounded-sm){border-radius:.125rem}.dokan-layout :is(.rounded-l){border-top-left-radius:5px;border-bottom-left-radius:5px}.dokan-layout :is(.rounded-l-none){border-top-left-radius:0;border-bottom-left-radius:0}.dokan-layout :is(.rounded-r){border-top-right-radius:5px;border-bottom-right-radius:5px}.dokan-layout :is(.rounded-r-lg){border-top-right-radius:.5rem;border-bottom-right-radius:.5rem}.dokan-layout :is(.rounded-r-none){border-top-right-radius:0;border-bottom-right-radius:0}.dokan-layout :is(.rounded-bl){border-bottom-left-radius:5px}.dokan-layout :is(.rounded-br){border-bottom-right-radius:5px}.dokan-layout :is(.rounded-tl){border-top-left-radius:5px}.dokan-layout :is(.rounded-tr){border-top-right-radius:5px}.dokan-layout :is(.\!border){border-width:1px!important}.dokan-layout :is(.\!border-2){border-width:2px!important}.dokan-layout :is(.border){border-width:1px}.dokan-layout :is(.border-0){border-width:0px}.dokan-layout :is(.border-b){border-bottom-width:1px}.dokan-layout :is(.border-l){border-left-width:1px}.dokan-layout :is(.border-l-2){border-left-width:2px}.dokan-layout :is(.border-r-0){border-right-width:0px}.dokan-layout :is(.border-t){border-top-width:1px}.dokan-layout :is(.border-none){border-style:none}.dokan-layout :is(.\!border-danger-500){border-color:var(--colors-danger-500)!important}.dokan-layout :is(.\!border-gray-200){border-color:var(--colors-gray-200)!important}.dokan-layout :is(.\!border-primary-500){border-color:var(--colors-primary-500)!important}.dokan-layout :is(.border-amber-600){border-color:var(--colors-amber-600)}.dokan-layout :is(.border-blue-600){border-color:var(--colors-blue-600)}.dokan-layout :is(.border-cyan-600){border-color:var(--colors-cyan-600)}.dokan-layout :is(.border-danger-600){border-color:var(--colors-danger-600)}.dokan-layout :is(.border-emerald-600){border-color:var(--colors-emerald-600)}.dokan-layout :is(.border-fuchsia-600){border-color:var(--colors-fuchsia-600)}.dokan-layout :is(.border-gray-200){border-color:var(--colors-gray-200)}.dokan-layout :is(.border-gray-300){border-color:var(--colors-gray-300)}.dokan-layout :is(.border-gray-600){border-color:var(--colors-gray-600)}.dokan-layout :is(.border-green-600){border-color:var(--colors-green-600)}.dokan-layout :is(.border-indigo-600){border-color:var(--colors-indigo-600)}.dokan-layout :is(.border-lime-600){border-color:var(--colors-lime-600)}.dokan-layout :is(.border-orange-600){border-color:var(--colors-orange-600)}.dokan-layout :is(.border-pink-600){--tw-border-opacity: 1;border-color:rgb(219 39 119 / var(--tw-border-opacity))}.dokan-layout :is(.border-primary-400){border-color:var(--colors-primary-400)}.dokan-layout :is(.border-primary-600){border-color:var(--colors-primary-600)}.dokan-layout :is(.border-purple-600){border-color:var(--colors-purple-600)}.dokan-layout :is(.border-red-300){border-color:var(--colors-red-300)}.dokan-layout :is(.border-red-600){border-color:var(--colors-red-600)}.dokan-layout :is(.border-rose-600){border-color:var(--colors-rose-600)}.dokan-layout :is(.border-sky-600){border-color:var(--colors-sky-600)}.dokan-layout :is(.border-teal-600){border-color:var(--colors-teal-600)}.dokan-layout :is(.border-transparent){border-color:transparent}.dokan-layout :is(.border-violet-600){border-color:var(--colors-violet-600)}.dokan-layout :is(.border-yellow-600){border-color:var(--colors-yellow-600)}.dokan-layout :is(.bg-\[\#EAEAEA\]){--tw-bg-opacity: 1;background-color:rgb(234 234 234 / var(--tw-bg-opacity))}.dokan-layout :is(.bg-amber-200){background-color:var(--colors-amber-200)}.dokan-layout :is(.bg-amber-50){background-color:var(--colors-amber-50)}.dokan-layout :is(.bg-amber-500){background-color:var(--colors-amber-500)}.dokan-layout :is(.bg-amber-600){background-color:var(--colors-amber-600)}.dokan-layout :is(.bg-black){--tw-bg-opacity: 1;background-color:rgb(0 0 0 / var(--tw-bg-opacity))}.dokan-layout :is(.bg-blue-100){background-color:var(--colors-blue-100)}.dokan-layout :is(.bg-blue-200){background-color:var(--colors-blue-200)}.dokan-layout :is(.bg-blue-50){background-color:var(--colors-blue-50)}.dokan-layout :is(.bg-blue-500){background-color:var(--colors-blue-500)}.dokan-layout :is(.bg-blue-600){background-color:var(--colors-blue-600)}.dokan-layout :is(.bg-cyan-200){background-color:var(--colors-cyan-200)}.dokan-layout :is(.bg-cyan-50){background-color:var(--colors-cyan-50)}.dokan-layout :is(.bg-cyan-500){background-color:var(--colors-cyan-500)}.dokan-layout :is(.bg-cyan-600){background-color:var(--colors-cyan-600)}.dokan-layout :is(.bg-danger-200){background-color:var(--colors-danger-200)}.dokan-layout :is(.bg-danger-500){background-color:var(--colors-danger-500)}.dokan-layout :is(.bg-danger-600){background-color:var(--colors-danger-600)}.dokan-layout :is(.bg-emerald-200){background-color:var(--colors-emerald-200)}.dokan-layout :is(.bg-emerald-50){background-color:var(--colors-emerald-50)}.dokan-layout :is(.bg-emerald-500){background-color:var(--colors-emerald-500)}.dokan-layout :is(.bg-emerald-600){background-color:var(--colors-emerald-600)}.dokan-layout :is(.bg-fuchsia-200){background-color:var(--colors-fuchsia-200)}.dokan-layout :is(.bg-fuchsia-50){background-color:var(--colors-fuchsia-50)}.dokan-layout :is(.bg-fuchsia-500){background-color:var(--colors-fuchsia-500)}.dokan-layout :is(.bg-fuchsia-600){background-color:var(--colors-fuchsia-600)}.dokan-layout :is(.bg-gray-100){background-color:var(--colors-gray-100)}.dokan-layout :is(.bg-gray-200){background-color:var(--colors-gray-200)}.dokan-layout :is(.bg-gray-50){background-color:var(--colors-gray-50)}.dokan-layout :is(.bg-gray-500){background-color:var(--colors-gray-500)}.dokan-layout :is(.bg-gray-600){background-color:var(--colors-gray-600)}.dokan-layout :is(.bg-green-200){background-color:var(--colors-green-200)}.dokan-layout :is(.bg-green-50){background-color:var(--colors-green-50)}.dokan-layout :is(.bg-green-500){background-color:var(--colors-green-500)}.dokan-layout :is(.bg-green-600){background-color:var(--colors-green-600)}.dokan-layout :is(.bg-indigo-200){background-color:var(--colors-indigo-200)}.dokan-layout :is(.bg-indigo-50){background-color:var(--colors-indigo-50)}.dokan-layout :is(.bg-indigo-500){background-color:var(--colors-indigo-500)}.dokan-layout :is(.bg-indigo-600){background-color:var(--colors-indigo-600)}.dokan-layout :is(.bg-lime-200){background-color:var(--colors-lime-200)}.dokan-layout :is(.bg-lime-50){background-color:var(--colors-lime-50)}.dokan-layout :is(.bg-lime-500){background-color:var(--colors-lime-500)}.dokan-layout :is(.bg-lime-600){background-color:var(--colors-lime-600)}.dokan-layout :is(.bg-orange-200){background-color:var(--colors-orange-200)}.dokan-layout :is(.bg-orange-50){background-color:var(--colors-orange-50)}.dokan-layout :is(.bg-orange-500){background-color:var(--colors-orange-500)}.dokan-layout :is(.bg-orange-600){background-color:var(--colors-orange-600)}.dokan-layout :is(.bg-pink-200){--tw-bg-opacity: 1;background-color:rgb(251 207 232 / var(--tw-bg-opacity))}.dokan-layout :is(.bg-pink-50){--tw-bg-opacity: 1;background-color:rgb(253 242 248 / var(--tw-bg-opacity))}.dokan-layout :is(.bg-pink-500){--tw-bg-opacity: 1;background-color:rgb(236 72 153 / var(--tw-bg-opacity))}.dokan-layout :is(.bg-pink-600){--tw-bg-opacity: 1;background-color:rgb(219 39 119 / var(--tw-bg-opacity))}.dokan-layout :is(.bg-primary-100){background-color:var(--colors-primary-100)}.dokan-layout :is(.bg-primary-200){background-color:var(--colors-primary-200)}.dokan-layout :is(.bg-primary-50){background-color:var(--colors-primary-50)}.dokan-layout :is(.bg-primary-500){background-color:var(--colors-primary-500)}.dokan-layout :is(.bg-primary-600){background-color:var(--colors-primary-600)}.dokan-layout :is(.bg-purple-200){background-color:var(--colors-purple-200)}.dokan-layout :is(.bg-purple-50){background-color:var(--colors-purple-50)}.dokan-layout :is(.bg-purple-500){background-color:var(--colors-purple-500)}.dokan-layout :is(.bg-purple-600){background-color:var(--colors-purple-600)}.dokan-layout :is(.bg-red-200){background-color:var(--colors-red-200)}.dokan-layout :is(.bg-red-50){background-color:var(--colors-red-50)}.dokan-layout :is(.bg-red-500){background-color:var(--colors-red-500)}.dokan-layout :is(.bg-red-600){background-color:var(--colors-red-600)}.dokan-layout :is(.bg-rose-200){background-color:var(--colors-rose-200)}.dokan-layout :is(.bg-rose-50){background-color:var(--colors-rose-50)}.dokan-layout :is(.bg-rose-500){background-color:var(--colors-rose-500)}.dokan-layout :is(.bg-rose-600){background-color:var(--colors-rose-600)}.dokan-layout :is(.bg-sky-200){background-color:var(--colors-sky-200)}.dokan-layout :is(.bg-sky-50){background-color:var(--colors-sky-50)}.dokan-layout :is(.bg-sky-500){background-color:var(--colors-sky-500)}.dokan-layout :is(.bg-sky-600){background-color:var(--colors-sky-600)}.dokan-layout :is(.bg-slate-300){--tw-bg-opacity: 1;background-color:rgb(203 213 225 / var(--tw-bg-opacity))}.dokan-layout :is(.bg-teal-200){background-color:var(--colors-teal-200)}.dokan-layout :is(.bg-teal-50){background-color:var(--colors-teal-50)}.dokan-layout :is(.bg-teal-500){background-color:var(--colors-teal-500)}.dokan-layout :is(.bg-teal-600){background-color:var(--colors-teal-600)}.dokan-layout :is(.bg-transparent){background-color:transparent}.dokan-layout :is(.bg-violet-200){background-color:var(--colors-violet-200)}.dokan-layout :is(.bg-violet-50){background-color:var(--colors-violet-50)}.dokan-layout :is(.bg-violet-500){background-color:var(--colors-violet-500)}.dokan-layout :is(.bg-violet-600){background-color:var(--colors-violet-600)}.dokan-layout :is(.bg-white){--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity))}.dokan-layout :is(.bg-yellow-200){background-color:var(--colors-yellow-200)}.dokan-layout :is(.bg-yellow-50){background-color:var(--colors-yellow-50)}.dokan-layout :is(.bg-yellow-500){background-color:var(--colors-yellow-500)}.dokan-layout :is(.bg-yellow-600){background-color:var(--colors-yellow-600)}.dokan-layout :is(.bg-opacity-75){--tw-bg-opacity: .75}.dokan-layout :is(.fill-black){fill:#000}.dokan-layout :is(.p-0){padding:0}.dokan-layout :is(.p-1){padding:.25rem}.dokan-layout :is(.p-1\.5){padding:.375rem}.dokan-layout :is(.p-2){padding:.5rem}.dokan-layout :is(.p-4){padding:1rem}.dokan-layout :is(.p-6){padding:1.5rem}.dokan-layout :is(.px-1){padding-left:.25rem;padding-right:.25rem}.dokan-layout :is(.px-1\.5){padding-left:.375rem;padding-right:.375rem}.dokan-layout :is(.px-2){padding-left:.5rem;padding-right:.5rem}.dokan-layout :is(.px-3){padding-left:.75rem;padding-right:.75rem}.dokan-layout :is(.px-4){padding-left:1rem;padding-right:1rem}.dokan-layout :is(.px-5){padding-left:1.25rem;padding-right:1.25rem}.dokan-layout :is(.px-6){padding-left:1.5rem;padding-right:1.5rem}.dokan-layout :is(.py-1){padding-top:.25rem;padding-bottom:.25rem}.dokan-layout :is(.py-1\.5){padding-top:.375rem;padding-bottom:.375rem}.dokan-layout :is(.py-2){padding-top:.5rem;padding-bottom:.5rem}.dokan-layout :is(.py-2\.5){padding-top:.625rem;padding-bottom:.625rem}.dokan-layout :is(.py-4){padding-top:1rem;padding-bottom:1rem}.dokan-layout :is(.pb-0){padding-bottom:0}.dokan-layout :is(.pb-4){padding-bottom:1rem}.dokan-layout :is(.pe-2){padding-inline-end:.5rem}.dokan-layout :is(.pe-3){padding-inline-end:.75rem}.dokan-layout :is(.pl-0){padding-left:0}.dokan-layout :is(.pl-10){padding-left:2.5rem}.dokan-layout :is(.pl-3){padding-left:.75rem}.dokan-layout :is(.pr-2){padding-right:.5rem}.dokan-layout :is(.pr-3){padding-right:.75rem}.dokan-layout :is(.ps-3){padding-inline-start:.75rem}.dokan-layout :is(.ps-8){padding-inline-start:2rem}.dokan-layout :is(.pt-0){padding-top:0}.dokan-layout :is(.pt-0\.5){padding-top:.125rem}.dokan-layout :is(.pt-2){padding-top:.5rem}.dokan-layout :is(.pt-5){padding-top:1.25rem}.dokan-layout :is(.text-left){text-align:left}.dokan-layout :is(.text-center){text-align:center}.dokan-layout :is(.align-middle){vertical-align:middle}.dokan-layout :is(.text-base){font-size:1rem;line-height:1.5rem}.dokan-layout :is(.text-lg){font-size:1.125rem;line-height:1.75rem}.dokan-layout :is(.text-sm){font-size:.875rem;line-height:1.25rem}.dokan-layout :is(.text-xl){font-size:1.25rem;line-height:1.75rem}.dokan-layout :is(.text-xs){font-size:.75rem;line-height:1rem}.dokan-layout :is(.font-medium){font-weight:500}.dokan-layout :is(.font-semibold){font-weight:600}.dokan-layout :is(.uppercase){text-transform:uppercase}.dokan-layout :is(.leading-4){line-height:1rem}.dokan-layout :is(.leading-5){line-height:1.25rem}.dokan-layout :is(.leading-\[21px\]){line-height:21px}.dokan-layout :is(.leading-none){line-height:1}.dokan-layout :is(.text-\[\#4F4F4F\]){--tw-text-opacity: 1;color:rgb(79 79 79 / var(--tw-text-opacity))}.dokan-layout :is(.text-\[\#575757\]){--tw-text-opacity: 1;color:rgb(87 87 87 / var(--tw-text-opacity))}.dokan-layout :is(.text-amber-400){color:var(--colors-amber-400)}.dokan-layout :is(.text-amber-500){color:var(--colors-amber-500)}.dokan-layout :is(.text-amber-600){color:var(--colors-amber-600)}.dokan-layout :is(.text-amber-800){color:var(--colors-amber-800)}.dokan-layout :is(.text-amber-900){color:var(--colors-amber-900)}.dokan-layout :is(.text-blue-400){color:var(--colors-blue-400)}.dokan-layout :is(.text-blue-500){color:var(--colors-blue-500)}.dokan-layout :is(.text-blue-600){color:var(--colors-blue-600)}.dokan-layout :is(.text-blue-800){color:var(--colors-blue-800)}.dokan-layout :is(.text-blue-900){color:var(--colors-blue-900)}.dokan-layout :is(.text-cyan-400){color:var(--colors-cyan-400)}.dokan-layout :is(.text-cyan-500){color:var(--colors-cyan-500)}.dokan-layout :is(.text-cyan-600){color:var(--colors-cyan-600)}.dokan-layout :is(.text-cyan-800){color:var(--colors-cyan-800)}.dokan-layout :is(.text-cyan-900){color:var(--colors-cyan-900)}.dokan-layout :is(.text-danger-400){color:var(--colors-danger-400)}.dokan-layout :is(.text-danger-500){color:var(--colors-danger-500)}.dokan-layout :is(.text-danger-600){color:var(--colors-danger-600)}.dokan-layout :is(.text-danger-900){color:var(--colors-danger-900)}.dokan-layout :is(.text-emerald-400){color:var(--colors-emerald-400)}.dokan-layout :is(.text-emerald-500){color:var(--colors-emerald-500)}.dokan-layout :is(.text-emerald-600){color:var(--colors-emerald-600)}.dokan-layout :is(.text-emerald-800){color:var(--colors-emerald-800)}.dokan-layout :is(.text-emerald-900){color:var(--colors-emerald-900)}.dokan-layout :is(.text-fuchsia-400){color:var(--colors-fuchsia-400)}.dokan-layout :is(.text-fuchsia-500){color:var(--colors-fuchsia-500)}.dokan-layout :is(.text-fuchsia-600){color:var(--colors-fuchsia-600)}.dokan-layout :is(.text-fuchsia-800){color:var(--colors-fuchsia-800)}.dokan-layout :is(.text-fuchsia-900){color:var(--colors-fuchsia-900)}.dokan-layout :is(.text-gray-400){color:var(--colors-gray-400)}.dokan-layout :is(.text-gray-50){color:var(--colors-gray-50)}.dokan-layout :is(.text-gray-500){color:var(--colors-gray-500)}.dokan-layout :is(.text-gray-600){color:var(--colors-gray-600)}.dokan-layout :is(.text-gray-700){color:var(--colors-gray-700)}.dokan-layout :is(.text-gray-800){color:var(--colors-gray-800)}.dokan-layout :is(.text-gray-900){color:var(--colors-gray-900)}.dokan-layout :is(.text-green-400){color:var(--colors-green-400)}.dokan-layout :is(.text-green-500){color:var(--colors-green-500)}.dokan-layout :is(.text-green-600){color:var(--colors-green-600)}.dokan-layout :is(.text-green-800){color:var(--colors-green-800)}.dokan-layout :is(.text-green-900){color:var(--colors-green-900)}.dokan-layout :is(.text-indigo-400){color:var(--colors-indigo-400)}.dokan-layout :is(.text-indigo-500){color:var(--colors-indigo-500)}.dokan-layout :is(.text-indigo-600){color:var(--colors-indigo-600)}.dokan-layout :is(.text-indigo-800){color:var(--colors-indigo-800)}.dokan-layout :is(.text-indigo-900){color:var(--colors-indigo-900)}.dokan-layout :is(.text-lime-400){color:var(--colors-lime-400)}.dokan-layout :is(.text-lime-500){color:var(--colors-lime-500)}.dokan-layout :is(.text-lime-600){color:var(--colors-lime-600)}.dokan-layout :is(.text-lime-800){color:var(--colors-lime-800)}.dokan-layout :is(.text-lime-900){color:var(--colors-lime-900)}.dokan-layout :is(.text-orange-400){color:var(--colors-orange-400)}.dokan-layout :is(.text-orange-500){color:var(--colors-orange-500)}.dokan-layout :is(.text-orange-600){color:var(--colors-orange-600)}.dokan-layout :is(.text-orange-800){color:var(--colors-orange-800)}.dokan-layout :is(.text-orange-900){color:var(--colors-orange-900)}.dokan-layout :is(.text-pink-400){--tw-text-opacity: 1;color:rgb(244 114 182 / var(--tw-text-opacity))}.dokan-layout :is(.text-pink-500){--tw-text-opacity: 1;color:rgb(236 72 153 / var(--tw-text-opacity))}.dokan-layout :is(.text-pink-600){--tw-text-opacity: 1;color:rgb(219 39 119 / var(--tw-text-opacity))}.dokan-layout :is(.text-pink-800){--tw-text-opacity: 1;color:rgb(157 23 77 / var(--tw-text-opacity))}.dokan-layout :is(.text-pink-900){--tw-text-opacity: 1;color:rgb(131 24 67 / var(--tw-text-opacity))}.dokan-layout :is(.text-primary-400){color:var(--colors-primary-400)}.dokan-layout :is(.text-primary-500){color:var(--colors-primary-500)}.dokan-layout :is(.text-primary-600){color:var(--colors-primary-600)}.dokan-layout :is(.text-primary-800){color:var(--colors-primary-800)}.dokan-layout :is(.text-primary-900){color:var(--colors-primary-900)}.dokan-layout :is(.text-purple-400){color:var(--colors-purple-400)}.dokan-layout :is(.text-purple-500){color:var(--colors-purple-500)}.dokan-layout :is(.text-purple-600){color:var(--colors-purple-600)}.dokan-layout :is(.text-purple-800){color:var(--colors-purple-800)}.dokan-layout :is(.text-purple-900){color:var(--colors-purple-900)}.dokan-layout :is(.text-red-400){color:var(--colors-red-400)}.dokan-layout :is(.text-red-500){color:var(--colors-red-500)}.dokan-layout :is(.text-red-600){color:var(--colors-red-600)}.dokan-layout :is(.text-red-800){color:var(--colors-red-800)}.dokan-layout :is(.text-red-900){color:var(--colors-red-900)}.dokan-layout :is(.text-rose-400){color:var(--colors-rose-400)}.dokan-layout :is(.text-rose-500){color:var(--colors-rose-500)}.dokan-layout :is(.text-rose-600){color:var(--colors-rose-600)}.dokan-layout :is(.text-rose-800){color:var(--colors-rose-800)}.dokan-layout :is(.text-rose-900){color:var(--colors-rose-900)}.dokan-layout :is(.text-sky-400){color:var(--colors-sky-400)}.dokan-layout :is(.text-sky-500){color:var(--colors-sky-500)}.dokan-layout :is(.text-sky-600){color:var(--colors-sky-600)}.dokan-layout :is(.text-sky-800){color:var(--colors-sky-800)}.dokan-layout :is(.text-sky-900){color:var(--colors-sky-900)}.dokan-layout :is(.text-teal-400){color:var(--colors-teal-400)}.dokan-layout :is(.text-teal-500){color:var(--colors-teal-500)}.dokan-layout :is(.text-teal-600){color:var(--colors-teal-600)}.dokan-layout :is(.text-teal-800){color:var(--colors-teal-800)}.dokan-layout :is(.text-teal-900){color:var(--colors-teal-900)}.dokan-layout :is(.text-violet-400){color:var(--colors-violet-400)}.dokan-layout :is(.text-violet-500){color:var(--colors-violet-500)}.dokan-layout :is(.text-violet-600){color:var(--colors-violet-600)}.dokan-layout :is(.text-violet-800){color:var(--colors-violet-800)}.dokan-layout :is(.text-violet-900){color:var(--colors-violet-900)}.dokan-layout :is(.text-white){--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity))}.dokan-layout :is(.text-yellow-400){color:var(--colors-yellow-400)}.dokan-layout :is(.text-yellow-500){color:var(--colors-yellow-500)}.dokan-layout :is(.text-yellow-600){color:var(--colors-yellow-600)}.dokan-layout :is(.text-yellow-800){color:var(--colors-yellow-800)}.dokan-layout :is(.text-yellow-900){color:var(--colors-yellow-900)}.dokan-layout :is(.underline){text-decoration-line:underline}.dokan-layout :is(.decoration-2){text-decoration-thickness:2px}.dokan-layout :is(.placeholder-gray-400)::-moz-placeholder{color:var(--colors-gray-400)}.dokan-layout :is(.placeholder-gray-400)::placeholder{color:var(--colors-gray-400)}.dokan-layout :is(.placeholder-red-300)::-moz-placeholder{color:var(--colors-red-300)}.dokan-layout :is(.placeholder-red-300)::placeholder{color:var(--colors-red-300)}.dokan-layout :is(.opacity-0){opacity:0}.dokan-layout :is(.opacity-100){opacity:1}.dokan-layout :is(.opacity-50){opacity:.5}.dokan-layout :is(.opacity-70){opacity:.7}.dokan-layout :is(.shadow){--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.dokan-layout :is(.shadow-\[0_6px_20px_0_rgba\(0\,0\,0\,0\.08\)\]){--tw-shadow: 0 6px 20px 0 rgba(0,0,0,.08);--tw-shadow-colored: 0 6px 20px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.dokan-layout :is(.shadow-\[0px_2px_20px_rgb\(0\,0\,0\,0\.15\)\]){--tw-shadow: 0px 2px 20px rgb(0,0,0,.15);--tw-shadow-colored: 0px 2px 20px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.dokan-layout :is(.shadow-lg){--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.dokan-layout :is(.shadow-md){--tw-shadow: 0 4px 6px -1px rgb(0 0 0 / .1), 0 2px 4px -2px rgb(0 0 0 / .1);--tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.dokan-layout :is(.shadow-none){--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.dokan-layout :is(.shadow-sm){--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / .05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.dokan-layout :is(.shadow-xl){--tw-shadow: 0 20px 25px -5px rgb(0 0 0 / .1), 0 8px 10px -6px rgb(0 0 0 / .1);--tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.dokan-layout :is(.outline-none){outline:2px solid transparent;outline-offset:2px}.dokan-layout :is(.ring-0){--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.dokan-layout :is(.ring-1){--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.dokan-layout :is(.ring-2){--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.dokan-layout :is(.ring-\[\#E9E9E9\]){--tw-ring-opacity: 1;--tw-ring-color: rgb(233 233 233 / var(--tw-ring-opacity))}.dokan-layout :is(.ring-black){--tw-ring-opacity: 1;--tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity))}.dokan-layout :is(.ring-gray-200){--tw-ring-color: var(--colors-gray-200)}.dokan-layout :is(.ring-red-500){--tw-ring-color: var(--colors-red-500)}.dokan-layout :is(.ring-opacity-5){--tw-ring-opacity: .05}.dokan-layout :is(.filter){filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.dokan-layout :is(.backdrop-blur-sm){--tw-backdrop-blur: blur(4px);-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}.dokan-layout :is(.transition){transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.dokan-layout :is(.transition-all){transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.dokan-layout :is(.transition-colors){transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.dokan-layout :is(.transition-opacity){transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.dokan-layout :is(.transition-transform){transition-property:transform;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.dokan-layout :is(.duration-150){transition-duration:.15s}.dokan-layout :is(.duration-200){transition-duration:.2s}.dokan-layout :is(.duration-300){transition-duration:.3s}.dokan-layout :is(.ease-in){transition-timing-function:cubic-bezier(.4,0,1,1)}.dokan-layout :is(.ease-in-out){transition-timing-function:cubic-bezier(.4,0,.2,1)}.dokan-layout :is(.ease-linear){transition-timing-function:linear}.dokan-layout :is(.ease-out){transition-timing-function:cubic-bezier(0,0,.2,1)}.dokan-layout :is(.will-change-\[transform\,opacity\]){will-change:transform,opacity}.pac-container{border-top-width:0px;--tw-shadow: 0 4px 6px -1px rgb(0 0 0 / .1), 0 2px 4px -2px rgb(0 0 0 / .1);--tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.pac-container .pac-item{padding:.25rem 1rem}.pac-container:after{display:none}.react-select input:focus{border-style:none!important;--tw-shadow: 0 0 #0000 !important;--tw-shadow-colored: 0 0 #0000 !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important;outline:2px solid transparent!important;outline-offset:2px!important}.dokan-layout :is(.placeholder\:text-\[\#828282\])::-moz-placeholder{--tw-text-opacity: 1;color:rgb(130 130 130 / var(--tw-text-opacity))}.dokan-layout :is(.placeholder\:text-\[\#828282\])::placeholder{--tw-text-opacity: 1;color:rgb(130 130 130 / var(--tw-text-opacity))}.dokan-layout :is(.placeholder\:text-gray-300)::-moz-placeholder{color:var(--colors-gray-300)}.dokan-layout :is(.placeholder\:text-gray-300)::placeholder{color:var(--colors-gray-300)}.dokan-layout :is(.placeholder\:text-gray-400)::-moz-placeholder{color:var(--colors-gray-400)}.dokan-layout :is(.placeholder\:text-gray-400)::placeholder{color:var(--colors-gray-400)}.dokan-layout :is(.first\:rounded-t:first-child){border-top-left-radius:5px;border-top-right-radius:5px}.dokan-layout :is(.last\:rounded-b:last-child){border-bottom-right-radius:5px;border-bottom-left-radius:5px}.dokan-layout :is(.focus-within\:z-10:focus-within){z-index:10}.dokan-layout :is(.focus-within\:border-danger-500:focus-within){border-color:var(--colors-danger-500)}.dokan-layout :is(.focus-within\:border-primary-500:focus-within){border-color:var(--colors-primary-500)}.dokan-layout :is(.focus-within\:ring-1:focus-within){--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.dokan-layout :is(.focus-within\:ring-2:focus-within){--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.dokan-layout :is(.focus-within\:ring-danger-500:focus-within){--tw-ring-color: var(--colors-danger-500)}.dokan-layout :is(.focus-within\:ring-primary-500:focus-within){--tw-ring-color: var(--colors-primary-500)}.dokan-layout :is(.hover\:cursor-pointer:hover){cursor:pointer}.dokan-layout :is(.hover\:bg-amber-100:hover){background-color:var(--colors-amber-100)}.dokan-layout :is(.hover\:bg-amber-700:hover){background-color:var(--colors-amber-700)}.dokan-layout :is(.hover\:bg-blue-100:hover){background-color:var(--colors-blue-100)}.dokan-layout :is(.hover\:bg-blue-700:hover){background-color:var(--colors-blue-700)}.dokan-layout :is(.hover\:bg-cyan-100:hover){background-color:var(--colors-cyan-100)}.dokan-layout :is(.hover\:bg-cyan-700:hover){background-color:var(--colors-cyan-700)}.dokan-layout :is(.hover\:bg-danger-100:hover){background-color:var(--colors-danger-100)}.dokan-layout :is(.hover\:bg-danger-700:hover){background-color:var(--colors-danger-700)}.dokan-layout :is(.hover\:bg-emerald-100:hover){background-color:var(--colors-emerald-100)}.dokan-layout :is(.hover\:bg-emerald-700:hover){background-color:var(--colors-emerald-700)}.dokan-layout :is(.hover\:bg-fuchsia-100:hover){background-color:var(--colors-fuchsia-100)}.dokan-layout :is(.hover\:bg-fuchsia-700:hover){background-color:var(--colors-fuchsia-700)}.dokan-layout :is(.hover\:bg-gray-100:hover){background-color:var(--colors-gray-100)}.dokan-layout :is(.hover\:bg-gray-50:hover){background-color:var(--colors-gray-50)}.dokan-layout :is(.hover\:bg-gray-700:hover){background-color:var(--colors-gray-700)}.dokan-layout :is(.hover\:bg-green-100:hover){background-color:var(--colors-green-100)}.dokan-layout :is(.hover\:bg-green-700:hover){background-color:var(--colors-green-700)}.dokan-layout :is(.hover\:bg-indigo-100:hover){background-color:var(--colors-indigo-100)}.dokan-layout :is(.hover\:bg-indigo-700:hover){background-color:var(--colors-indigo-700)}.dokan-layout :is(.hover\:bg-lime-100:hover){background-color:var(--colors-lime-100)}.dokan-layout :is(.hover\:bg-lime-700:hover){background-color:var(--colors-lime-700)}.dokan-layout :is(.hover\:bg-orange-100:hover){background-color:var(--colors-orange-100)}.dokan-layout :is(.hover\:bg-orange-700:hover){background-color:var(--colors-orange-700)}.dokan-layout :is(.hover\:bg-pink-100:hover){--tw-bg-opacity: 1;background-color:rgb(252 231 243 / var(--tw-bg-opacity))}.dokan-layout :is(.hover\:bg-pink-700:hover){--tw-bg-opacity: 1;background-color:rgb(190 24 93 / var(--tw-bg-opacity))}.dokan-layout :is(.hover\:bg-primary-100:hover){background-color:var(--colors-primary-100)}.dokan-layout :is(.hover\:bg-primary-700:hover){background-color:var(--colors-primary-700)}.dokan-layout :is(.hover\:bg-purple-100:hover){background-color:var(--colors-purple-100)}.dokan-layout :is(.hover\:bg-purple-700:hover){background-color:var(--colors-purple-700)}.dokan-layout :is(.hover\:bg-red-100:hover){background-color:var(--colors-red-100)}.dokan-layout :is(.hover\:bg-red-700:hover){background-color:var(--colors-red-700)}.dokan-layout :is(.hover\:bg-rose-100:hover){background-color:var(--colors-rose-100)}.dokan-layout :is(.hover\:bg-rose-700:hover){background-color:var(--colors-rose-700)}.dokan-layout :is(.hover\:bg-sky-100:hover){background-color:var(--colors-sky-100)}.dokan-layout :is(.hover\:bg-sky-700:hover){background-color:var(--colors-sky-700)}.dokan-layout :is(.hover\:bg-teal-100:hover){background-color:var(--colors-teal-100)}.dokan-layout :is(.hover\:bg-teal-700:hover){background-color:var(--colors-teal-700)}.dokan-layout :is(.hover\:bg-violet-100:hover){background-color:var(--colors-violet-100)}.dokan-layout :is(.hover\:bg-violet-700:hover){background-color:var(--colors-violet-700)}.dokan-layout :is(.hover\:bg-yellow-100:hover){background-color:var(--colors-yellow-100)}.dokan-layout :is(.hover\:bg-yellow-700:hover){background-color:var(--colors-yellow-700)}.dokan-layout :is(.hover\:text-gray-700:hover){color:var(--colors-gray-700)}.dokan-layout :is(.hover\:text-gray-800:hover){color:var(--colors-gray-800)}.dokan-layout :is(.hover\:underline:hover){text-decoration-line:underline}.dokan-layout :is(.hover\:shadow-lg:hover){--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.dokan-layout :is(.focus\:border-gray-500:focus){border-color:var(--colors-gray-500)}.dokan-layout :is(.focus\:border-primary-500:focus){border-color:var(--colors-primary-500)}.dokan-layout :is(.focus\:border-red-500:focus){border-color:var(--colors-red-500)}.dokan-layout :is(.focus\:border-transparent:focus){border-color:transparent}.dokan-layout :is(.focus\:bg-gray-100:focus){background-color:var(--colors-gray-100)}.dokan-layout :is(.focus\:bg-primary-50:focus){background-color:var(--colors-primary-50)}.dokan-layout :is(.focus\:text-gray-900:focus){color:var(--colors-gray-900)}.dokan-layout :is(.focus\:text-primary-800:focus){color:var(--colors-primary-800)}.dokan-layout :is(.focus\:outline-none:focus){outline:2px solid transparent;outline-offset:2px}.dokan-layout :is(.focus\:ring-0:focus){--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.dokan-layout :is(.focus\:ring-2:focus){--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.dokan-layout :is(.focus\:ring-inset:focus){--tw-ring-inset: inset}.dokan-layout :is(.focus\:ring-amber-600:focus){--tw-ring-color: var(--colors-amber-600)}.dokan-layout :is(.focus\:ring-blue-600:focus){--tw-ring-color: var(--colors-blue-600)}.dokan-layout :is(.focus\:ring-cyan-600:focus){--tw-ring-color: var(--colors-cyan-600)}.dokan-layout :is(.focus\:ring-danger-600:focus){--tw-ring-color: var(--colors-danger-600)}.dokan-layout :is(.focus\:ring-emerald-600:focus){--tw-ring-color: var(--colors-emerald-600)}.dokan-layout :is(.focus\:ring-fuchsia-600:focus){--tw-ring-color: var(--colors-fuchsia-600)}.dokan-layout :is(.focus\:ring-gray-600:focus){--tw-ring-color: var(--colors-gray-600)}.dokan-layout :is(.focus\:ring-green-600:focus){--tw-ring-color: var(--colors-green-600)}.dokan-layout :is(.focus\:ring-indigo-500:focus){--tw-ring-color: var(--colors-indigo-500)}.dokan-layout :is(.focus\:ring-indigo-600:focus){--tw-ring-color: var(--colors-indigo-600)}.dokan-layout :is(.focus\:ring-lime-600:focus){--tw-ring-color: var(--colors-lime-600)}.dokan-layout :is(.focus\:ring-orange-600:focus){--tw-ring-color: var(--colors-orange-600)}.dokan-layout :is(.focus\:ring-pink-600:focus){--tw-ring-opacity: 1;--tw-ring-color: rgb(219 39 119 / var(--tw-ring-opacity))}.dokan-layout :is(.focus\:ring-primary-500:focus){--tw-ring-color: var(--colors-primary-500)}.dokan-layout :is(.focus\:ring-primary-600:focus){--tw-ring-color: var(--colors-primary-600)}.dokan-layout :is(.focus\:ring-purple-600:focus){--tw-ring-color: var(--colors-purple-600)}.dokan-layout :is(.focus\:ring-red-500:focus){--tw-ring-color: var(--colors-red-500)}.dokan-layout :is(.focus\:ring-red-600:focus){--tw-ring-color: var(--colors-red-600)}.dokan-layout :is(.focus\:ring-rose-600:focus){--tw-ring-color: var(--colors-rose-600)}.dokan-layout :is(.focus\:ring-sky-600:focus){--tw-ring-color: var(--colors-sky-600)}.dokan-layout :is(.focus\:ring-teal-600:focus){--tw-ring-color: var(--colors-teal-600)}.dokan-layout :is(.focus\:ring-violet-600:focus){--tw-ring-color: var(--colors-violet-600)}.dokan-layout :is(.focus\:ring-white:focus){--tw-ring-opacity: 1;--tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity))}.dokan-layout :is(.focus\:ring-yellow-600:focus){--tw-ring-color: var(--colors-yellow-600)}.dokan-layout :is(.focus\:ring-offset-2:focus){--tw-ring-offset-width: 2px}.dokan-layout :is(.focus\:ring-offset-amber-50:focus){--tw-ring-offset-color: var(--colors-amber-50)}.dokan-layout :is(.focus\:ring-offset-blue-50:focus){--tw-ring-offset-color: var(--colors-blue-50)}.dokan-layout :is(.focus\:ring-offset-cyan-50:focus){--tw-ring-offset-color: var(--colors-cyan-50)}.dokan-layout :is(.focus\:ring-offset-emerald-50:focus){--tw-ring-offset-color: var(--colors-emerald-50)}.dokan-layout :is(.focus\:ring-offset-fuchsia-50:focus){--tw-ring-offset-color: var(--colors-fuchsia-50)}.dokan-layout :is(.focus\:ring-offset-gray-50:focus){--tw-ring-offset-color: var(--colors-gray-50)}.dokan-layout :is(.focus\:ring-offset-green-50:focus){--tw-ring-offset-color: var(--colors-green-50)}.dokan-layout :is(.focus\:ring-offset-indigo-50:focus){--tw-ring-offset-color: var(--colors-indigo-50)}.dokan-layout :is(.focus\:ring-offset-lime-50:focus){--tw-ring-offset-color: var(--colors-lime-50)}.dokan-layout :is(.focus\:ring-offset-orange-50:focus){--tw-ring-offset-color: var(--colors-orange-50)}.dokan-layout :is(.focus\:ring-offset-pink-50:focus){--tw-ring-offset-color: #fdf2f8}.dokan-layout :is(.focus\:ring-offset-primary-50:focus){--tw-ring-offset-color: var(--colors-primary-50)}.dokan-layout :is(.focus\:ring-offset-purple-50:focus){--tw-ring-offset-color: var(--colors-purple-50)}.dokan-layout :is(.focus\:ring-offset-red-50:focus){--tw-ring-offset-color: var(--colors-red-50)}.dokan-layout :is(.focus\:ring-offset-rose-50:focus){--tw-ring-offset-color: var(--colors-rose-50)}.dokan-layout :is(.focus\:ring-offset-sky-50:focus){--tw-ring-offset-color: var(--colors-sky-50)}.dokan-layout :is(.focus\:ring-offset-teal-50:focus){--tw-ring-offset-color: var(--colors-teal-50)}.dokan-layout :is(.focus\:ring-offset-violet-50:focus){--tw-ring-offset-color: var(--colors-violet-50)}.dokan-layout :is(.focus\:ring-offset-yellow-50:focus){--tw-ring-offset-color: var(--colors-yellow-50)}.dokan-layout :is(.disabled\:cursor-not-allowed:disabled){cursor:not-allowed}.dokan-layout :is(.disabled\:opacity-50:disabled){opacity:.5}.dokan-layout :is(.peer:disabled~.peer-disabled\:cursor-not-allowed){cursor:not-allowed}.dokan-layout :is(.peer:disabled~.peer-disabled\:opacity-50){opacity:.5}.dokan-layout :is(.data-\[disabled\]\:pointer-events-none[data-disabled]){pointer-events:none}.dokan-layout :is(.data-\[side\=bottom\]\:translate-y-1[data-side=bottom]){--tw-translate-y: .25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.dokan-layout :is(.data-\[side\=left\]\:-translate-x-1[data-side=left]){--tw-translate-x: -.25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.dokan-layout :is(.data-\[side\=right\]\:translate-x-1[data-side=right]){--tw-translate-x: .25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.dokan-layout :is(.data-\[side\=top\]\:-translate-y-1[data-side=top]){--tw-translate-y: -.25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.dokan-layout :is(.data-\[disabled\]\:opacity-50[data-disabled]){opacity:.5}.dokan-layout :is(.data-\[state\=open\]\:ring-2[data-state=open]){--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.dokan-layout :is(.data-\[state\=open\]\:ring-primary-600[data-state=open]){--tw-ring-color: var(--colors-primary-600)}.dokan-layout :is(.group\/trigger[data-state=open] .group-data-\[state\=open\]\/trigger\:rotate-180){--tw-rotate: 180deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.dokan-layout :is(.group\/trigger[data-state=open] .group-data-\[state\=open\]\/trigger\:text-primary-600){color:var(--colors-primary-600)}@media (min-width: 640px){.dokan-layout :is(.sm\:inline-block){display:inline-block}.dokan-layout :is(.sm\:h-4){height:1rem}.dokan-layout :is(.sm\:w-4){width:1rem}.dokan-layout :is(.sm\:text-sm){font-size:.875rem;line-height:1.25rem}}

*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/*:where(.dokan-layout,.dokan-layout *),
:where(.dokan-layout,.dokan-layout *)::before,
:where(.dokan-layout,.dokan-layout *)::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}:where(.dokan-layout,.dokan-layout *)::before,
:where(.dokan-layout,.dokan-layout *)::after {
  --tw-content: '';
}/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/.dokan-layout {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/.dokan-layout {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/hr:where(.dokan-layout,.dokan-layout *) {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/abbr:where([title]):where(.dokan-layout,.dokan-layout *) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}/*
Remove the default font size and weight for headings.
*/h1:where(.dokan-layout,.dokan-layout *),
h2:where(.dokan-layout,.dokan-layout *),
h3:where(.dokan-layout,.dokan-layout *),
h4:where(.dokan-layout,.dokan-layout *),
h5:where(.dokan-layout,.dokan-layout *),
h6:where(.dokan-layout,.dokan-layout *) {
  font-size: inherit;
  font-weight: inherit;
}/*
Reset links to optimize for opt-in styling instead of opt-out.
*/a:where(.dokan-layout,.dokan-layout *) {
  color: inherit;
  text-decoration: inherit;
}/*
Add the correct font weight in Edge and Safari.
*/b:where(.dokan-layout,.dokan-layout *),
strong:where(.dokan-layout,.dokan-layout *) {
  font-weight: bolder;
}/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/code:where(.dokan-layout,.dokan-layout *),
kbd:where(.dokan-layout,.dokan-layout *),
samp:where(.dokan-layout,.dokan-layout *),
pre:where(.dokan-layout,.dokan-layout *) {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}/*
Add the correct font size in all browsers.
*/small:where(.dokan-layout,.dokan-layout *) {
  font-size: 80%;
}/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/sub:where(.dokan-layout,.dokan-layout *),
sup:where(.dokan-layout,.dokan-layout *) {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}sub:where(.dokan-layout,.dokan-layout *) {
  bottom: -0.25em;
}sup:where(.dokan-layout,.dokan-layout *) {
  top: -0.5em;
}/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/table:where(.dokan-layout,.dokan-layout *) {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/button:where(.dokan-layout,.dokan-layout *),
input:where(.dokan-layout,.dokan-layout *),
optgroup:where(.dokan-layout,.dokan-layout *),
select:where(.dokan-layout,.dokan-layout *),
textarea:where(.dokan-layout,.dokan-layout *) {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}/*
Remove the inheritance of text transform in Edge and Firefox.
*/button:where(.dokan-layout,.dokan-layout *),
select:where(.dokan-layout,.dokan-layout *) {
  text-transform: none;
}/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/button:where(.dokan-layout,.dokan-layout *),
input:where([type='button']):where(.dokan-layout,.dokan-layout *),
input:where([type='reset']):where(.dokan-layout,.dokan-layout *),
input:where([type='submit']):where(.dokan-layout,.dokan-layout *) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}/*
Use the modern Firefox focus style for all focusable elements.
*/:-moz-focusring:where(.dokan-layout,.dokan-layout *) {
  outline: auto;
}/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/:-moz-ui-invalid:where(.dokan-layout,.dokan-layout *) {
  box-shadow: none;
}/*
Add the correct vertical alignment in Chrome and Firefox.
*/progress:where(.dokan-layout,.dokan-layout *) {
  vertical-align: baseline;
}/*
Correct the cursor style of increment and decrement buttons in Safari.
*/:where(.dokan-layout,.dokan-layout *) ::-webkit-inner-spin-button,
:where(.dokan-layout,.dokan-layout *) ::-webkit-outer-spin-button {
  height: auto;
}/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/[type='search']:where(.dokan-layout,.dokan-layout *) {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}/*
Remove the inner padding in Chrome and Safari on macOS.
*/:where(.dokan-layout,.dokan-layout *) ::-webkit-search-decoration {
  -webkit-appearance: none;
}/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/:where(.dokan-layout,.dokan-layout *) ::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}/*
Add the correct display in Chrome and Safari.
*/summary:where(.dokan-layout,.dokan-layout *) {
  display: list-item;
}/*
Removes the default spacing and border for appropriate elements.
*/blockquote:where(.dokan-layout,.dokan-layout *),
dl:where(.dokan-layout,.dokan-layout *),
dd:where(.dokan-layout,.dokan-layout *),
h1:where(.dokan-layout,.dokan-layout *),
h2:where(.dokan-layout,.dokan-layout *),
h3:where(.dokan-layout,.dokan-layout *),
h4:where(.dokan-layout,.dokan-layout *),
h5:where(.dokan-layout,.dokan-layout *),
h6:where(.dokan-layout,.dokan-layout *),
hr:where(.dokan-layout,.dokan-layout *),
figure:where(.dokan-layout,.dokan-layout *),
p:where(.dokan-layout,.dokan-layout *),
pre:where(.dokan-layout,.dokan-layout *) {
  margin: 0;
}fieldset:where(.dokan-layout,.dokan-layout *) {
  margin: 0;
  padding: 0;
}legend:where(.dokan-layout,.dokan-layout *) {
  padding: 0;
}ol:where(.dokan-layout,.dokan-layout *),
ul:where(.dokan-layout,.dokan-layout *),
menu:where(.dokan-layout,.dokan-layout *) {
  list-style: none;
  margin: 0;
  padding: 0;
}/*
Reset default styling for dialogs.
*/dialog:where(.dokan-layout,.dokan-layout *) {
  padding: 0;
}/*
Prevent resizing textareas horizontally by default.
*/textarea:where(.dokan-layout,.dokan-layout *) {
  resize: vertical;
}/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/:where(.dokan-layout,.dokan-layout *) input::-moz-placeholder, :where(.dokan-layout,.dokan-layout *) textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}:where(.dokan-layout,.dokan-layout *) input::placeholder,
:where(.dokan-layout,.dokan-layout *) textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}/*
Set the default cursor for buttons.
*/button:where(.dokan-layout,.dokan-layout *),
[role="button"]:where(.dokan-layout,.dokan-layout *) {
  cursor: pointer;
}/*
Make sure disabled buttons don't get the pointer cursor.
*/:disabled:where(.dokan-layout,.dokan-layout *) {
  cursor: default;
}/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/img:where(.dokan-layout,.dokan-layout *),
svg:where(.dokan-layout,.dokan-layout *),
video:where(.dokan-layout,.dokan-layout *),
canvas:where(.dokan-layout,.dokan-layout *),
audio:where(.dokan-layout,.dokan-layout *),
iframe:where(.dokan-layout,.dokan-layout *),
embed:where(.dokan-layout,.dokan-layout *),
object:where(.dokan-layout,.dokan-layout *) {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/img:where(.dokan-layout,.dokan-layout *),
video:where(.dokan-layout,.dokan-layout *) {
  max-width: 100%;
  height: auto;
}/* Make elements with the HTML hidden attribute stay hidden by default */[hidden]:where(:not([hidden="until-found"])):where(.dokan-layout,.dokan-layout *) {
  display: none;
}[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select{
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #2563eb;
}input::-moz-placeholder, textarea::-moz-placeholder{
  color: #6b7280;
  opacity: 1;
}input::placeholder,textarea::placeholder{
  color: #6b7280;
  opacity: 1;
}::-webkit-datetime-edit-fields-wrapper{
  padding: 0;
}::-webkit-date-and-time-value{
  min-height: 1.5em;
  text-align: inherit;
}::-webkit-datetime-edit{
  display: inline-flex;
}::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field{
  padding-top: 0;
  padding-bottom: 0;
}select{
  background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 20 20%27%3e%3cpath stroke=%27%236b7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%271.5%27 d=%27M6 8l4 4 4-4%27/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}[multiple],[size]:where(select:not([size="1"])){
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: unset;
          print-color-adjust: unset;
}[type='checkbox'],[type='radio']{
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #2563eb;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}[type='checkbox']{
  border-radius: 0px;
}[type='radio']{
  border-radius: 100%;
}[type='checkbox']:focus,[type='radio']:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}[type='checkbox']:checked,[type='radio']:checked{
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}[type='checkbox']:checked{
  background-image: url("data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3e%3c/svg%3e");
}@media (forced-colors: active) {[type='checkbox']:checked{
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}[type='radio']:checked{
  background-image: url("data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e");
}@media (forced-colors: active) {[type='radio']:checked{
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus{
  border-color: transparent;
  background-color: currentColor;
}[type='checkbox']:indeterminate{
  background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 16%27%3e%3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27M4 8h8%27/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}@media (forced-colors: active) {[type='checkbox']:indeterminate{
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus{
  border-color: transparent;
  background-color: currentColor;
}[type='file']{
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}[type='file']:focus{
  outline: 1px solid ButtonText;
  outline: 1px auto -webkit-focus-ring-color;
}:root{--colors-primary-500: var(--dokan-button-border-color, #7047EB);--wp-components-color-accent: var(--dokan-button-background-color, #7047EB);--wp-components-color-accent-darker-20: var(--dokan-button-hover-background-color, #502BBF)}#headlessui-portal-root{display:none}#headlessui-portal-root.dokan-layout button[type=button]:is(.absolute.right-2.top-2){
  border-radius: 9999px;
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}#headlessui-portal-root.dokan-layout button[type=button]:is(.absolute.right-2.top-2):hover{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
  --tw-ring-offset-width: 2px;
}.dokan-layout table:not(.dataviews-view-table),.dokan-layout table:not(.dataviews-view-table) th,.dokan-layout table:not(.dataviews-view-table) td{margin:0;padding:0;border:0;border-spacing:0;border-collapse:collapse;font-size:inherit;font-weight:inherit;text-align:inherit;vertical-align:inherit;box-sizing:border-box}.dokan-layout a:focus:not([role=switch],[role=combobox]),.dokan-layout button:focus:not([role=switch],[role=combobox]),.dokan-layout .button.alt:focus:not([role=switch],[role=combobox]),.dokan-layout textarea:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=button]:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=reset]:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=submit]:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=tel]:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=url]:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=password]:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=search]:focus:not([role=switch],[role=combobox]){outline-color:var(--dokan-button-border-color, #7047EB)}.dokan-layout  a:not(.dokan-btn):not([class*=dokan-btn-],.skip-color-module){
  color: var(--dokan-link-color, #7047EB);
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}.dokan-layout  a:not(.dokan-btn):not([class*=dokan-btn-],.skip-color-module):hover,:is(.dokan-layout a:not(.dokan-btn):not([class*=dokan-btn-],.skip-color-module):focus){
  color: var(--dokan-link-hover-color, var(--dokan-sidebar-background-color, #322067));
}.dokan-layout  input[type=checkbox]::before{
  --tw-content: "" !important;
  content: var(--tw-content) !important;
}.dokan-layout textarea:focus,.dokan-layout input[type=text]:focus,.dokan-layout input[type=number]:focus{outline-offset:0}button[data-headlessui-state=checked]:hover,button[data-headlessui-state=checked]:focus{background-color:var(--dokan-button-background-color, #7047EB) !important}div[data-radix-popper-content-wrapper],div[data-headlessui-state=open][role=dialog]{z-index:999 !important}.container{
  width: 100%;
}@media (min-width: 640px){.container{
    max-width: 640px;
  }
}@media (min-width: 768px){.container{
    max-width: 768px;
  }
}@media (min-width: 1024px){.container{
    max-width: 1024px;
  }
}@media (min-width: 1280px){.container{
    max-width: 1280px;
  }
}@media (min-width: 1536px){.container{
    max-width: 1536px;
  }
}.dokan-layout .sr-only{
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}.dokan-layout .visible{
  visibility: visible;
}.dokan-layout .static{
  position: static;
}.dokan-layout .fixed{
  position: fixed;
}.dokan-layout .absolute{
  position: absolute;
}.dokan-layout .relative{
  position: relative;
}.dokan-layout .sticky{
  position: sticky;
}.dokan-layout .-start-6{
  inset-inline-start: -1.5rem;
}.dokan-layout .-top-2{
  top: -0.5rem;
}.dokan-layout .right-0{
  right: 0px;
}.dokan-layout .right-0\.5{
  right: 0.125rem;
}.dokan-layout .right-1{
  right: 0.25rem;
}.dokan-layout .right-3{
  right: 0.75rem;
}.dokan-layout .right-6{
  right: 1.5rem;
}.dokan-layout .top-0{
  top: 0px;
}.dokan-layout .top-0\.5{
  top: 0.125rem;
}.dokan-layout .top-1{
  top: 0.25rem;
}.dokan-layout .top-6{
  top: 1.5rem;
}.dokan-layout .top-full{
  top: 100%;
}.dokan-layout .z-0{
  z-index: 0;
}.dokan-layout .z-10{
  z-index: 10;
}.dokan-layout .z-40{
  z-index: 40;
}.dokan-layout .z-50{
  z-index: 50;
}.dokan-layout .col-span-12{
  grid-column: span 12 / span 12;
}.dokan-layout .col-span-4{
  grid-column: span 4 / span 4;
}.dokan-layout .-m-4{
  margin: -1rem;
}.dokan-layout .m-0{
  margin: 0px;
}.dokan-layout .mx-1{
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}.dokan-layout .mx-auto{
  margin-left: auto;
  margin-right: auto;
}.dokan-layout .my-4{
  margin-top: 1rem;
  margin-bottom: 1rem;
}.dokan-layout .my-6{
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}.dokan-layout .-mb-px{
  margin-bottom: -1px;
}.dokan-layout .-mt-10{
  margin-top: -2.5rem;
}.dokan-layout .mb-0{
  margin-bottom: 0px;
}.dokan-layout .mb-10{
  margin-bottom: 2.5rem;
}.dokan-layout .mb-12{
  margin-bottom: 3rem;
}.dokan-layout .mb-14{
  margin-bottom: 3.5rem;
}.dokan-layout .mb-2{
  margin-bottom: 0.5rem;
}.dokan-layout .mb-3{
  margin-bottom: 0.75rem;
}.dokan-layout .mb-4{
  margin-bottom: 1rem;
}.dokan-layout .mb-5{
  margin-bottom: 1.25rem;
}.dokan-layout .mb-6{
  margin-bottom: 1.5rem;
}.dokan-layout .mb-7{
  margin-bottom: 1.75rem;
}.dokan-layout .mb-8{
  margin-bottom: 2rem;
}.dokan-layout .ml-1{
  margin-left: 0.25rem;
}.dokan-layout .ml-2{
  margin-left: 0.5rem;
}.dokan-layout .ml-5{
  margin-left: 1.25rem;
}.dokan-layout .ml-auto{
  margin-left: auto;
}.dokan-layout .mr-2{
  margin-right: 0.5rem;
}.dokan-layout .mr-20{
  margin-right: 5rem;
}.dokan-layout .mr-4{
  margin-right: 1rem;
}.dokan-layout .mr-5{
  margin-right: 1.25rem;
}.dokan-layout .mr-\[20px\]{
  margin-right: 20px;
}.dokan-layout .mr-auto{
  margin-right: auto;
}.dokan-layout .ms-10{
  margin-inline-start: 2.5rem;
}.dokan-layout .mt-0{
  margin-top: 0px;
}.dokan-layout .mt-0\.5{
  margin-top: 0.125rem;
}.dokan-layout .mt-1{
  margin-top: 0.25rem;
}.dokan-layout .mt-1\.5{
  margin-top: 0.375rem;
}.dokan-layout .mt-10{
  margin-top: 2.5rem;
}.dokan-layout .mt-12{
  margin-top: 3rem;
}.dokan-layout .mt-16{
  margin-top: 4rem;
}.dokan-layout .mt-2{
  margin-top: 0.5rem;
}.dokan-layout .mt-3{
  margin-top: 0.75rem;
}.dokan-layout .mt-4{
  margin-top: 1rem;
}.dokan-layout .mt-5{
  margin-top: 1.25rem;
}.dokan-layout .mt-6{
  margin-top: 1.5rem;
}.dokan-layout .box-border{
  box-sizing: border-box;
}.dokan-layout .block{
  display: block;
}.dokan-layout .inline-block{
  display: inline-block;
}.dokan-layout .inline{
  display: inline;
}.dokan-layout .flex{
  display: flex;
}.dokan-layout .inline-flex{
  display: inline-flex;
}.dokan-layout .table{
  display: table;
}.dokan-layout .table-column{
  display: table-column;
}.dokan-layout .table-row{
  display: table-row;
}.dokan-layout .grid{
  display: grid;
}.dokan-layout .hidden{
  display: none;
}.dokan-layout .aspect-video{
  aspect-ratio: 16 / 9;
}.dokan-layout .size-5{
  width: 1.25rem;
  height: 1.25rem;
}.dokan-layout .h-1\.5{
  height: 0.375rem;
}.dokan-layout .h-1\/2{
  height: 50%;
}.dokan-layout .h-10{
  height: 2.5rem;
}.dokan-layout .h-12{
  height: 3rem;
}.dokan-layout .h-14{
  height: 3.5rem;
}.dokan-layout .h-16{
  height: 4rem;
}.dokan-layout .h-3{
  height: 0.75rem;
}.dokan-layout .h-3\.5{
  height: 0.875rem;
}.dokan-layout .h-36{
  height: 9rem;
}.dokan-layout .h-4{
  height: 1rem;
}.dokan-layout .h-48{
  height: 12rem;
}.dokan-layout .h-5{
  height: 1.25rem;
}.dokan-layout .h-6{
  height: 1.5rem;
}.dokan-layout .h-7{
  height: 1.75rem;
}.dokan-layout .h-8{
  height: 2rem;
}.dokan-layout .h-\[21rem\]{
  height: 21rem;
}.dokan-layout .h-\[25px\]{
  height: 25px;
}.dokan-layout .h-\[3rem\]{
  height: 3rem;
}.dokan-layout .h-\[48px\]{
  height: 48px;
}.dokan-layout .h-\[85px\]{
  height: 85px;
}.dokan-layout .h-full{
  height: 100%;
}.dokan-layout .max-h-\[280px\]{
  max-height: 280px;
}.dokan-layout .max-h-\[500px\]{
  max-height: 500px;
}.dokan-layout .\!min-h-\[max-content\]{
  min-height: -moz-max-content !important;
  min-height: max-content !important;
}.dokan-layout .min-h-32{
  min-height: 8rem;
}.dokan-layout .min-h-48{
  min-height: 12rem;
}.dokan-layout .min-h-\[194px\]{
  min-height: 194px;
}.dokan-layout .min-h-\[3rem\]{
  min-height: 3rem;
}.dokan-layout .min-h-screen{
  min-height: 100vh;
}.dokan-layout .w-1\.5{
  width: 0.375rem;
}.dokan-layout .w-1\/2{
  width: 50%;
}.dokan-layout .w-1\/3{
  width: 33.333333%;
}.dokan-layout .w-1\/4{
  width: 25%;
}.dokan-layout .w-10{
  width: 2.5rem;
}.dokan-layout .w-12{
  width: 3rem;
}.dokan-layout .w-14{
  width: 3.5rem;
}.dokan-layout .w-16{
  width: 4rem;
}.dokan-layout .w-2\/3{
  width: 66.666667%;
}.dokan-layout .w-20{
  width: 5rem;
}.dokan-layout .w-24{
  width: 6rem;
}.dokan-layout .w-28{
  width: 7rem;
}.dokan-layout .w-3\.5{
  width: 0.875rem;
}.dokan-layout .w-3\/4{
  width: 75%;
}.dokan-layout .w-3\/6{
  width: 50%;
}.dokan-layout .w-32{
  width: 8rem;
}.dokan-layout .w-36{
  width: 9rem;
}.dokan-layout .w-4{
  width: 1rem;
}.dokan-layout .w-4\/6{
  width: 66.666667%;
}.dokan-layout .w-44{
  width: 11rem;
}.dokan-layout .w-48{
  width: 12rem;
}.dokan-layout .w-5{
  width: 1.25rem;
}.dokan-layout .w-5\/6{
  width: 83.333333%;
}.dokan-layout .w-6{
  width: 1.5rem;
}.dokan-layout .w-64{
  width: 16rem;
}.dokan-layout .w-7{
  width: 1.75rem;
}.dokan-layout .w-8{
  width: 2rem;
}.dokan-layout .w-\[10\.5rem\]{
  width: 10.5rem;
}.dokan-layout .w-\[11rem\]{
  width: 11rem;
}.dokan-layout .w-\[137px\]{
  width: 137px;
}.dokan-layout .w-\[157px\]{
  width: 157px;
}.dokan-layout .w-\[169px\]{
  width: 169px;
}.dokan-layout .w-\[5px\]{
  width: 5px;
}.dokan-layout .w-auto{
  width: auto;
}.dokan-layout .w-fit{
  width: -moz-fit-content;
  width: fit-content;
}.dokan-layout .w-full{
  width: 100%;
}.dokan-layout .min-w-0{
  min-width: 0px;
}.dokan-layout .min-w-72{
  min-width: 18rem;
}.dokan-layout .min-w-\[25px\]{
  min-width: 25px;
}.dokan-layout .min-w-full{
  min-width: 100%;
}.dokan-layout .max-w-2xl{
  max-width: 42rem;
}.dokan-layout .max-w-3xl{
  max-width: 48rem;
}.dokan-layout .max-w-7xl{
  max-width: 80rem;
}.dokan-layout .max-w-\[20rem\]{
  max-width: 20rem;
}.dokan-layout .max-w-\[23rem\]{
  max-width: 23rem;
}.dokan-layout .max-w-\[24rem\]{
  max-width: 24rem;
}.dokan-layout .max-w-\[450px\]{
  max-width: 450px;
}.dokan-layout .max-w-\[46rem\]{
  max-width: 46rem;
}.dokan-layout .max-w-\[530px\]{
  max-width: 530px;
}.dokan-layout .max-w-\[720px\]{
  max-width: 720px;
}.dokan-layout .max-w-full{
  max-width: 100%;
}.dokan-layout .max-w-md{
  max-width: 28rem;
}.dokan-layout .max-w-sm{
  max-width: 24rem;
}.dokan-layout .max-w-xl{
  max-width: 36rem;
}.dokan-layout .flex-1{
  flex: 1 1 0%;
}.dokan-layout .flex-auto{
  flex: 1 1 auto;
}.dokan-layout .flex-shrink-0{
  flex-shrink: 0;
}.dokan-layout .shrink{
  flex-shrink: 1;
}.dokan-layout .rotate-45{
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.dokan-layout .scale-100{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.dokan-layout .scale-95{
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.dokan-layout .transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}@keyframes pulse{50%{
    opacity: .5;
  }
}.dokan-layout .animate-pulse{
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}@keyframes spin{to{
    transform: rotate(360deg);
  }
}.dokan-layout .animate-spin{
  animation: spin 1s linear infinite;
}.dokan-layout .cursor-pointer{
  cursor: pointer;
}.dokan-layout .resize{
  resize: both;
}.dokan-layout .grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}.dokan-layout .grid-cols-12{
  grid-template-columns: repeat(12, minmax(0, 1fr));
}.dokan-layout .grid-cols-4{
  grid-template-columns: repeat(4, minmax(0, 1fr));
}.dokan-layout .grid-cols-8{
  grid-template-columns: repeat(8, minmax(0, 1fr));
}.dokan-layout .flex-row{
  flex-direction: row;
}.dokan-layout .flex-col{
  flex-direction: column;
}.dokan-layout .flex-wrap{
  flex-wrap: wrap;
}.dokan-layout .items-start{
  align-items: flex-start;
}.dokan-layout .items-end{
  align-items: flex-end;
}.dokan-layout .items-center{
  align-items: center;
}.dokan-layout .justify-start{
  justify-content: flex-start;
}.dokan-layout .justify-end{
  justify-content: flex-end;
}.dokan-layout .justify-center{
  justify-content: center;
}.dokan-layout .justify-between{
  justify-content: space-between;
}.dokan-layout .gap-1{
  gap: 0.25rem;
}.dokan-layout .gap-1\.5{
  gap: 0.375rem;
}.dokan-layout .gap-2{
  gap: 0.5rem;
}.dokan-layout .gap-2\.5{
  gap: 0.625rem;
}.dokan-layout .gap-3{
  gap: 0.75rem;
}.dokan-layout .gap-4{
  gap: 1rem;
}.dokan-layout .gap-5{
  gap: 1.25rem;
}.dokan-layout .gap-6{
  gap: 1.5rem;
}.dokan-layout .gap-7{
  gap: 1.75rem;
}.dokan-layout .gap-x-1{
  -moz-column-gap: 0.25rem;
       column-gap: 0.25rem;
}.dokan-layout .gap-x-2{
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}.dokan-layout .gap-y-4{
  row-gap: 1rem;
}.dokan-layout :is(.space-x-1 > :not([hidden]) ~ :not([hidden])){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}.dokan-layout :is(.space-x-2 > :not([hidden]) ~ :not([hidden])){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}.dokan-layout :is(.space-x-3 > :not([hidden]) ~ :not([hidden])){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}.dokan-layout :is(.space-x-4 > :not([hidden]) ~ :not([hidden])){
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}.dokan-layout :is(.space-x-8 > :not([hidden]) ~ :not([hidden])){
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}.dokan-layout :is(.space-y-2 > :not([hidden]) ~ :not([hidden])){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}.dokan-layout :is(.space-y-2\.5 > :not([hidden]) ~ :not([hidden])){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.625rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.625rem * var(--tw-space-y-reverse));
}.dokan-layout :is(.space-y-3 > :not([hidden]) ~ :not([hidden])){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}.dokan-layout :is(.space-y-4 > :not([hidden]) ~ :not([hidden])){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}.dokan-layout :is(.space-y-6 > :not([hidden]) ~ :not([hidden])){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}.dokan-layout :is(.space-y-8 > :not([hidden]) ~ :not([hidden])){
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}.dokan-layout :is(.divide-y > :not([hidden]) ~ :not([hidden])){
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}.dokan-layout :is(.divide-gray-200 > :not([hidden]) ~ :not([hidden])){
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}.dokan-layout :is(.divide-gray-300 > :not([hidden]) ~ :not([hidden])){
  --tw-divide-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-divide-opacity, 1));
}.dokan-layout .self-center{
  align-self: center;
}.dokan-layout .overflow-auto{
  overflow: auto;
}.dokan-layout .overflow-hidden{
  overflow: hidden;
}.dokan-layout .overflow-y-auto{
  overflow-y: auto;
}.dokan-layout .truncate{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}.dokan-layout .whitespace-nowrap{
  white-space: nowrap;
}.dokan-layout .rounded{
  border-radius: 0.25rem;
}.dokan-layout .rounded-2xl{
  border-radius: 1rem;
}.dokan-layout .rounded-\[20px\]{
  border-radius: 20px;
}.dokan-layout .rounded-full{
  border-radius: 9999px;
}.dokan-layout .rounded-lg{
  border-radius: 0.5rem;
}.dokan-layout .rounded-md{
  border-radius: 0.375rem;
}.dokan-layout .rounded-none{
  border-radius: 0px;
}.dokan-layout .rounded-sm{
  border-radius: 0.125rem;
}.dokan-layout .rounded-l-md{
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}.dokan-layout .rounded-l-none{
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}.dokan-layout .rounded-r-md{
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}.dokan-layout .rounded-r-none{
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}.dokan-layout .border{
  border-width: 1px;
}.dokan-layout .border-0{
  border-width: 0px;
}.dokan-layout .border-2{
  border-width: 2px;
}.dokan-layout .border-\[0\.957434px\]{
  border-width: 0.957434px;
}.dokan-layout .border-\[1px\]{
  border-width: 1px;
}.dokan-layout .\!border-b-2{
  border-bottom-width: 2px !important;
}.dokan-layout .\!border-b-\[1px\]{
  border-bottom-width: 1px !important;
}.dokan-layout .\!border-r-\[1px\]{
  border-right-width: 1px !important;
}.dokan-layout .border-b{
  border-bottom-width: 1px;
}.dokan-layout .border-b-0{
  border-bottom-width: 0px;
}.dokan-layout .border-b-2{
  border-bottom-width: 2px;
}.dokan-layout .border-b-\[1px\]{
  border-bottom-width: 1px;
}.dokan-layout .border-l{
  border-left-width: 1px;
}.dokan-layout .border-l-0{
  border-left-width: 0px;
}.dokan-layout .border-l-\[1px\]{
  border-left-width: 1px;
}.dokan-layout .border-r{
  border-right-width: 1px;
}.dokan-layout .border-r-\[0\.957434px\]{
  border-right-width: 0.957434px;
}.dokan-layout .border-r-\[1px\]{
  border-right-width: 1px;
}.dokan-layout .border-s-2{
  border-inline-start-width: 2px;
}.dokan-layout .border-t{
  border-top-width: 1px;
}.dokan-layout .border-solid{
  border-style: solid;
}.dokan-layout .border-dashed{
  border-style: dashed;
}.dokan-layout .border-none{
  border-style: none;
}.dokan-layout .\!border-dokan-btn{
  border-color: var(--dokan-button-border-color, #7047EB) !important;
}.dokan-layout .border-\[\#1a9ed4\]{
  --tw-border-opacity: 1;
  border-color: rgb(26 158 212 / var(--tw-border-opacity, 1));
}.dokan-layout .border-\[\#5341C20F\]{
  border-color: #5341C20F;
}.dokan-layout .border-\[\#7047EB\]{
  --tw-border-opacity: 1;
  border-color: rgb(112 71 235 / var(--tw-border-opacity, 1));
}.dokan-layout .border-\[\#E5E0F2\]{
  --tw-border-opacity: 1;
  border-color: rgb(229 224 242 / var(--tw-border-opacity, 1));
}.dokan-layout .border-\[\#E9E9E9\]{
  --tw-border-opacity: 1;
  border-color: rgb(233 233 233 / var(--tw-border-opacity, 1));
}.dokan-layout .border-\[\#e9e9ea\]{
  --tw-border-opacity: 1;
  border-color: rgb(233 233 234 / var(--tw-border-opacity, 1));
}.dokan-layout .border-gray-200{
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}.dokan-layout .border-gray-300{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}.dokan-layout .border-indigo-600{
  --tw-border-opacity: 1;
  border-color: rgb(79 70 229 / var(--tw-border-opacity, 1));
}.dokan-layout .border-orange-500{
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}.dokan-layout .border-red-300{
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}.dokan-layout .border-red-50{
  --tw-border-opacity: 1;
  border-color: rgb(254 242 242 / var(--tw-border-opacity, 1));
}.dokan-layout .border-transparent{
  border-color: transparent;
}.dokan-layout .\!bg-transparent{
  background-color: transparent !important;
}.dokan-layout .bg-\[\#0C5F9A\]{
  --tw-bg-opacity: 1;
  background-color: rgb(12 95 154 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#6F4CEB\]{
  --tw-bg-opacity: 1;
  background-color: rgb(111 76 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#7047EB\]{
  --tw-bg-opacity: 1;
  background-color: rgb(112 71 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#D8D8FE\]{
  --tw-bg-opacity: 1;
  background-color: rgb(216 216 254 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#E4E6EB\]{
  --tw-bg-opacity: 1;
  background-color: rgb(228 230 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#EFEAFF\]{
  --tw-bg-opacity: 1;
  background-color: rgb(239 234 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#F1EDFD\]{
  --tw-bg-opacity: 1;
  background-color: rgb(241 237 253 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#F8F9F8\]{
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 248 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#FF9B5366\]{
  background-color: #FF9B5366;
}.dokan-layout .bg-\[\#e4e6eb\]{
  --tw-bg-opacity: 1;
  background-color: rgb(228 230 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-amber-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-blue-50{
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-blue-500{
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-gray-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-gray-200{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-gray-50{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-gray-800{
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-green-50{
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-green-500{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-indigo-100{
  --tw-bg-opacity: 1;
  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-indigo-50{
  --tw-bg-opacity: 1;
  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-indigo-600{
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-orange-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-pink-50{
  --tw-bg-opacity: 1;
  background-color: rgb(253 242 248 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-purple-50{
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-red-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-red-500{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-teal-50{
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 250 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-transparent{
  background-color: transparent;
}.dokan-layout .bg-violet-900{
  --tw-bg-opacity: 1;
  background-color: rgb(76 29 149 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-yellow-400{
  --tw-bg-opacity: 1;
  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-yellow-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[url\(\'\/assets\/images\/error-page-bg\.png\'\)\]{
  background-image: url(../images/error-page-bg.png);
}.dokan-layout .bg-auto{
  background-size: auto;
}.dokan-layout .bg-cover{
  background-size: cover;
}.dokan-layout .bg-\[-20px_10px\]{
  background-position: -20px 10px;
}.dokan-layout .bg-\[right_-40px_bottom_-40px\]{
  background-position: right -40px bottom -40px;
}.dokan-layout .bg-center{
  background-position: center;
}.dokan-layout .bg-no-repeat{
  background-repeat: no-repeat;
}.dokan-layout .\!fill-\[\#7047EB\]{
  fill: #7047EB !important;
}.dokan-layout .fill-neutral-400{
  fill: #a3a3a3;
}.dokan-layout .fill-red-500{
  fill: #ef4444;
}.dokan-layout .fill-white{
  fill: #fff;
}.dokan-layout .stroke-red-500{
  stroke: #ef4444;
}.dokan-layout .stroke-\[2\.5\]{
  stroke-width: 2.5;
}.dokan-layout .object-cover{
  -o-object-fit: cover;
     object-fit: cover;
}.dokan-layout .p-0{
  padding: 0px;
}.dokan-layout .p-1{
  padding: 0.25rem;
}.dokan-layout .p-2{
  padding: 0.5rem;
}.dokan-layout .p-2\.5{
  padding: 0.625rem;
}.dokan-layout .p-3{
  padding: 0.75rem;
}.dokan-layout .p-4{
  padding: 1rem;
}.dokan-layout .p-5{
  padding: 1.25rem;
}.dokan-layout .p-6{
  padding: 1.5rem;
}.dokan-layout .p-8{
  padding: 2rem;
}.dokan-layout .px-1{
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}.dokan-layout .px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}.dokan-layout .px-2\.5{
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}.dokan-layout .px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}.dokan-layout .px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}.dokan-layout .px-5{
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}.dokan-layout .px-6{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}.dokan-layout .px-8{
  padding-left: 2rem;
  padding-right: 2rem;
}.dokan-layout .px-\[18px\]{
  padding-left: 18px;
  padding-right: 18px;
}.dokan-layout .px-\[25px\]{
  padding-left: 25px;
  padding-right: 25px;
}.dokan-layout .py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}.dokan-layout .py-12{
  padding-top: 3rem;
  padding-bottom: 3rem;
}.dokan-layout .py-16{
  padding-top: 4rem;
  padding-bottom: 4rem;
}.dokan-layout .py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}.dokan-layout .py-2\.5{
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}.dokan-layout .py-3{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}.dokan-layout .py-3\.5{
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}.dokan-layout .py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}.dokan-layout .py-5{
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}.dokan-layout .py-6{
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}.dokan-layout .py-8{
  padding-top: 2rem;
  padding-bottom: 2rem;
}.dokan-layout .pb-10{
  padding-bottom: 2.5rem;
}.dokan-layout .pb-5{
  padding-bottom: 1.25rem;
}.dokan-layout .pl-1{
  padding-left: 0.25rem;
}.dokan-layout .pl-2{
  padding-left: 0.5rem;
}.dokan-layout .pl-3{
  padding-left: 0.75rem;
}.dokan-layout .pl-4{
  padding-left: 1rem;
}.dokan-layout .pl-\[5px\]{
  padding-left: 5px;
}.dokan-layout .pr-0{
  padding-right: 0px;
}.dokan-layout .pr-1\.5{
  padding-right: 0.375rem;
}.dokan-layout .pr-2{
  padding-right: 0.5rem;
}.dokan-layout .pt-4{
  padding-top: 1rem;
}.dokan-layout .text-left{
  text-align: left;
}.dokan-layout .text-center{
  text-align: center;
}.dokan-layout .align-middle{
  vertical-align: middle;
}.dokan-layout .text-2xl{
  font-size: 1.5rem;
  line-height: 2rem;
}.dokan-layout .text-2xl\/7{
  font-size: 1.5rem;
  line-height: 1.75rem;
}.dokan-layout .text-3xl{
  font-size: 1.875rem;
  line-height: 2.25rem;
}.dokan-layout .text-\[10px\]{
  font-size: 10px;
}.dokan-layout .text-\[12px\]{
  font-size: 12px;
}.dokan-layout .text-\[14px\]{
  font-size: 14px;
}.dokan-layout .text-base{
  font-size: 1rem;
  line-height: 1.5rem;
}.dokan-layout .text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}.dokan-layout .text-sm{
  font-size: 0.875rem;
  line-height: 1.25rem;
}.dokan-layout .text-sm\/6{
  font-size: 0.875rem;
  line-height: 1.5rem;
}.dokan-layout .text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}.dokan-layout .text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}.dokan-layout .font-bold{
  font-weight: 700;
}.dokan-layout .font-light{
  font-weight: 300;
}.dokan-layout .font-medium{
  font-weight: 500;
}.dokan-layout .font-normal{
  font-weight: 400;
}.dokan-layout .font-semibold{
  font-weight: 600;
}.dokan-layout .capitalize{
  text-transform: capitalize;
}.dokan-layout .leading-3{
  line-height: .75rem;
}.dokan-layout .leading-4{
  line-height: 1rem;
}.dokan-layout .leading-5{
  line-height: 1.25rem;
}.dokan-layout .leading-6{
  line-height: 1.5rem;
}.dokan-layout .leading-\[48px\]{
  line-height: 48px;
}.dokan-layout .tracking-wide{
  letter-spacing: 0.025em;
}.dokan-layout .\!text-dokan-primary{
  color: var(--dokan-button-background-color, #7047EB) !important;
}.dokan-layout .\!text-gray-800{
  --tw-text-opacity: 1 !important;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1)) !important;
}.dokan-layout .\!text-gray-900{
  --tw-text-opacity: 1 !important;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1)) !important;
}.dokan-layout .\!text-white{
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}.dokan-layout .text-\[\#1a9ed4\]{
  --tw-text-opacity: 1;
  color: rgb(26 158 212 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#25252D\]{
  --tw-text-opacity: 1;
  color: rgb(37 37 45 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#393939\]{
  --tw-text-opacity: 1;
  color: rgb(57 57 57 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#4C19E6\]{
  --tw-text-opacity: 1;
  color: rgb(76 25 230 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#575757\]{
  --tw-text-opacity: 1;
  color: rgb(87 87 87 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#637381\]{
  --tw-text-opacity: 1;
  color: rgb(99 115 129 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#7047EB\]{
  --tw-text-opacity: 1;
  color: rgb(112 71 235 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#788383\]{
  --tw-text-opacity: 1;
  color: rgb(120 131 131 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#7B4E2E\]{
  --tw-text-opacity: 1;
  color: rgb(123 78 46 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#828282\]{
  --tw-text-opacity: 1;
  color: rgb(130 130 130 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#bcbed5\]{
  --tw-text-opacity: 1;
  color: rgb(188 190 213 / var(--tw-text-opacity, 1));
}.dokan-layout .text-amber-800{
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity, 1));
}.dokan-layout .text-black{
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}.dokan-layout .text-blue-600{
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}.dokan-layout .text-blue-800{
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}.dokan-layout .text-dokan-danger{
  color: var(--dokan-danger-text-color, #BC1C21);
}.dokan-layout .text-dokan-link{
  color: var(--dokan-link-color, #7047EB);
}.dokan-layout .text-dokan-primary{
  color: var(--dokan-button-background-color, #7047EB);
}.dokan-layout .text-gray-200{
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}.dokan-layout .text-gray-300{
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}.dokan-layout .text-gray-500{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}.dokan-layout .text-gray-600{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}.dokan-layout .text-gray-700{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}.dokan-layout .text-gray-800{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}.dokan-layout .text-gray-900{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}.dokan-layout .text-green-800{
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}.dokan-layout .text-indigo-600{
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}.dokan-layout .text-indigo-800{
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}.dokan-layout .text-orange-600{
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}.dokan-layout .text-orange-800{
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}.dokan-layout .text-pink-800{
  --tw-text-opacity: 1;
  color: rgb(157 23 77 / var(--tw-text-opacity, 1));
}.dokan-layout .text-purple-600{
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}.dokan-layout .text-purple-800{
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}.dokan-layout .text-red-300{
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}.dokan-layout .text-red-500{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}.dokan-layout .text-red-600{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}.dokan-layout .text-red-800{
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}.dokan-layout .text-teal-800{
  --tw-text-opacity: 1;
  color: rgb(17 94 89 / var(--tw-text-opacity, 1));
}.dokan-layout .text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}.dokan-layout .text-yellow-400{
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}.dokan-layout .text-yellow-800{
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}.dokan-layout .underline{
  text-decoration-line: underline;
}.dokan-layout .no-underline{
  text-decoration-line: none;
}.dokan-layout .opacity-0{
  opacity: 0;
}.dokan-layout .opacity-100{
  opacity: 1;
}.dokan-layout .opacity-25{
  opacity: 0.25;
}.dokan-layout .opacity-75{
  opacity: 0.75;
}.dokan-layout .\!shadow-none{
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}.dokan-layout .shadow{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.dokan-layout .shadow-\[0px_2px_14px_1px_rgba\(50\2c 22\2c 56\2c 0\.06\)\]{
  --tw-shadow: 0px 2px 14px 1px rgba(50,22,56,0.06);
  --tw-shadow-colored: 0px 2px 14px 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.dokan-layout .shadow-\[0px_4px_20px_0px_rgba\(0\2c 0\2c 0\2c 0\.05\)\]{
  --tw-shadow: 0px 4px 20px 0px rgba(0,0,0,0.05);
  --tw-shadow-colored: 0px 4px 20px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.dokan-layout .shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.dokan-layout .shadow-none{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.dokan-layout .shadow-sm{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.dokan-layout .shadow-xl{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.dokan-layout .outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}.dokan-layout .ring-1{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.dokan-layout .ring-2{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.dokan-layout .ring-inset{
  --tw-ring-inset: inset;
}.dokan-layout .ring-black\/5{
  --tw-ring-color: rgb(0 0 0 / 0.05);
}.dokan-layout .ring-gray-900\/5{
  --tw-ring-color: rgb(17 24 39 / 0.05);
}.dokan-layout .ring-white{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));
}.dokan-layout .filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}.dokan-layout .transition{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.dokan-layout .transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.dokan-layout .transition-colors{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.dokan-layout .transition-opacity{
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.dokan-layout .transition-transform{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.dokan-layout .duration-150{
  transition-duration: 150ms;
}.dokan-layout .duration-200{
  transition-duration: 200ms;
}.dokan-layout .duration-300{
  transition-duration: 300ms;
}.dokan-layout .duration-500{
  transition-duration: 500ms;
}.dokan-layout .ease-in-out{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}.dokan-layout .ease-out{
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}.dokan-layout .\@container\/after-header{
  container-type: inline-size;
  container-name: after-header;
}.dokan-layout .\@container\/before-header{
  container-type: inline-size;
  container-name: before-header;
}.dokan-layout .\@container\/combine{
  container-type: inline-size;
  container-name: combine;
}.dokan-layout .\@container\/currency{
  container-type: inline-size;
  container-name: currency;
}.dokan-layout .\@container\/header{
  container-type: inline-size;
  container-name: header;
}.dokan-layout .\@container\/header-title-section{
  container-type: inline-size;
  container-name: header-title-section;
}.dokan-layout .\@container\/main{
  container-type: inline-size;
  container-name: main;
}.dokan-layout .\@container\/radio{
  container-type: inline-size;
  container-name: radio;
}.dokan-layout .\@container\/step-body{
  container-type: inline-size;
  container-name: step-body;
}.dokan-layout .\@container\/switcher{
  container-type: inline-size;
  container-name: switcher;
}.dokan-layout  .dokan-btn{
  position: relative !important;
  border-color: var(--dokan-button-border-color, #7047EB) !important;
  background-color: var(--dokan-button-background-color, #7047EB) !important;
  color: var(--dokan-button-text-color, #ffffff) !important;
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
  --tw-ring-color: var(--dokan-button-border-color, #7047EB) !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;
  transition-duration: 200ms !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
}.dokan-layout  .dokan-btn:hover{
  border-color: var(--dokan-button-hover-border-color, #370EB1) !important;
  background-color: var(--dokan-button-hover-background-color, #502BBF) !important;
  color: var(--dokan-button-hover-text-color, #ffffff) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn:disabled{
  cursor: not-allowed !important;
  opacity: 0.6 !important;
}.dokan-layout  .dokan-btn-secondary{
  position: relative !important;
  border-style: none !important;
  border-color: var(--dokan-button-secondary-border-color, var(--dokan-button-border-color, #7047EB)) !important;
  background-color: var(--dokan-button-secondary-background-color, #ffffff) !important;
  color: var(--dokan-button-secondary-text-color, #7047EB) !important;
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-color: var(--dokan-button-secondary-border-color, var(--dokan-button-border-color, #7047EB)) !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;
  transition-duration: 200ms !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
}.dokan-layout  .dokan-btn-secondary:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-secondary:hover{
  border-color: var(--dokan-button-secondary-hover-border-color, #7047EB) !important;
  background-color: var(--dokan-button-secondary-hover-background-color, #DACEFF82) !important;
  color: var(--dokan-button-secondary-hover-text-color, #7047EB) !important;
  --tw-ring-color: var(--dokan-button-secondary-hover-border-color, #7047EB) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout .dokan-btn-tertiary{--tw-shadow: 1px 3px 4px 0 rgb(0 0 0 / 0.05);position: relative !important;border-color: var(--dokan-button-tertiary-border-color, #00000000) !important;background-color: var(--dokan-button-tertiary-background-color, #00000000) !important;color: var(--dokan-button-tertiary-text-color, var(--dokan-button-background-color, #7047EB)) !important;--tw-ring-color: var(--dokan-button-tertiary-border-color, #00000000) !important;transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;transition-duration: 200ms !important;transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important}.dokan-layout  .dokan-btn-tertiary:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-tertiary:hover{
  background-color: var(--dokan-button-tertiary-hover-background-color, #DACEFF82) !important;
  color: var(--dokan-button-tertiary-hover-text-color, var(--dokan-button-background-color, #7047EB)) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-info{
  position: relative !important;
  border-color: var(--dokan-button-info-background-color, #0B76B7) !important;
  background-color: var(--dokan-button-info-background-color, #0B76B7) !important;
  color: var(--dokan-button-info-text-color, #ffffff) !important;
  --tw-ring-color: var(--dokan-button-info-background-color, #0B76B7) !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-duration: 200ms !important;
}.dokan-layout  .dokan-btn-info:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-info:hover{
  border-color: var(--dokan-button-info-hover-background-color, #2795d7) !important;
  background-color: var(--dokan-button-info-hover-background-color, #2795d7) !important;
  color: var(--dokan-button-info-hover-text-color, #ffffff) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-color: var(--dokan-button-info-hover-background-color, #2795d7) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-success{
  position: relative !important;
  border-color: var(--dokan-button-success-background-color, #07a67e) !important;
  background-color: var(--dokan-button-success-background-color, #07a67e) !important;
  color: var(--dokan-button-success-text-color, #ffffff) !important;
  --tw-ring-color: var(--dokan-button-success-background-color, #07a67e) !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-duration: 200ms !important;
}.dokan-layout  .dokan-btn-success:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-success:hover{
  border-color: var(--dokan-button-success-hover-background-color, #11b68c) !important;
  background-color: var(--dokan-button-success-hover-background-color, #11b68c) !important;
  color: var(--dokan-button-success-hover-text-color, #ffffff) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-color: var(--dokan-button-success-hover-background-color, #11b68c) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-warning{
  position: relative !important;
  border-color: var(--dokan-button-warning-background-color, #e9a905) !important;
  background-color: var(--dokan-button-warning-background-color, #e9a905) !important;
  color: var(--dokan-button-warning-text-color, #ffffff) !important;
  --tw-ring-color: var(--dokan-button-warning-background-color, #e9a905) !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-duration: 200ms !important;
}.dokan-layout  .dokan-btn-warning:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-warning:hover{
  border-color: var(--dokan-button-warning-hover-background-color, #fbbf24) !important;
  background-color: var(--dokan-button-warning-hover-background-color, #fbbf24) !important;
  color: var(--dokan-button-warning-hover-text-color, #ffffff) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-color: var(--dokan-button-warning-hover-background-color, #fbbf24) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-danger{
  position: relative !important;
  border-color: var(--dokan-button-danger-background-color, #e3050c) !important;
  background-color: var(--dokan-button-danger-background-color, #e3050c) !important;
  color: var(--dokan-button-danger-text-color, #ffffff) !important;
  --tw-ring-color: var(--dokan-button-danger-background-color, #e3050c) !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-duration: 200ms !important;
}.dokan-layout  .dokan-btn-danger:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-danger:hover{
  border-color: var(--dokan-button-danger-hover-background-color, #f23030) !important;
  background-color: var(--dokan-button-danger-hover-background-color, #f23030) !important;
  color: var(--dokan-button-danger-hover-text-color, #ffffff) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-color: var(--dokan-button-danger-hover-background-color, #f23030) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .\!dokan-link{
  position: relative !important;
  color: var(--dokan-link-color, #7047EB) !important;
  text-decoration-line: none !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-duration: 200ms !important;
}.dokan-layout  .dokan-link{
  position: relative !important;
  color: var(--dokan-link-color, #7047EB) !important;
  text-decoration-line: none !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-duration: 200ms !important;
}.dokan-layout  .\!dokan-link:hover,:is(.dokan-layout .\!dokan-link:focus){
  color: var(--dokan-link-hover-color, var(--dokan-sidebar-background-color, #322067)) !important;
}.dokan-layout  .dokan-link:hover,:is(.dokan-layout .dokan-link:focus){
  color: var(--dokan-link-hover-color, var(--dokan-sidebar-background-color, #322067)) !important;
}.dokan-layout  .\!dokan-link:hover,:is(.dokan-layout .\!dokan-link:focus){
  color: var(--dokan-link-hover-color, var(--dokan-sidebar-background-color, #322067)) !important;
}.dokan-layout  .dokan-link:hover,:is(.dokan-layout .dokan-link:focus){
  color: var(--dokan-link-hover-color, var(--dokan-sidebar-background-color, #322067)) !important;
}.dokan-layout  .dokan-badge-primary{
  position: relative !important;
  background-color: var(--dokan-button-background-color, #7047EB) !important;
  color: var(--dokan-button-text-color, #ffffff) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-button-border-color, #7047EB) !important;
}.dokan-layout  .dokan-badge-secondary{
  position: relative !important;
  background-color: var(--dokan-button-secondary-background-color, #ffffff) !important;
  color: var(--dokan-button-secondary-text-color, #7047EB) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-button-secondary-border-color, var(--dokan-button-border-color, #7047EB)) !important;
}.dokan-layout  .dokan-badge-info{
  position: relative !important;
  background-color: var(--dokan-info-background-color, #E9F9FF) !important;
  color: var(--dokan-info-text-color, #0B76B7) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-info-border-color, #E9F9FFFF) !important;
}.dokan-layout  .dokan-badge-success{
  position: relative !important;
  background-color: var(--dokan-success-background-color, #DAF8E6) !important;
  color: var(--dokan-success-text-color, #004434) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-success-border-color, #DAF8E6FF) !important;
}.dokan-layout  .dokan-badge-warning{
  position: relative !important;
  background-color: var(--dokan-warning-background-color, #FFFBEB) !important;
  color: var(--dokan-warning-text-color, #9D5425) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-warning-border-color, #FFFBEBFF) !important;
}.dokan-layout  .dokan-badge-danger{
  position: relative !important;
  background-color: var(--dokan-danger-background-color, #FEF3F3) !important;
  color: var(--dokan-danger-text-color, #BC1C21) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-danger-border-color, #FEF3F3FF) !important;
}.dokan-layout  .dokan-alert-info{
  position: relative !important;
  background-color: var(--dokan-info-background-color, #E9F9FF) !important;
  color: var(--dokan-info-secondary-text-color, #637381) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-info-border-color, #E9F9FFFF) !important;
}.dokan-layout  .dokan-alert-success{
  position: relative !important;
  background-color: var(--dokan-success-background-color, #DAF8E6) !important;
  color: var(--dokan-success-secondary-text-color, #637381) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-success-border-color, #DAF8E6FF) !important;
}.dokan-layout  .dokan-alert-warning{
  position: relative !important;
  background-color: var(--dokan-warning-background-color, #FFFBEB) !important;
  color: var(--dokan-warning-secondary-text-color, #D0915C) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-warning-border-color, #FFFBEBFF) !important;
}.dokan-layout  .dokan-alert-danger{
  position: relative !important;
  background-color: var(--dokan-danger-background-color, #FEF3F3) !important;
  color: var(--dokan-danger-secondary-text-color, #F56060) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-danger-border-color, #FEF3F3FF) !important;
}.dokan-admin-setup-wizard{margin:0}.dokan-admin-setup-wizard .dokan-onboard-welcome{background-image:url(../images/onboard-bg.jpg);background-size:cover;background-position:center;background-repeat:no-repeat}.dokan-onboard-radial-gradient{background:radial-gradient(farthest-corner, #FFE0EE, #EAF6FF, #FFF9EA, #FAFAFA, #F5F7F2);background-size:200% 200%;animation:moveGradient 10s infinite linear alternate}@keyframes moveGradient{0%{background-position:0% 0%}25%{background-position:100% 0%}50%{background-position:100% 100%}75%{background-position:0% 100%}100%{background-position:0% 0%}}#headlessui-portal-root.dokan-layout button[type=button]:is(.before\:absolute.right-2.top-2)::before{content: var(--tw-content);border-radius: 9999px;padding-top: 0.125rem;padding-bottom: 0.125rem;padding-left: 0.375rem;padding-right: 0.375rem}#headlessui-portal-root.dokan-layout button[type=button]:is(.before\:absolute.right-2.top-2):hover::before{content: var(--tw-content);--tw-bg-opacity: 1;background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color: rgb(239 68 68 / var(--tw-text-opacity, 1));--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);--tw-ring-opacity: 1;--tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));--tw-ring-offset-width: 2px}.dokan-layout .before\:absolute::before{
  content: var(--tw-content);
  position: absolute;
}.dokan-layout .before\:bottom-full::before{
  content: var(--tw-content);
  bottom: 100%;
}.dokan-layout .before\:left-0::before{
  content: var(--tw-content);
  left: 0px;
}.dokan-layout .before\:h-12::before{
  content: var(--tw-content);
  height: 3rem;
}.dokan-layout .before\:w-full::before{
  content: var(--tw-content);
  width: 100%;
}.dokan-layout .before\:content-\[\'\'\]::before{
  --tw-content: '';
  content: var(--tw-content);
}.dokan-layout :is(.\*\:first\:\*\:border-transparent > *:first-child > *){
  border-color: transparent;
}.dokan-layout :is(.\*\:first\:border-gray-200:first-child > *){
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}.dokan-layout .last\:mb-0:last-child{
  margin-bottom: 0px;
}.dokan-layout .last\:border-b-0:last-child{
  border-bottom-width: 0px;
}.dokan-layout .hover\:border-gray-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}.dokan-layout .hover\:bg-blue-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .hover\:bg-gray-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}.dokan-layout .hover\:bg-indigo-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));
}.dokan-layout .hover\:bg-indigo-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}.dokan-layout .hover\:bg-transparent:hover{
  background-color: transparent;
}.dokan-layout .hover\:bg-white:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .hover\:bg-yellow-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}.dokan-layout .hover\:bg-opacity-85:hover{
  --tw-bg-opacity: 0.85;
}.dokan-layout .hover\:\!text-\[\#7047EB\]:hover{
  --tw-text-opacity: 1 !important;
  color: rgb(112 71 235 / var(--tw-text-opacity, 1)) !important;
}.dokan-layout .hover\:text-\[\#7047EB\]:hover{
  --tw-text-opacity: 1;
  color: rgb(112 71 235 / var(--tw-text-opacity, 1));
}.dokan-layout .hover\:text-gray-600:hover{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}.dokan-layout .hover\:text-gray-700:hover{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}.dokan-layout .hover\:text-gray-900:hover{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}.dokan-layout .hover\:underline:hover{
  text-decoration-line: underline;
}.dokan-layout .focus\:border-none:focus{
  border-style: none;
}.dokan-layout .focus\:border-gray-300:focus{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}.dokan-layout .focus\:border-gray-900:focus{
  --tw-border-opacity: 1;
  border-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}.dokan-layout .focus\:border-orange-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}.dokan-layout .focus\:\!outline-none:focus{
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
}.dokan-layout .focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}.dokan-layout .focus\:outline-dokan-btn:focus{
  outline-color: var(--dokan-button-border-color, #7047EB);
}.dokan-layout .focus\:\!ring-0:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
}.dokan-layout .focus\:ring-0:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.dokan-layout .focus\:ring-gray-900:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(17 24 39 / var(--tw-ring-opacity, 1));
}.dokan-layout .focus\:ring-indigo-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));
}.dokan-layout .focus\:ring-orange-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity, 1));
}.dokan-layout .focus-visible\:outline:focus-visible{
  outline-style: solid;
}.dokan-layout .focus-visible\:outline-2:focus-visible{
  outline-width: 2px;
}.dokan-layout .focus-visible\:outline-offset-2:focus-visible{
  outline-offset: 2px;
}.dokan-layout .focus-visible\:outline-indigo-600:focus-visible{
  outline-color: #4f46e5;
}.dokan-layout .disabled\:cursor-not-allowed:disabled{
  cursor: not-allowed;
}.dokan-layout :is(.group:hover .group-hover\:\!bg-\[\#EFEAFF\]){
  --tw-bg-opacity: 1 !important;
  background-color: rgb(239 234 255 / var(--tw-bg-opacity, 1)) !important;
}.dokan-layout :is(.group:hover .group-hover\:fill-\[\#7047EB\]){
  fill: #7047EB;
}.dokan-layout .data-\[closed\]\:translate-y-1[data-closed]{
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.dokan-layout .data-\[closed\]\:opacity-0[data-closed]{
  opacity: 0;
}.dokan-layout .data-\[enter\]\:duration-200[data-enter]{
  transition-duration: 200ms;
}.dokan-layout .data-\[leave\]\:duration-150[data-leave]{
  transition-duration: 150ms;
}.dokan-layout .data-\[enter\]\:ease-out[data-enter]{
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}.dokan-layout .data-\[leave\]\:ease-in[data-leave]{
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}@container main (min-width: 20rem){.dokan-layout .\@xs\/main\:py-4{
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}@container currency (min-width: 24rem){.dokan-layout .\@sm\/currency\:col-span-4{
    grid-column: span 4 / span 4;
  }.dokan-layout .\@sm\/currency\:col-span-8{
    grid-column: span 8 / span 8;
  }.dokan-layout .\@sm\/currency\:justify-end{
    justify-content: flex-end;
  }.dokan-layout .\@sm\/currency\:text-base{
    font-size: 1rem;
    line-height: 1.5rem;
  }.dokan-layout .\@sm\/currency\:text-sm{
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}@container (min-width: 24rem){.dokan-layout .\@sm\:text-sm{
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}@container combine (min-width: 28rem){.dokan-layout .\@md\/combine\:text-base{
    font-size: 1rem;
    line-height: 1.5rem;
  }.dokan-layout .\@md\/combine\:text-sm{
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}@container radio (min-width: 28rem){.dokan-layout .\@md\/radio\:col-span-4{
    grid-column: span 4 / span 4;
  }.dokan-layout .\@md\/radio\:col-span-8{
    grid-column: span 8 / span 8;
  }.dokan-layout .\@md\/radio\:justify-end{
    justify-content: flex-end;
  }
}@container step-body (min-width: 28rem){.dokan-layout .\@md\/step-body\:p-12{
    padding: 3rem;
  }
}@container switcher (min-width: 28rem){.dokan-layout .\@md\/switcher\:col-span-4{
    grid-column: span 4 / span 4;
  }.dokan-layout .\@md\/switcher\:col-span-8{
    grid-column: span 8 / span 8;
  }.dokan-layout .\@md\/switcher\:justify-end{
    justify-content: flex-end;
  }
}@container (min-width: 28rem){.dokan-layout .\@md\:gap-6{
    gap: 1.5rem;
  }.dokan-layout .\@md\:text-base{
    font-size: 1rem;
    line-height: 1.5rem;
  }
}@container combine (min-width: 36rem){.dokan-layout .\@xl\/combine\:col-span-5{
    grid-column: span 5 / span 5;
  }.dokan-layout .\@xl\/combine\:col-span-7{
    grid-column: span 7 / span 7;
  }
}@container main (min-width: 36rem){.dokan-layout .\@xl\/main\:col-span-4{
    grid-column: span 4 / span 4;
  }.dokan-layout .\@xl\/main\:col-span-8{
    grid-column: span 8 / span 8;
  }.dokan-layout .\@xl\/main\:my-0{
    margin-top: 0px;
    margin-bottom: 0px;
  }
}@container main (min-width: 42rem){.dokan-layout .\@2xl\/main\:mb-10{
    margin-bottom: 2.5rem;
  }
}@container main (min-width: 48rem){.dokan-layout .\@3xl\/main\:col-span-3{
    grid-column: span 3 / span 3;
  }.dokan-layout .\@3xl\/main\:col-span-9{
    grid-column: span 9 / span 9;
  }.dokan-layout .\@3xl\/main\:py-10{
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }
}@container main (min-width: 64rem){.dokan-layout .\@5xl\/main\:pl-12{
    padding-left: 3rem;
  }
}@container step-body (min-width: 64rem){.dokan-layout .\@5xl\/step-body\:px-28{
    padding-left: 7rem;
    padding-right: 7rem;
  }.dokan-layout .\@5xl\/step-body\:py-16{
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}@media (min-width: 640px){.dokan-layout .sm\:mb-0{
    margin-bottom: 0px;
  }.dokan-layout .sm\:ml-4{
    margin-left: 1rem;
  }.dokan-layout .sm\:mt-0{
    margin-top: 0px;
  }.dokan-layout .sm\:block{
    display: block;
  }.dokan-layout .sm\:flex{
    display: flex;
  }.dokan-layout .sm\:hidden{
    display: none;
  }.dokan-layout .sm\:w-\[50rem\]{
    width: 50rem;
  }.dokan-layout .sm\:w-\[54rem\]{
    width: 54rem;
  }.dokan-layout .sm\:w-\[70\%\]{
    width: 70%;
  }.dokan-layout .sm\:w-auto{
    width: auto;
  }.dokan-layout .sm\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }.dokan-layout .sm\:flex-col{
    flex-direction: column;
  }.dokan-layout .sm\:items-start{
    align-items: flex-start;
  }.dokan-layout .sm\:\!items-center{
    align-items: center !important;
  }.dokan-layout .sm\:truncate{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }.dokan-layout .sm\:rounded-lg{
    border-radius: 0.5rem;
  }.dokan-layout .sm\:rounded-md{
    border-radius: 0.375rem;
  }.dokan-layout .sm\:p-6{
    padding: 1.5rem;
  }.dokan-layout .sm\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }.dokan-layout .sm\:text-left{
    text-align: left;
  }.dokan-layout .sm\:text-2xl{
    font-size: 1.5rem;
    line-height: 2rem;
  }.dokan-layout .sm\:text-3xl{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }.dokan-layout .sm\:text-base{
    font-size: 1rem;
    line-height: 1.5rem;
  }.dokan-layout .sm\:text-sm{
    font-size: 0.875rem;
    line-height: 1.25rem;
  }.dokan-layout .sm\:tracking-tight{
    letter-spacing: -0.025em;
  }
}@media (min-width: 768px){.dokan-layout .md\:mb-4{
    margin-bottom: 1rem;
  }.dokan-layout .md\:mb-6{
    margin-bottom: 1.5rem;
  }.dokan-layout .md\:mb-8{
    margin-bottom: 2rem;
  }.dokan-layout .md\:ml-4{
    margin-left: 1rem;
  }.dokan-layout .md\:mt-0{
    margin-top: 0px;
  }.dokan-layout .md\:flex{
    display: flex;
  }.dokan-layout .md\:h-\[33\.5rem\]{
    height: 33.5rem;
  }.dokan-layout .md\:\!w-1\/2{
    width: 50% !important;
  }.dokan-layout .md\:w-1\/2{
    width: 50%;
  }.dokan-layout .md\:w-\[1px\]{
    width: 1px;
  }.dokan-layout .md\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }.dokan-layout .md\:\!flex-row{
    flex-direction: row !important;
  }.dokan-layout .md\:flex-row{
    flex-direction: row;
  }.dokan-layout .md\:items-center{
    align-items: center;
  }.dokan-layout .md\:\!justify-end{
    justify-content: flex-end !important;
  }.dokan-layout .md\:justify-between{
    justify-content: space-between;
  }.dokan-layout .md\:border-0{
    border-width: 0px;
  }.dokan-layout .md\:bg-\[0px_20px\]{
    background-position: 0px 20px;
  }.dokan-layout .md\:bg-\[right_0px_bottom_-10px\]{
    background-position: right 0px bottom -10px;
  }.dokan-layout .md\:p-10{
    padding: 2.5rem;
  }.dokan-layout .md\:text-3xl{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }.dokan-layout .md\:text-4xl{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }.dokan-layout .md\:text-\[14px\]{
    font-size: 14px;
  }.dokan-layout .md\:text-sm{
    font-size: 0.875rem;
    line-height: 1.25rem;
  }.dokan-layout .md\:leading-\[1\.3\]{
    line-height: 1.3;
  }
}@media (min-width: 1024px){.dokan-layout .lg\:col-span-12{
    grid-column: span 12 / span 12;
  }.dokan-layout .lg\:col-span-3{
    grid-column: span 3 / span 3;
  }.dokan-layout .lg\:col-span-9{
    grid-column: span 9 / span 9;
  }.dokan-layout .lg\:grid{
    display: grid;
  }.dokan-layout .lg\:grid-cols-12{
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }.dokan-layout .lg\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }.dokan-layout .lg\:gap-6{
    gap: 1.5rem;
  }.dokan-layout .lg\:gap-x-5{
    -moz-column-gap: 1.25rem;
         column-gap: 1.25rem;
  }.dokan-layout .lg\:px-0{
    padding-left: 0px;
    padding-right: 0px;
  }.dokan-layout .lg\:py-0{
    padding-top: 0px;
    padding-bottom: 0px;
  }.dokan-layout .lg\:py-5{
    padding-top: 1.25rem;
    padding-bottom: 1.25rem;
  }.dokan-layout .lg\:text-3xl{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}@media (min-width: 1280px){.dokan-layout .xl\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}@media (prefers-color-scheme: dark){.dokan-layout .dark\:border-gray-700{
    --tw-border-opacity: 1;
    border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
  }.dokan-layout .dark\:text-black{
    --tw-text-opacity: 1;
    color: rgb(0 0 0 / var(--tw-text-opacity, 1));
  }.dokan-layout .dark\:text-blue-500{
    --tw-text-opacity: 1;
    color: rgb(59 130 246 / var(--tw-text-opacity, 1));
  }.dokan-layout .dark\:text-gray-400{
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity, 1));
  }.dokan-layout .dark\:text-gray-600{
    --tw-text-opacity: 1;
    color: rgb(75 85 99 / var(--tw-text-opacity, 1));
  }
}.dokan-layout .\[\&\:\:-webkit-scrollbar-thumb\]\:rounded-full::-webkit-scrollbar-thumb{
  border-radius: 9999px;
}.dokan-layout .\[\&\:\:-webkit-scrollbar\]\:h-1::-webkit-scrollbar{
  height: 0.25rem;
}.dokan-layout .\[\&\:\:-webkit-scrollbar\]\:w-2::-webkit-scrollbar{
  width: 0.5rem;
}.dokan-layout :is(.\*\:\[\&\:not\(\:last-child\)\]\:\*\:border-b-2 > *:not(:last-child) > *){
  border-bottom-width: 2px;
}.dokan-layout :is(.\[\&\:not\(\:last-child\)\]\:\*\:border-b > *:not(:last-child)){
  border-bottom-width: 1px;
}.dokan-layout :is(.focus\:\*\:\[\&\:not\(\:last-child\)\]\:\*\:outline-transparent > *:not(:last-child) > *:focus){
  outline-color: transparent;
}.dokan-layout :is(.\[\&_\*_p\]\:m-0 * p){
  margin: 0px;
}
