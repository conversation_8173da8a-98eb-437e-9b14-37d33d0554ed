jQuery(function(n){function t(e){var o=Dokan_Pointers.pointers[e],i=n.extend(o.options,{close:function(){n.post(dokan_pointer_data.ajaxurl,{screen:dokan_pointer_data.screen,action:"dokan-dismiss-wp-pointer"})}}),r=n(o.target).pointer(i);r.pointer("open"),"next_button"in o&&n(".wp-pointer-buttons").append(o.next_button),n(".wp-pointer-buttons").find("a.close").addClass("dokan button button-secondary"),o.next_trigger&&n(o.next_trigger.target).on(o.next_trigger.event,function(){setTimeout(function(){r.pointer("close"),t(o.next)},200)})}setTimeout(function(){n.each(Dokan_Pointers.pointers,function(n){return t(n),!1})},800)});