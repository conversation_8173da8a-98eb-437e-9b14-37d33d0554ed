(()=>{"use strict";var e={n:t=>{var r=t&&t.__esModule?()=>t.default:()=>t;return e.d(r,{a:r}),r},d:(t,r)=>{for(var s in r)e.o(r,s)&&!e.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:r[s]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{DOKAN_PRODUCT_STORE:()=>p,default:()=>w});const r=window.wp.data,s={items:{},queries:{},isLoading:!1,error:null},o="SET_ITEMS",a="SET_QUERY",n="SET_ERROR",i="SET_LOADING",d={setItems:e=>({type:o,items:e}),setQuery:(e,t,r,s)=>({type:a,queryId:e,ids:t,totalCount:r,totalPages:s}),setError:e=>({type:n,error:e}),setLoading:e=>({type:i,isLoading:e})},u=window.wp.url,g={getItem:(e,t)=>e.items[t],getItems:(e,t={})=>{const r=(0,u.addQueryArgs)("/dokan/v2/products",t),s=JSON.stringify((0,u.getQueryArgs)(r)),o=e.queries[s];if(o)return o.ids.map(t=>e.items[t]).filter(Boolean)},getQueryTotalCount:(e,t={})=>{const r=(0,u.addQueryArgs)("/dokan/v2/products",t),s=JSON.stringify((0,u.getQueryArgs)(r));return e.queries[s]?.totalCount},getQueryTotalPages:(e,t={})=>{const r=(0,u.addQueryArgs)("/dokan/v2/products",t),s=JSON.stringify((0,u.getQueryArgs)(r));return e.queries[s]?.totalPages},isLoading:e=>e.isLoading,getError:e=>e.error},c=window.wp.apiFetch;var l=e.n(c);const y={getItem:e=>async({dispatch:t})=>{try{const r=await l()({path:`/dokan/v2/products/${e}`});return t(d.setItems({[e]:r}))}catch(e){return t(d.setError(e))}},getItems:(e={})=>async({dispatch:t})=>{t(d.setLoading(!0));try{const r=(0,u.addQueryArgs)("/dokan/v2/products",e),s=JSON.stringify((0,u.getQueryArgs)(r)),o=await l()({path:r,parse:!1}),a=await o.json(),n=parseInt(o.headers.get("X-WP-Total")||"0",10),i=parseInt(o.headers.get("X-WP-TotalPages")||"0",10),g={},c=a.map(e=>(g[e.id]=e,e.id));t(d.setItems(g)),t(d.setQuery(s,c,n,i))}catch(e){t(d.setError(e))}finally{t(d.setLoading(!1))}}},p="dokan/products",m=(0,r.createReduxStore)(p,{reducer:(e=s,t)=>{switch(t.type){case o:return{...e,items:{...e.items,...t.items},error:null};case a:return{...e,queries:{...e.queries,[t.queryId]:{ids:t.ids,totalCount:t.totalCount,totalPages:t.totalPages}},error:null};case n:return{...e,error:t.error};case i:return{...e,isLoading:t.isLoading};default:return e}},actions:d,selectors:g,resolvers:y});(0,r.register)(m);const w=m;(window.dokan=window.dokan||{}).productsStore=t})();