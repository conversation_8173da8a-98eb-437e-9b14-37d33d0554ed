!function(e){const i={init(){e(".product-cat-stack-dokan li.has-children").on("click","> a span.caret-icon",this.toggle_product_widget_category_dropdown),e(".store-cat-stack-dokan li.has-children").on("click","> a span.caret-icon",this.toggle_store_category_widget_dropdown)},toggle_product_widget_category_dropdown(i){i.preventDefault();let t=e(this),s=t.closest("li.has-children");s.find("> ul.children").is(":visible")||(t.find("i.fa").addClass("fa-rotate-90"),s.find("> ul.children").hasClass("level-0")&&t.closest("a").css({borderBottom:"none"})),s.find("> ul.children").slideToggle("fast",function(){e(this).is(":visible")||(t.find("i.fa").removeClass("fa-rotate-90"),s.find("> ul.children").hasClass("level-0")&&t.closest("a").css({borderBottom:"1px solid #eee"}))})},toggle_store_category_widget_dropdown(i){i.preventDefault();var t=e(this),s=t.closest("li.has-children");s.find("> ul.children").is(":visible")||(t.find("i.fa").addClass("fa-rotate-90"),s.find("> ul.children").hasClass("level-0")&&t.closest("a").css({borderBottom:"none"})),s.find("> ul.children").slideToggle("fast",function(){e(this).is(":visible")||(t.find("i.fa").removeClass("fa-rotate-90"),s.find("> ul.children").hasClass("level-0")&&t.closest("a").css({borderBottom:"1px solid #eee"}))})},init_category_widget_css(){e(".cat-drop-stack ul li.has-children.parent-cat-wrap").find("ul.children.level-0").each(function(i,t){e(t).parent().find("a span.caret-icon").each(function(e,i){i.click()})});let i=e(".cat-drop-stack ul").find("a.selected");i.css({fontWeight:"bold"}),i.parents("ul.children").each(function(i,t){e(t).css({display:"block"})})}};e(function(){i.init(),i.init_category_widget_css()})}(jQuery,document,window);