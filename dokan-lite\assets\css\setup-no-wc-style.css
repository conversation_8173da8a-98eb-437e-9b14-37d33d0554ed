body {
  margin: 65px auto 24px;
  box-shadow: none;
  background: #f1f1f1;
  padding: 0;
}
#wc-logo {
  border: 0;
  margin: 0 0 24px;
  padding: 0;
  text-align: center;
}
#wc-logo img {
  max-width: 30%;
}
.wc-setup-content {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.13);
  padding: 2em;
  margin: 0 0 20px;
  background: #fff;
  overflow: hidden;
  zoom: 1;
  text-align: left;
}
.wc-setup-content h1 {
  margin: 0 0 20px;
  border: 0;
  padding: 0;
  color: #666;
  clear: none;
  font-weight: 500;
}
.wc-setup-content p {
  margin: 20px 0;
  font-size: 1em;
  line-height: 1.75;
  color: #666;
}
.wc-setup-content table {
  font-size: 1em;
  line-height: 1.75;
  color: #666;
}
.wc-setup-content a {
  color: #a16696;
  text-decoration: none;
}
.wc-setup-content a:hover,
.wc-setup-content a:focus {
  color: #111;
}
.wc-setup-content .dokan-no-wc-warning {
  border: 1px solid #f1f1f1;
  padding: 10px 12px;
  border-left-width: 4px;
  border-left-color: #dc3232;
}
.wc-setup .wc-setup-actions {
  overflow: hidden;
  margin: 20px 0 0;
  position: relative;
}
.wc-setup .wc-setup-actions .button-primary {
  background-color: #bb77ae;
  border-color: #a36597;
  text-shadow: none;
  margin: 0;
  opacity: 1;
}
.wc-setup .wc-setup-actions .button-primary:hover,
.wc-setup .wc-setup-actions .button-primary:focus,
.wc-setup .wc-setup-actions .button-primary:active {
  background: #a36597;
  border-color: #a36597;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25), 0 1px 0 #a36597;
}
.wc-setup-content p:last-child {
  margin-bottom: 0;
}
.step {
  text-align: center;
}
.wc-setup .wc-setup-actions .button {
  font-weight: 300;
  font-size: 16px;
  padding: 1em 2em;
  box-shadow: none;
  min-width: 12em;
  margin-top: 10px;
  line-height: 1;
  margin-right: 0.5em;
  margin-bottom: 2px;
  height: auto;
  border-radius: 4px;
}
.wc-setup .wc-setup-actions .button:focus,
.wc-setup .wc-setup-actions .button:hover,
.wc-setup .wc-setup-actions .button:active {
  box-shadow: none;
}
@media only screen and (max-width: 400px) {
  #wc-logo img {
    max-width: 80%;
  }
}
@keyframes spin {
  100% {
    transform: rotate(360deg);
  }
}
.blockUI.blockOverlay::before {
  height: 1em;
  width: 1em;
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -0.5em;
  margin-top: -0.5em;
  content: '';
  -webkit-animation: spin 1s ease-in-out infinite;
  animation: spin 1s ease-in-out infinite;
  background: url(../js/../images/spinner-2x.gif) center center;
  background-size: cover;
  line-height: 1;
  text-align: center;
  font-size: 2em;
  color: rgba(0, 0, 0, 0.75);
}
.dokan-spinner {
  position: absolute;
  width: 20px;
  height: 20px;
  background: url(../js/../images/spinner-2x.gif) center center no-repeat;
  z-index: 99;
  background-size: 20px;
}
/* Variables */
/* Toggle switch */
.form-table .updated p {
  margin-bottom: 10px;
}
.form-table .switch-input {
  display: none;
}
.form-table .switch-label {
  position: relative;
  display: inline-block;
  cursor: pointer;
  font-weight: 500;
  text-align: left;
  margin: 2px 0;
  padding: 0 0 0 44px;
}
.form-table .switch-label:before,
.form-table .switch-label:after {
  content: "";
  position: absolute;
  margin: 0;
  outline: 0;
  top: 50%;
  -ms-transform: translate(0, -50%);
  -webkit-transform: translate(0, -50%);
  transform: translate(0, -50%);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.form-table .switch-label:before {
  left: 1px;
  width: 34px;
  height: 14px;
  background-color: #9E9E9E;
  border-radius: 8px;
}
.form-table .switch-label:after {
  left: 0;
  width: 20px;
  height: 20px;
  background-color: #FAFAFA;
  border-radius: 50%;
  box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.14), 0 2px 2px 0 rgba(0, 0, 0, 0.098), 0 1px 5px 0 rgba(0, 0, 0, 0.084);
}
.form-table .switch-label .toggle--on {
  display: none;
}
.form-table .switch-label .toggle--off {
  display: inline-block;
}
.form-table .switch-input:checked + .switch-label:before {
  background-color: #c9baf8;
}
.form-table .switch-input:checked + .switch-label:after {
  background-color: #7047EB;
  -ms-transform: translate(80%, -50%);
  -webkit-transform: translate(80%, -50%);
  transform: translate(80%, -50%);
}
.form-table .switch-input:checked + .switch-label .toggle--on {
  display: inline-block;
}
.form-table .switch-input:checked + .switch-label .toggle--off {
  display: none;
}
.form-table .list-unstyled {
  padding: 0;
  margin-top: 0;
}
.form-table .list-unstyled li {
  list-style-type: none;
  margin-bottom: 5px;
}
.form-table .dokan-bank-settings-template {
  padding: 0 15px;
  border: 1px solid #EEEEEE;
}
.form-table .dokan-bank-settings-template .dokan-form-group {
  height: 70px !important;
}
.form-table .dokan-bank-settings-template .dokan-form-group input[name="settings[bank][routing_number]"],
.form-table .dokan-bank-settings-template .dokan-form-group input[name="settings[bank][ac_number]"] {
  width: 100%;
}
.form-table .dokan-bank-settings-template .dokan-form-group textarea[name="settings[bank][bank_addr]"] {
  height: 45px !important;
  border-radius: 4px !important;
}
.form-table .dokan-bank-settings-template .dokan-form-group input,
.form-table .dokan-bank-settings-template .dokan-form-group select,
.form-table .dokan-bank-settings-template .dokan-form-group textarea {
  margin-top: 5px;
  border: 1px solid #BBBBBB;
}
.form-table .dokan-bank-settings-template .dokan-form-group input[type=checkbox] {
  margin-top: 0;
}
.form-table .dokan-bank-settings-template .dokan-form-group img {
  height: 60px;
}
.form-table .dokan-bank-settings-template .dokan-form-group #declaration {
  width: auto !important;
}
.form-table .dokan-bank-settings-template div.data-warning {
  display: flex !important;
  box-shadow: 0 0 5px #AAAAAA !important;
  padding: 10px !important;
  margin-bottom: 10px !important;
}
.form-table .dokan-bank-settings-template div.data-warning div.left-icon-container {
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  padding: 5px !important;
}
.form-table .dokan-bank-settings-template div.data-warning div.left-icon-container i {
  color: orange !important;
}
.form-table .dokan-bank-settings-template div.data-warning div.vr-separator {
  margin: 0 10px !important;
  border-left: 1px #dddddd solid !important;
}
.form-table .dokan-bank-settings-template .bottom-note {
  text-align: left !important;
  margin-bottom: 2em !important;
}
.dokan-form-group .dokan-w8 {
  margin-bottom: 10px;
}
.dokan-form-group textarea {
  font-size: 15px;
}
/* WC Overrides */
.wc-setup .wc-setup-steps li.done,
.wc-setup .wc-setup-steps li.active {
  color: #7047EB;
  border-color: #7047EB;
}
.wc-setup .wc-setup-steps li.done:before,
.wc-setup .wc-setup-steps li.active:before {
  background: #7047EB;
  border-color: #7047EB;
}
.wc-setup .wc-setup-steps li.active:before {
  background: #fff;
}
.wc-setup .wc-setup-actions .button {
  color: #555;
  -webkit-transition: background-color 0.3s ease;
  transition: background-color 0.3s ease;
}
.wc-setup .wc-setup-actions .button-primary {
  color: #fff;
  background: #7047EB;
  border-color: #7047EB;
  text-shadow: none;
}
.wc-setup .wc-setup-actions .button-primary:hover,
.wc-setup .wc-setup-actions .button-primary:active,
.wc-setup .wc-setup-actions .button-primary:focus {
  background-color: #4c19e6;
  border-color: #4c19e6;
}
.wc-setup .wc-setup-content a {
  color: #7047EB;
}
.wc-setup .wc-setup-content .checkbox input[type=checkbox]:checked + label::before {
  background: #7047EB;
  border-color: #7047EB;
}
.wc-setup .wc-setup-content .checkbox label {
  font-weight: 400;
  line-height: 1.7;
}
.wc-setup .wc-setup-content table {
  font-weight: 400;
}
.wc-setup .wc-setup-content table th {
  font-weight: 500;
}
.wc-wizard-service-item .wc-wizard-service-name {
  padding-left: 20px;
}
.wc-wizard-service-item .wc-wizard-service-name p {
  text-align: left;
}
.wc-wizard-service-item .dokan-wizard-service-enable {
  flex-basis: 0;
  min-width: 75px;
  text-align: center;
  cursor: pointer;
  padding: 2em 0;
  position: relative;
  max-height: 1.5em;
  align-self: flex-start;
}
.wc-wizard-service-item .dokan-wizard-service-enable .switch-label {
  left: 20px;
}
.dokan-setup-done h1 {
  text-align: center;
}
.dokan-setup-done img {
  display: block;
  margin-left: auto;
  margin-right: auto;
  padding-bottom: 30px;
}
.dokan-setup-done-content {
  display: flex;
  justify-content: center;
}
.margin-bottom-10 {
  margin-bottom: 10px;
}
.wc-wizard-service-item .dokan-wc-wizard-service-enable {
  -webkit-flex-basis: 0;
  flex-basis: 0;
  min-width: 75px;
  text-align: center;
  cursor: pointer;
  padding: 2em 0;
  position: relative;
  max-height: 1.5em;
  -webkit-align-self: flex-start;
  align-self: flex-start;
  -webkit-box-ordinal-group: 4;
  -webkit-order: 3;
  order: 3;
}
.dokan-admin-setup-wizard {
  border: none;
}
.dokan-vendor-setup-wizard {
  border: none;
}
.dokan-vendor-setup-wizard #wc-logo img {
  max-width: 100%;
}
.dokan-vendor-setup-wizard .wc-setup-content .dokan-seller-setup-form .dokan-map-wrap .dokan-map-search-bar input#dokan-map-add {
  width: 100%;
}
.dokan-vendor-setup-wizard .wc-setup-content .dokan-seller-setup-form .dokan-map-wrap .dokan-map-search-bar a#dokan-location-find-btn {
  display: none;
}
.dokan-vendor-setup-wizard .wc-setup-content .dokan-seller-setup-form .dokan-map-wrap .dokan-google-map {
  width: 100%;
  height: 300px;
}
span.required {
  color: #e2401c;
}
.wc-return-to-dashboard {
  text-align: center;
  display: block;
  color: #bdbdbd;
  font-size: 12px;
  text-decoration: none;
}
.wc-return-to-dashboard:hover {
  color: #bdbdbd;
  text-decoration: underline;
}

