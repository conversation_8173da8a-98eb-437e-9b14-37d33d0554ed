/**
 * Dokan Plugin main Style less file
 *
 * Import all individual page styles
 * in this less file which compile to style.css
 * file in assets/css directoryS
 */
.dokan-spinner {
  position: absolute;
  width: 20px;
  height: 20px;
  background: url(../js/../images/spinner-2x.gif) center center no-repeat;
  z-index: 99;
  background-size: 20px;
}
.dokan-close {
  float: left;
  font-weight: bold;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.2;
  filter: alpha(opacity=20);
}
.dokan-close:hover,
.dokan-close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
  opacity: 0.5;
  filter: alpha(opacity=50);
}
button.dokan-close {
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none;
}
.dokan-alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px;
}
.dokan-alert h4 {
  margin-top: 0;
  color: inherit;
}
.dokan-alert .dokan-alert-link {
  font-weight: bold;
}
.dokan-alert > p,
.dokan-alert > ul {
  margin-bottom: 0 !important;
}
.dokan-alert > p + p {
  margin-top: 5px;
}
.dokan-alert-dismissable,
.dokan-alert-dismissible {
  padding-left: 35px;
}
.dokan-alert-dismissable .close,
.dokan-alert-dismissible .close {
  position: relative;
  top: -2px;
  left: -21px;
  color: inherit;
}
.dokan-alert-success {
  background-color: #DAF8E6;
  border-color: #c5f4d0;
  color: #004434;
}
.dokan-alert-success hr {
  border-top-color: #aff0be;
}
.dokan-alert-success .dokan-alert-link {
  color: #00110d;
}
.dokan-alert-info {
  background-color: #E9F9FF;
  border-color: #c5f9ff;
  color: #0B76B7;
}
.dokan-alert-info hr {
  border-top-color: #acf6ff;
}
.dokan-alert-info .dokan-alert-link {
  color: #085787;
}
.dokan-alert-warning {
  background-color: #FFFBEB;
  border-color: #ffeed1;
  color: #9D5425;
}
.dokan-alert-warning hr {
  border-top-color: #ffe5b8;
}
.dokan-alert-warning .dokan-alert-link {
  color: #743e1b;
}
.dokan-alert-danger {
  background-color: #FEF3F3;
  border-color: #fcdbe1;
  color: #BC1C21;
}
.dokan-alert-danger hr {
  border-top-color: #fac4cd;
}
.dokan-alert-danger .dokan-alert-link {
  color: #901519;
}
.tooltip {
  position: absolute;
  z-index: 9999;
  display: block;
  visibility: visible;
  line-height: 1.3;
  opacity: 0;
  filter: alpha(opacity=0);
}
.tooltip.in {
  opacity: 0.9;
  filter: alpha(opacity=90);
}
.tooltip.top {
  margin-top: -3px;
  padding: 5px 0;
}
.tooltip.right {
  margin-right: 3px;
  padding: 0 5px;
}
.tooltip.bottom {
  margin-top: 3px;
  padding: 5px 0;
}
.tooltip.left {
  margin-right: -3px;
  padding: 0 5px;
}
.tooltip-inner {
  max-width: 200px;
  padding: 10px !important;
  color: #fff;
  text-align: center;
  text-decoration: none;
  background-color: #000;
  border-radius: 4px;
  font-weight: normal !important;
}
.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.tooltip.top .tooltip-arrow {
  bottom: 0;
  right: 50%;
  margin-right: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000;
}
.tooltip.top-left .tooltip-arrow {
  bottom: 0;
  right: 5px;
  border-width: 5px 5px 0;
  border-top-color: #000;
}
.tooltip.top-right .tooltip-arrow {
  bottom: 0;
  left: 5px;
  border-width: 5px 5px 0;
  border-top-color: #000;
}
.tooltip.right .tooltip-arrow {
  top: 50%;
  right: 0;
  margin-top: -5px;
  border-width: 5px 0 5px 5px;
  border-left-color: #000;
}
.tooltip.left .tooltip-arrow {
  top: 50%;
  left: 0;
  margin-top: -5px;
  border-width: 5px 5px 5px 0;
  border-right-color: #000;
}
.tooltip.bottom .tooltip-arrow {
  top: 0;
  right: 50%;
  margin-right: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000;
}
.tooltip.bottom-left .tooltip-arrow {
  top: 0;
  right: 5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000;
}
.tooltip.bottom-right .tooltip-arrow {
  top: 0;
  left: 5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000;
}
.dokan-form-container label {
  display: inline-block;
  max-width: 100%;
  margin-bottom: 5px;
}
.dokan-form-control {
  background-color: #ffffff;
  background-image: none;
  border: 1px solid #EDEDED;
  padding: 4px 6px;
  border-radius: 0;
  color: #555555;
  display: block;
  font-size: 14px;
  min-height: 26px;
  line-height: 20px;
  vertical-align: middle;
  width: 100%;
  margin: 0;
}
.dokan-form-control p {
  margin-bottom: 0;
}
textarea.dokan-form-control {
  height: auto;
}
select.dokan-form-control {
  height: 35px;
}
.dokan-radio-inline + .dokan-radio-inline,
.dokan-checkbox-inline + .dokan-checkbox-inline {
  margin-right: 10px;
  margin-top: 0;
}
.dokan-form-group {
  margin-bottom: 15px;
}
.dokan-input-group {
  position: relative;
  display: table;
  border-collapse: separate;
}
.dokan-input-group[class*='col-'] {
  float: none;
  padding-right: 0;
  padding-left: 0;
}
.dokan-input-group .dokan-form-control {
  position: relative;
  z-index: 2;
  float: right;
  width: 100%;
  margin-bottom: 0;
}
.dokan-input-group-addon,
.dokan-input-group-btn,
.dokan-input-group .form-control {
  display: table-cell;
}
.dokan-input-group-addon:not(:first-child):not(:last-child),
.dokan-input-group-btn:not(:first-child):not(:last-child),
.dokan-input-group .form-control:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.dokan-input-group-addon,
.dokan-input-group-btn {
  width: 1%;
  white-space: nowrap;
  vertical-align: middle;
}
.dokan-input-group-addon {
  padding: 6px 12px;
  font-weight: normal;
  line-height: 1;
  color: #555;
  text-align: center;
  background-color: #eee;
  border: 1px solid #EDEDED;
  border-radius: 4px;
}
.dokan-input-group-addon input[type='radio'],
.dokan-input-group-addon input[type='checkbox'] {
  margin-top: 0;
}
.dokan-input-group .dokan-form-control:first-child,
.dokan-input-group-addon:first-child,
.dokan-input-group-btn:first-child > .btn,
.dokan-input-group-btn:first-child > .btn-group > .btn,
.dokan-input-group-btn:first-child > .dropdown-toggle,
.dokan-input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle),
.dokan-input-group-btn:last-child > .btn-group:not(:last-child) > .btn {
  border-bottom-left-radius: 0px;
  border-top-left-radius: 0px;
}
.dokan-input-group-addon:first-child {
  border-left: 0;
}
.dokan-input-group .dokan-form-control:last-child,
.dokan-input-group-addon:last-child,
.dokan-input-group-btn:last-child > .btn,
.dokan-input-group-btn:last-child > .btn-group > .btn,
.dokan-input-group-btn:last-child > .dropdown-toggle,
.dokan-input-group-btn:first-child > .btn:not(:first-child),
.dokan-input-group-btn:first-child > .btn-group:not(:first-child) > .btn {
  border-bottom-right-radius: 0px;
  border-top-right-radius: 0px;
}
.dokan-input-group-addon:last-child {
  border-right: 0;
}
.dokan-input-group-btn {
  position: relative;
  font-size: 0;
  white-space: nowrap;
}
.dokan-input-group-btn > .btn {
  position: relative;
}
.dokan-input-group-btn > .btn + .btn {
  margin-right: -1px;
}
.dokan-input-group-btn > .btn:hover,
.dokan-input-group-btn > .btn:focus,
.dokan-input-group-btn > .btn:active {
  z-index: 2;
}
.dokan-input-group-btn:first-child > .btn,
.dokan-input-group-btn:first-child > .btn-group {
  margin-left: -1px;
}
.dokan-input-group-btn:last-child > .btn,
.dokan-input-group-btn:last-child > .btn-group {
  margin-right: -1px;
}
.dokan-form-horizontal {
  text-align: center;
}
.dokan-form-horizontal label {
  display: inline-block;
  max-width: 100%;
  margin-bottom: 5px;
}
.dokan-form-horizontal .dokan-form-group:before,
.dokan-form-horizontal .dokan-form-group:after {
  display: table;
  content: ' ';
}
.dokan-form-horizontal .dokan-form-group:after {
  clear: both;
}
.dokan-form-horizontal .dokan-control-label {
  text-align: left;
  margin-bottom: 0;
  margin-top: 0;
  padding-left: 15px;
  font-weight: bold;
}
@media (max-width: 430px) {
  .dokan-form-horizontal .dokan-control-label {
    text-align: right;
  }
}
.dokan-row {
  margin-left: -15px;
  margin-right: -15px;
}
.dokan-w1 {
  width: 8.33333333%;
  float: right;
}
.dokan-w2 {
  width: 16.66666667%;
  float: right;
}
.dokan-w3 {
  width: 25%;
  float: right;
}
.dokan-w4 {
  width: 33.33333333%;
  float: right;
}
.dokan-w5 {
  width: 41.66666667%;
  float: right;
}
.dokan-w6 {
  width: 50%;
  float: right;
}
.dokan-w7 {
  width: 63%;
  float: right;
}
.dokan-w8 {
  width: 58.33333333%;
  float: right;
}
.dokan-w9 {
  width: 75%;
  float: right;
}
.dokan-w10 {
  width: 83.33333333%;
  float: right;
}
.dokan-w11 {
  width: 91.66666667%;
  float: right;
}
.dokan-w12 {
  width: 100%;
  float: right;
}
.dokan-text-left {
  text-align: right;
}
.dokan-text-right {
  text-align: left;
}
@media (max-width: 430px) {
  .dokan-w3,
  .dokan-w4,
  .dokan-w5,
  .dokan-w6,
  .dokan-w7,
  .dokan-w8,
  .dokan-w9,
  .dokan-w10,
  .dokan-w11 {
    width: 100% !important;
  }
}
.woocommerce .form-row.has-error {
  background-color: #ffe5e5;
  padding: 10px;
  border-radius: 3px;
}
.woocommerce form.register.dokan-vendor-register {
  border: none;
  padding: 0;
}
.woocommerce form.register.dokan-vendor-register .name-field {
  display: flex;
  justify-content: space-between;
}
.woocommerce form.register.dokan-vendor-register .name-field p {
  width: 48%;
}
.wp-editor-wrap {
  border: 1px solid #EDEDED;
}
.dokan-message,
.dokan-info,
.dokan-error {
  padding: 15px 50px 15px 15px;
  margin: 5px 0 15px 0;
  position: relative;
  background: #fff;
  border-bottom: 1px solid #EDEDED;
  border-right: 1px solid #EDEDED;
  border-left: 1px solid #EDEDED;
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
}
.dokan-message:before,
.dokan-info:before,
.dokan-error:before {
  position: absolute;
  top: 0;
  right: 15px;
  padding-top: 15px;
  font-family: sans-serif;
  color: #fff;
  width: 20px;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
  text-align: center;
}
.dokan-message {
  border-top: 3px solid #8fae1b;
}
.dokan-message:before {
  background-color: #8fae1b;
  content: '\2713';
}
.dokan-info {
  border-top: 3px solid #109ae7;
}
.dokan-info:before {
  background-color: #109ae7;
  content: '\2713';
  content: 'i';
  font-family: Times, Georgia, serif;
  font-style: italic;
}
.dokan-error {
  border-top: 3px solid #b81c23;
}
.dokan-error:before {
  background-color: #b81c23;
  content: '\00d7';
  font-weight: 700;
}
ul.dokan_tabs {
  border-bottom: 1px solid #EDEDED;
  margin-bottom: 20px;
  line-height: 24px;
  margin-right: 0;
}
ul.dokan_tabs > li {
  margin-bottom: -1px !important;
}
ul.dokan_tabs li {
  display: inline-block;
  margin-left: 5px !important;
  border: 1px solid #EDEDED;
  border-bottom: none;
}
ul.dokan_tabs li:first-child {
  margin-right: 10px;
}
ul.dokan_tabs li a {
  display: block;
  padding: 6px 8px !important;
}
ul.dokan_tabs li a:hover {
  background-color: #eee;
}
ul.dokan_tabs li.dokan-hide {
  display: none;
}
ul.dokan_tabs li.active {
  border-bottom: 1px solid #fff !important;
}
.dokan-pagination-container {
  text-align: center;
}
.dokan-pagination-container .dokan-pagination {
  display: inline-block;
  padding-right: 0;
}
.dokan-pagination-container .dokan-pagination li {
  display: inline;
}
.dokan-pagination-container .dokan-pagination li a {
  padding: 3px 10px;
  border: 1px solid #EDEDED;
  margin-left: 3px;
  text-decoration: none;
}
.dokan-pagination-container .dokan-pagination li.active a {
  background: #eee;
}
.dokan-pagination-container .dokan-pagination li.disabled a {
  cursor: not-allowed;
  color: #ccc;
}
input[type='submit'].dokan-btn,
a.dokan-btn,
.dokan-btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: normal;
  text-align: center;
  vertical-align: middle;
  touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  white-space: nowrap;
  background-color: #eee;
  color: #444;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}
input[type='submit'].dokan-btn:hover,
a.dokan-btn:hover,
.dokan-btn:hover,
input[type='submit'].dokan-btn:focus,
a.dokan-btn:focus,
.dokan-btn:focus,
input[type='submit'].dokan-btn.focus,
a.dokan-btn.focus,
.dokan-btn.focus {
  color: #fff;
  border-color: var(--dokan-button-background-color, #7047EB);
  text-decoration: none;
  background-color: var(--dokan-button-background-color, #7047EB);
}
input[type='submit'].dokan-btn:active,
a.dokan-btn:active,
.dokan-btn:active,
input[type='submit'].dokan-btn.active,
a.dokan-btn.active,
.dokan-btn.active {
  outline: 0;
  background-image: none;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
input[type='submit'].dokan-btn-theme,
a.dokan-btn-theme,
.dokan-btn-theme {
  color: #fff;
  background-color: #7047EB;
  border-color: #7047EB;
}
input[type='submit'].dokan-btn-theme:hover,
a.dokan-btn-theme:hover,
.dokan-btn-theme:hover,
input[type='submit'].dokan-btn-theme:focus,
a.dokan-btn-theme:focus,
.dokan-btn-theme:focus,
input[type='submit'].dokan-btn-theme:active,
a.dokan-btn-theme:active,
.dokan-btn-theme:active,
input[type='submit'].dokan-btn-theme.active,
a.dokan-btn-theme.active,
.dokan-btn-theme.active,
.open .dropdown-toggleinput[type='submit'].dokan-btn-theme,
.open .dropdown-togglea.dokan-btn-theme,
.open .dropdown-toggle.dokan-btn-theme {
  color: #fff;
  background-color: #5322e7;
  border-color: #4918dd;
}
input[type='submit'].dokan-btn-theme:active,
a.dokan-btn-theme:active,
.dokan-btn-theme:active,
input[type='submit'].dokan-btn-theme.active,
a.dokan-btn-theme.active,
.dokan-btn-theme.active,
.open .dropdown-toggleinput[type='submit'].dokan-btn-theme,
.open .dropdown-togglea.dokan-btn-theme,
.open .dropdown-toggle.dokan-btn-theme {
  background-image: none;
}
input[type='submit'].dokan-btn-theme.disabled,
a.dokan-btn-theme.disabled,
.dokan-btn-theme.disabled,
input[type='submit'].dokan-btn-theme[disabled],
a.dokan-btn-theme[disabled],
.dokan-btn-theme[disabled],
fieldset[disabled] input[type='submit'].dokan-btn-theme,
fieldset[disabled] a.dokan-btn-theme,
fieldset[disabled] .dokan-btn-theme,
input[type='submit'].dokan-btn-theme.disabled:hover,
a.dokan-btn-theme.disabled:hover,
.dokan-btn-theme.disabled:hover,
input[type='submit'].dokan-btn-theme[disabled]:hover,
a.dokan-btn-theme[disabled]:hover,
.dokan-btn-theme[disabled]:hover,
fieldset[disabled] input[type='submit'].dokan-btn-theme:hover,
fieldset[disabled] a.dokan-btn-theme:hover,
fieldset[disabled] .dokan-btn-theme:hover,
input[type='submit'].dokan-btn-theme.disabled:focus,
a.dokan-btn-theme.disabled:focus,
.dokan-btn-theme.disabled:focus,
input[type='submit'].dokan-btn-theme[disabled]:focus,
a.dokan-btn-theme[disabled]:focus,
.dokan-btn-theme[disabled]:focus,
fieldset[disabled] input[type='submit'].dokan-btn-theme:focus,
fieldset[disabled] a.dokan-btn-theme:focus,
fieldset[disabled] .dokan-btn-theme:focus,
input[type='submit'].dokan-btn-theme.disabled:active,
a.dokan-btn-theme.disabled:active,
.dokan-btn-theme.disabled:active,
input[type='submit'].dokan-btn-theme[disabled]:active,
a.dokan-btn-theme[disabled]:active,
.dokan-btn-theme[disabled]:active,
fieldset[disabled] input[type='submit'].dokan-btn-theme:active,
fieldset[disabled] a.dokan-btn-theme:active,
fieldset[disabled] .dokan-btn-theme:active,
input[type='submit'].dokan-btn-theme.disabled.active,
a.dokan-btn-theme.disabled.active,
.dokan-btn-theme.disabled.active,
input[type='submit'].dokan-btn-theme[disabled].active,
a.dokan-btn-theme[disabled].active,
.dokan-btn-theme[disabled].active,
fieldset[disabled] input[type='submit'].dokan-btn-theme.active,
fieldset[disabled] a.dokan-btn-theme.active,
fieldset[disabled] .dokan-btn-theme.active {
  background-color: #9475f0;
  border-color: #9475f0;
}
input[type='submit'].dokan-btn-theme .badge,
a.dokan-btn-theme .badge,
.dokan-btn-theme .badge {
  color: #7047EB;
  background-color: #fff;
}
input[type='submit'].dokan-btn-success,
a.dokan-btn-success,
.dokan-btn-success {
  color: #fff !important;
  background-color: #22AD5C !important;
  border-color: #1e9851 !important;
}
input[type='submit'].dokan-btn-success:hover,
a.dokan-btn-success:hover,
.dokan-btn-success:hover,
input[type='submit'].dokan-btn-success:focus,
a.dokan-btn-success:focus,
.dokan-btn-success:focus,
input[type='submit'].dokan-btn-success:active,
a.dokan-btn-success:active,
.dokan-btn-success:active,
input[type='submit'].dokan-btn-success.active,
a.dokan-btn-success.active,
.dokan-btn-success.active,
.open .dropdown-toggleinput[type='submit'].dokan-btn-success,
.open .dropdown-togglea.dokan-btn-success,
.open .dropdown-toggle.dokan-btn-success {
  color: #fff !important;
  background-color: #1b8b4a !important;
  border-color: #146535 !important;
}
input[type='submit'].dokan-btn-success:active,
a.dokan-btn-success:active,
.dokan-btn-success:active,
input[type='submit'].dokan-btn-success.active,
a.dokan-btn-success.active,
.dokan-btn-success.active,
.open .dropdown-toggleinput[type='submit'].dokan-btn-success,
.open .dropdown-togglea.dokan-btn-success,
.open .dropdown-toggle.dokan-btn-success {
  background-image: none !important;
}
input[type='submit'].dokan-btn-success.disabled,
a.dokan-btn-success.disabled,
.dokan-btn-success.disabled,
input[type='submit'].dokan-btn-success[disabled],
a.dokan-btn-success[disabled],
.dokan-btn-success[disabled],
fieldset[disabled] input[type='submit'].dokan-btn-success,
fieldset[disabled] a.dokan-btn-success,
fieldset[disabled] .dokan-btn-success,
input[type='submit'].dokan-btn-success.disabled:hover,
a.dokan-btn-success.disabled:hover,
.dokan-btn-success.disabled:hover,
input[type='submit'].dokan-btn-success[disabled]:hover,
a.dokan-btn-success[disabled]:hover,
.dokan-btn-success[disabled]:hover,
fieldset[disabled] input[type='submit'].dokan-btn-success:hover,
fieldset[disabled] a.dokan-btn-success:hover,
fieldset[disabled] .dokan-btn-success:hover,
input[type='submit'].dokan-btn-success.disabled:focus,
a.dokan-btn-success.disabled:focus,
.dokan-btn-success.disabled:focus,
input[type='submit'].dokan-btn-success[disabled]:focus,
a.dokan-btn-success[disabled]:focus,
.dokan-btn-success[disabled]:focus,
fieldset[disabled] input[type='submit'].dokan-btn-success:focus,
fieldset[disabled] a.dokan-btn-success:focus,
fieldset[disabled] .dokan-btn-success:focus,
input[type='submit'].dokan-btn-success.disabled:active,
a.dokan-btn-success.disabled:active,
.dokan-btn-success.disabled:active,
input[type='submit'].dokan-btn-success[disabled]:active,
a.dokan-btn-success[disabled]:active,
.dokan-btn-success[disabled]:active,
fieldset[disabled] input[type='submit'].dokan-btn-success:active,
fieldset[disabled] a.dokan-btn-success:active,
fieldset[disabled] .dokan-btn-success:active,
input[type='submit'].dokan-btn-success.disabled.active,
a.dokan-btn-success.disabled.active,
.dokan-btn-success.disabled.active,
input[type='submit'].dokan-btn-success[disabled].active,
a.dokan-btn-success[disabled].active,
.dokan-btn-success[disabled].active,
fieldset[disabled] input[type='submit'].dokan-btn-success.active,
fieldset[disabled] a.dokan-btn-success.active,
fieldset[disabled] .dokan-btn-success.active {
  background-color: #2cd673 !important;
  border-color: #26c267 !important;
}
input[type='submit'].dokan-btn-success .badge,
a.dokan-btn-success .badge,
.dokan-btn-success .badge {
  color: #22AD5C !important;
  background-color: #fff !important;
}
input[type='submit'].dokan-btn-default,
a.dokan-btn-default,
.dokan-btn-default {
  color: #333;
  background-color: #fff;
  border-color: #ccc;
}
input[type='submit'].dokan-btn-default:hover,
a.dokan-btn-default:hover,
.dokan-btn-default:hover,
input[type='submit'].dokan-btn-default:focus,
a.dokan-btn-default:focus,
.dokan-btn-default:focus,
input[type='submit'].dokan-btn-default:active,
a.dokan-btn-default:active,
.dokan-btn-default:active,
input[type='submit'].dokan-btn-default.active,
a.dokan-btn-default.active,
.dokan-btn-default.active,
.open .dropdown-toggleinput[type='submit'].dokan-btn-default,
.open .dropdown-togglea.dokan-btn-default,
.open .dropdown-toggle.dokan-btn-default {
  color: #333;
  background-color: #ebebeb;
  border-color: #adadad;
}
input[type='submit'].dokan-btn-default:active,
a.dokan-btn-default:active,
.dokan-btn-default:active,
input[type='submit'].dokan-btn-default.active,
a.dokan-btn-default.active,
.dokan-btn-default.active,
.open .dropdown-toggleinput[type='submit'].dokan-btn-default,
.open .dropdown-togglea.dokan-btn-default,
.open .dropdown-toggle.dokan-btn-default {
  background-image: none;
}
input[type='submit'].dokan-btn-default.disabled,
a.dokan-btn-default.disabled,
.dokan-btn-default.disabled,
input[type='submit'].dokan-btn-default[disabled],
a.dokan-btn-default[disabled],
.dokan-btn-default[disabled],
fieldset[disabled] input[type='submit'].dokan-btn-default,
fieldset[disabled] a.dokan-btn-default,
fieldset[disabled] .dokan-btn-default,
input[type='submit'].dokan-btn-default.disabled:hover,
a.dokan-btn-default.disabled:hover,
.dokan-btn-default.disabled:hover,
input[type='submit'].dokan-btn-default[disabled]:hover,
a.dokan-btn-default[disabled]:hover,
.dokan-btn-default[disabled]:hover,
fieldset[disabled] input[type='submit'].dokan-btn-default:hover,
fieldset[disabled] a.dokan-btn-default:hover,
fieldset[disabled] .dokan-btn-default:hover,
input[type='submit'].dokan-btn-default.disabled:focus,
a.dokan-btn-default.disabled:focus,
.dokan-btn-default.disabled:focus,
input[type='submit'].dokan-btn-default[disabled]:focus,
a.dokan-btn-default[disabled]:focus,
.dokan-btn-default[disabled]:focus,
fieldset[disabled] input[type='submit'].dokan-btn-default:focus,
fieldset[disabled] a.dokan-btn-default:focus,
fieldset[disabled] .dokan-btn-default:focus,
input[type='submit'].dokan-btn-default.disabled:active,
a.dokan-btn-default.disabled:active,
.dokan-btn-default.disabled:active,
input[type='submit'].dokan-btn-default[disabled]:active,
a.dokan-btn-default[disabled]:active,
.dokan-btn-default[disabled]:active,
fieldset[disabled] input[type='submit'].dokan-btn-default:active,
fieldset[disabled] a.dokan-btn-default:active,
fieldset[disabled] .dokan-btn-default:active,
input[type='submit'].dokan-btn-default.disabled.active,
a.dokan-btn-default.disabled.active,
.dokan-btn-default.disabled.active,
input[type='submit'].dokan-btn-default[disabled].active,
a.dokan-btn-default[disabled].active,
.dokan-btn-default[disabled].active,
fieldset[disabled] input[type='submit'].dokan-btn-default.active,
fieldset[disabled] a.dokan-btn-default.active,
fieldset[disabled] .dokan-btn-default.active {
  background-color: #ffffff;
  border-color: #e6e6e6;
}
input[type='submit'].dokan-btn-default .badge,
a.dokan-btn-default .badge,
.dokan-btn-default .badge {
  color: #fff;
  background-color: #fff;
}
input[type='submit'].dokan-btn-danger,
a.dokan-btn-danger,
.dokan-btn-danger {
  color: #fff !important;
  background-color: #F23030 !important;
  border-color: #f01818 !important;
}
input[type='submit'].dokan-btn-danger:hover,
a.dokan-btn-danger:hover,
.dokan-btn-danger:hover,
input[type='submit'].dokan-btn-danger:focus,
a.dokan-btn-danger:focus,
.dokan-btn-danger:focus,
input[type='submit'].dokan-btn-danger:active,
a.dokan-btn-danger:active,
.dokan-btn-danger:active,
input[type='submit'].dokan-btn-danger.active,
a.dokan-btn-danger.active,
.dokan-btn-danger.active,
.open .dropdown-toggleinput[type='submit'].dokan-btn-danger,
.open .dropdown-togglea.dokan-btn-danger,
.open .dropdown-toggle.dokan-btn-danger {
  color: #fff !important;
  background-color: #ea0f0f !important;
  border-color: #bf0c0c !important;
}
input[type='submit'].dokan-btn-danger:active,
a.dokan-btn-danger:active,
.dokan-btn-danger:active,
input[type='submit'].dokan-btn-danger.active,
a.dokan-btn-danger.active,
.dokan-btn-danger.active,
.open .dropdown-toggleinput[type='submit'].dokan-btn-danger,
.open .dropdown-togglea.dokan-btn-danger,
.open .dropdown-toggle.dokan-btn-danger {
  background-image: none !important;
}
input[type='submit'].dokan-btn-danger.disabled,
a.dokan-btn-danger.disabled,
.dokan-btn-danger.disabled,
input[type='submit'].dokan-btn-danger[disabled],
a.dokan-btn-danger[disabled],
.dokan-btn-danger[disabled],
fieldset[disabled] input[type='submit'].dokan-btn-danger,
fieldset[disabled] a.dokan-btn-danger,
fieldset[disabled] .dokan-btn-danger,
input[type='submit'].dokan-btn-danger.disabled:hover,
a.dokan-btn-danger.disabled:hover,
.dokan-btn-danger.disabled:hover,
input[type='submit'].dokan-btn-danger[disabled]:hover,
a.dokan-btn-danger[disabled]:hover,
.dokan-btn-danger[disabled]:hover,
fieldset[disabled] input[type='submit'].dokan-btn-danger:hover,
fieldset[disabled] a.dokan-btn-danger:hover,
fieldset[disabled] .dokan-btn-danger:hover,
input[type='submit'].dokan-btn-danger.disabled:focus,
a.dokan-btn-danger.disabled:focus,
.dokan-btn-danger.disabled:focus,
input[type='submit'].dokan-btn-danger[disabled]:focus,
a.dokan-btn-danger[disabled]:focus,
.dokan-btn-danger[disabled]:focus,
fieldset[disabled] input[type='submit'].dokan-btn-danger:focus,
fieldset[disabled] a.dokan-btn-danger:focus,
fieldset[disabled] .dokan-btn-danger:focus,
input[type='submit'].dokan-btn-danger.disabled:active,
a.dokan-btn-danger.disabled:active,
.dokan-btn-danger.disabled:active,
input[type='submit'].dokan-btn-danger[disabled]:active,
a.dokan-btn-danger[disabled]:active,
.dokan-btn-danger[disabled]:active,
fieldset[disabled] input[type='submit'].dokan-btn-danger:active,
fieldset[disabled] a.dokan-btn-danger:active,
fieldset[disabled] .dokan-btn-danger:active,
input[type='submit'].dokan-btn-danger.disabled.active,
a.dokan-btn-danger.disabled.active,
.dokan-btn-danger.disabled.active,
input[type='submit'].dokan-btn-danger[disabled].active,
a.dokan-btn-danger[disabled].active,
.dokan-btn-danger[disabled].active,
fieldset[disabled] input[type='submit'].dokan-btn-danger.active,
fieldset[disabled] a.dokan-btn-danger.active,
fieldset[disabled] .dokan-btn-danger.active {
  background-color: #f56060 !important;
  border-color: #f44848 !important;
}
input[type='submit'].dokan-btn-danger .badge,
a.dokan-btn-danger .badge,
.dokan-btn-danger .badge {
  color: #F23030 !important;
  background-color: #fff !important;
}
input[type='submit'].dokan-btn-info,
a.dokan-btn-info,
.dokan-btn-info {
  color: #fff;
  background-color: #0B76B7;
  border-color: #0a669f;
}
input[type='submit'].dokan-btn-info:hover,
a.dokan-btn-info:hover,
.dokan-btn-info:hover,
input[type='submit'].dokan-btn-info:focus,
a.dokan-btn-info:focus,
.dokan-btn-info:focus,
input[type='submit'].dokan-btn-info:active,
a.dokan-btn-info:active,
.dokan-btn-info:active,
input[type='submit'].dokan-btn-info.active,
a.dokan-btn-info.active,
.dokan-btn-info.active,
.open .dropdown-toggleinput[type='submit'].dokan-btn-info,
.open .dropdown-togglea.dokan-btn-info,
.open .dropdown-toggle.dokan-btn-info {
  color: #fff;
  background-color: #095d91;
  border-color: #064165;
}
input[type='submit'].dokan-btn-info:active,
a.dokan-btn-info:active,
.dokan-btn-info:active,
input[type='submit'].dokan-btn-info.active,
a.dokan-btn-info.active,
.dokan-btn-info.active,
.open .dropdown-toggleinput[type='submit'].dokan-btn-info,
.open .dropdown-togglea.dokan-btn-info,
.open .dropdown-toggle.dokan-btn-info {
  background-image: none;
}
input[type='submit'].dokan-btn-info.disabled,
a.dokan-btn-info.disabled,
.dokan-btn-info.disabled,
input[type='submit'].dokan-btn-info[disabled],
a.dokan-btn-info[disabled],
.dokan-btn-info[disabled],
fieldset[disabled] input[type='submit'].dokan-btn-info,
fieldset[disabled] a.dokan-btn-info,
fieldset[disabled] .dokan-btn-info,
input[type='submit'].dokan-btn-info.disabled:hover,
a.dokan-btn-info.disabled:hover,
.dokan-btn-info.disabled:hover,
input[type='submit'].dokan-btn-info[disabled]:hover,
a.dokan-btn-info[disabled]:hover,
.dokan-btn-info[disabled]:hover,
fieldset[disabled] input[type='submit'].dokan-btn-info:hover,
fieldset[disabled] a.dokan-btn-info:hover,
fieldset[disabled] .dokan-btn-info:hover,
input[type='submit'].dokan-btn-info.disabled:focus,
a.dokan-btn-info.disabled:focus,
.dokan-btn-info.disabled:focus,
input[type='submit'].dokan-btn-info[disabled]:focus,
a.dokan-btn-info[disabled]:focus,
.dokan-btn-info[disabled]:focus,
fieldset[disabled] input[type='submit'].dokan-btn-info:focus,
fieldset[disabled] a.dokan-btn-info:focus,
fieldset[disabled] .dokan-btn-info:focus,
input[type='submit'].dokan-btn-info.disabled:active,
a.dokan-btn-info.disabled:active,
.dokan-btn-info.disabled:active,
input[type='submit'].dokan-btn-info[disabled]:active,
a.dokan-btn-info[disabled]:active,
.dokan-btn-info[disabled]:active,
fieldset[disabled] input[type='submit'].dokan-btn-info:active,
fieldset[disabled] a.dokan-btn-info:active,
fieldset[disabled] .dokan-btn-info:active,
input[type='submit'].dokan-btn-info.disabled.active,
a.dokan-btn-info.disabled.active,
.dokan-btn-info.disabled.active,
input[type='submit'].dokan-btn-info[disabled].active,
a.dokan-btn-info[disabled].active,
.dokan-btn-info[disabled].active,
fieldset[disabled] input[type='submit'].dokan-btn-info.active,
fieldset[disabled] a.dokan-btn-info.active,
fieldset[disabled] .dokan-btn-info.active {
  background-color: #0e95e7;
  border-color: #0c86cf;
}
input[type='submit'].dokan-btn-info .badge,
a.dokan-btn-info .badge,
.dokan-btn-info .badge {
  color: #0B76B7;
  background-color: #fff;
}
input[type='submit'].dokan-btn-lg,
a.dokan-btn-lg,
.dokan-btn-lg {
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.33;
  border-radius: 6px;
}
input[type='submit'].dokan-btn-sm,
a.dokan-btn-sm,
.dokan-btn-sm {
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.428;
  border-radius: 3px;
}
.dokan-btn-round[class] {
  border-radius: 50%;
  width: 35px;
  height: 34px;
  font-size: 25px;
  line-height: 34px;
}
.dokan-table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px;
}
.dokan-table > thead > tr > th,
.dokan-table > tbody > tr > th,
.dokan-table > tfoot > tr > th,
.dokan-table > thead > tr > td,
.dokan-table > tbody > tr > td,
.dokan-table > tfoot > tr > td {
  padding: 8px;
  line-height: 1.42;
  vertical-align: top;
  border-top: 1px solid #EDEDED;
}
.dokan-table > thead > tr > th {
  vertical-align: bottom;
  border-bottom: 2px solid #EDEDED;
}
.dokan-table > caption + thead > tr:first-child > th,
.dokan-table > colgroup + thead > tr:first-child > th,
.dokan-table > thead:first-child > tr:first-child > th,
.dokan-table > caption + thead > tr:first-child > td,
.dokan-table > colgroup + thead > tr:first-child > td,
.dokan-table > thead:first-child > tr:first-child > td {
  border-top: 0;
}
.dokan-table > tbody + tbody {
  border-top: 2px solid #EDEDED;
}
.dokan-table-striped > tbody > tr:nth-of-type(odd) {
  background-color: #f9f9f9;
}
.table > thead > tr > .active,
.table > tbody > tr > .active,
.table > tfoot > tr > .active,
.table > thead > .active > td,
.table > tbody > .active > td,
.table > tfoot > .active > td,
.table > thead > .active > th,
.table > tbody > .active > th,
.table > tfoot > .active > th {
  background-color: #f5f5f5;
}
.table-hover > tbody > tr > .active:hover,
.table-hover > tbody > .active:hover > td,
.table-hover > tbody > .active:hover > th {
  background-color: #e8e8e8;
}
.dokan-table > thead > tr > td.active,
.dokan-table > tbody > tr > td.active,
.dokan-table > tfoot > tr > td.active,
.dokan-table > thead > tr > th.active,
.dokan-table > tbody > tr > th.active,
.dokan-table > tfoot > tr > th.active,
.dokan-table > thead > tr.active > td,
.dokan-table > tbody > tr.active > td,
.dokan-table > tfoot > tr.active > td,
.dokan-table > thead > tr.active > th,
.dokan-table > tbody > tr.active > th,
.dokan-table > tfoot > tr.active > th {
  background-color: #f5f5f5 !important;
}
.dokan-table-hover > tbody > tr > td.active:hover,
.dokan-table-hover > tbody > tr > th.active:hover,
.dokan-table-hover > tbody > tr.active:hover > td,
.dokan-table-hover > tbody > tr:hover > .active,
.dokan-table-hover > tbody > tr.active:hover > th {
  background-color: #e8e8e8;
}
.table > thead > tr > .success,
.table > tbody > tr > .success,
.table > tfoot > tr > .success,
.table > thead > .success > td,
.table > tbody > .success > td,
.table > tfoot > .success > td,
.table > thead > .success > th,
.table > tbody > .success > th,
.table > tfoot > .success > th {
  background-color: #dff0d8;
}
.table-hover > tbody > tr > .success:hover,
.table-hover > tbody > .success:hover > td,
.table-hover > tbody > .success:hover > th {
  background-color: #d0e9c6;
}
.dokan-table > thead > tr > td.success,
.dokan-table > tbody > tr > td.success,
.dokan-table > tfoot > tr > td.success,
.dokan-table > thead > tr > th.success,
.dokan-table > tbody > tr > th.success,
.dokan-table > tfoot > tr > th.success,
.dokan-table > thead > tr.success > td,
.dokan-table > tbody > tr.success > td,
.dokan-table > tfoot > tr.success > td,
.dokan-table > thead > tr.success > th,
.dokan-table > tbody > tr.success > th,
.dokan-table > tfoot > tr.success > th {
  background-color: #dff0d8 !important;
}
.dokan-table-hover > tbody > tr > td.success:hover,
.dokan-table-hover > tbody > tr > th.success:hover,
.dokan-table-hover > tbody > tr.success:hover > td,
.dokan-table-hover > tbody > tr:hover > .success,
.dokan-table-hover > tbody > tr.success:hover > th {
  background-color: #d0e9c6;
}
.table > thead > tr > .info,
.table > tbody > tr > .info,
.table > tfoot > tr > .info,
.table > thead > .info > td,
.table > tbody > .info > td,
.table > tfoot > .info > td,
.table > thead > .info > th,
.table > tbody > .info > th,
.table > tfoot > .info > th {
  background-color: #d9edf7;
}
.table-hover > tbody > tr > .info:hover,
.table-hover > tbody > .info:hover > td,
.table-hover > tbody > .info:hover > th {
  background-color: #c4e3f3;
}
.dokan-table > thead > tr > td.info,
.dokan-table > tbody > tr > td.info,
.dokan-table > tfoot > tr > td.info,
.dokan-table > thead > tr > th.info,
.dokan-table > tbody > tr > th.info,
.dokan-table > tfoot > tr > th.info,
.dokan-table > thead > tr.info > td,
.dokan-table > tbody > tr.info > td,
.dokan-table > tfoot > tr.info > td,
.dokan-table > thead > tr.info > th,
.dokan-table > tbody > tr.info > th,
.dokan-table > tfoot > tr.info > th {
  background-color: #d9edf7 !important;
}
.dokan-table-hover > tbody > tr > td.info:hover,
.dokan-table-hover > tbody > tr > th.info:hover,
.dokan-table-hover > tbody > tr.info:hover > td,
.dokan-table-hover > tbody > tr:hover > .info,
.dokan-table-hover > tbody > tr.info:hover > th {
  background-color: #c4e3f3;
}
.table > thead > tr > .warning,
.table > tbody > tr > .warning,
.table > tfoot > tr > .warning,
.table > thead > .warning > td,
.table > tbody > .warning > td,
.table > tfoot > .warning > td,
.table > thead > .warning > th,
.table > tbody > .warning > th,
.table > tfoot > .warning > th {
  background-color: #fcf8e3;
}
.table-hover > tbody > tr > .warning:hover,
.table-hover > tbody > .warning:hover > td,
.table-hover > tbody > .warning:hover > th {
  background-color: #faf2cc;
}
.dokan-table > thead > tr > td.warning,
.dokan-table > tbody > tr > td.warning,
.dokan-table > tfoot > tr > td.warning,
.dokan-table > thead > tr > th.warning,
.dokan-table > tbody > tr > th.warning,
.dokan-table > tfoot > tr > th.warning,
.dokan-table > thead > tr.warning > td,
.dokan-table > tbody > tr.warning > td,
.dokan-table > tfoot > tr.warning > td,
.dokan-table > thead > tr.warning > th,
.dokan-table > tbody > tr.warning > th,
.dokan-table > tfoot > tr.warning > th {
  background-color: #fcf8e3 !important;
}
.dokan-table-hover > tbody > tr > td.warning:hover,
.dokan-table-hover > tbody > tr > th.warning:hover,
.dokan-table-hover > tbody > tr.warning:hover > td,
.dokan-table-hover > tbody > tr:hover > .warning,
.dokan-table-hover > tbody > tr.warning:hover > th {
  background-color: #faf2cc;
}
.table > thead > tr > .danger,
.table > tbody > tr > .danger,
.table > tfoot > tr > .danger,
.table > thead > .danger > td,
.table > tbody > .danger > td,
.table > tfoot > .danger > td,
.table > thead > .danger > th,
.table > tbody > .danger > th,
.table > tfoot > .danger > th {
  background-color: #f2dede;
}
.table-hover > tbody > tr > .danger:hover,
.table-hover > tbody > .danger:hover > td,
.table-hover > tbody > .danger:hover > th {
  background-color: #ebcccc;
}
.dokan-table > thead > tr > td.danger,
.dokan-table > tbody > tr > td.danger,
.dokan-table > tfoot > tr > td.danger,
.dokan-table > thead > tr > th.danger,
.dokan-table > tbody > tr > th.danger,
.dokan-table > tfoot > tr > th.danger,
.dokan-table > thead > tr.danger > td,
.dokan-table > tbody > tr.danger > td,
.dokan-table > tfoot > tr.danger > td,
.dokan-table > thead > tr.danger > th,
.dokan-table > tbody > tr.danger > th,
.dokan-table > tfoot > tr.danger > th {
  background-color: #f2dede !important;
}
.dokan-table-hover > tbody > tr > td.danger:hover,
.dokan-table-hover > tbody > tr > th.danger:hover,
.dokan-table-hover > tbody > tr.danger:hover > td,
.dokan-table-hover > tbody > tr:hover > .danger,
.dokan-table-hover > tbody > tr.danger:hover > th {
  background-color: #ebcccc;
}
.chart-tooltip {
  position: absolute;
  display: none;
  line-height: 1;
  background: #333;
  color: #fff;
  padding: 3px 5px;
  font-size: 11px;
  border-radius: 3px;
}
.dokan-tooltips-help {
  margin-right: 7px;
}
.dokan-tooltips-help i {
  color: #ccc;
}
.tooltip-inner {
  font-size: 12px;
}
/**
 * Media Popup style
 */
.media-modal .screen-reader-text {
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}
.chosen-container-multi .chosen-choices li.search-field input[type='text'] {
  height: 28px;
}
.mfp-zoom-out {
  /* start state */
  /* animate in */
  /* animate out */
}
.mfp-zoom-out .mfp-with-anim {
  -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=0)';
  filter: alpha(opacity=0);
  opacity: 0;
  -moz-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  -moz-transform: scale(1.3);
  -ms-transform: scale(1.3);
  -o-transform: scale(1.3);
  -webkit-transform: scale(1.3);
  transform: scale(1.3);
}
.mfp-zoom-out.mfp-bg {
  -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=0)';
  filter: alpha(opacity=0);
  opacity: 0;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.mfp-zoom-out.mfp-ready .mfp-with-anim {
  -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=100)';
  filter: alpha(opacity=100);
  opacity: 1;
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  -webkit-transform: scale(1);
  transform: scale(1);
}
.mfp-zoom-out.mfp-ready.mfp-bg {
  -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=80)';
  filter: alpha(opacity=80);
  opacity: 0.8;
}
.mfp-zoom-out.mfp-removing .mfp-with-anim {
  -moz-transform: scale(1.3);
  -ms-transform: scale(1.3);
  -o-transform: scale(1.3);
  -webkit-transform: scale(1.3);
  transform: scale(1.3);
  -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=0)';
  filter: alpha(opacity=0);
  opacity: 0;
}
.mfp-zoom-out.mfp-removing.mfp-bg {
  -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=0)';
  filter: alpha(opacity=0);
  opacity: 0;
}
.dokan-blur-effect {
  background-color: #eee;
  -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=40)';
  filter: alpha(opacity=40);
  opacity: 0.4;
}
.dokan-right-margin-30 {
  margin-left: 30px;
}
.dokan_tock_check {
  width: auto;
}
table.my_account_orders tbody tr td.order-actions a.button {
  margin-left: 10px;
}
.dokan-dashboard-content ul.dokan_tabs {
  border-bottom: 1px solid #EDEDED;
  margin-bottom: 20px;
  line-height: 24px;
}
.dokan-dashboard-content ul.dokan_tabs > li {
  margin-bottom: -1px !important;
}
.dokan-dashboard-content ul.dokan_tabs li {
  display: inline-block;
  margin-left: 5px !important;
  border: 1px solid #EDEDED;
  border-bottom: none;
}
.dokan-dashboard-content ul.dokan_tabs li:first-child {
  margin-right: 10px;
}
.dokan-dashboard-content ul.dokan_tabs li a {
  color: #6d6d6d;
  display: block;
  padding: 6px 8px !important;
}
.dokan-dashboard-content ul.dokan_tabs li a:hover {
  color: #000;
  background-color: #eee;
}
.dokan-dashboard-content ul.dokan_tabs li.dokan-hide {
  display: none;
}
.dokan-dashboard-content ul.dokan_tabs li.active {
  border-bottom: 1px solid #fff !important;
}
.dokan-dashboard-content ul.dokan_tabs li.active a {
  color: #000;
}
.wc_error_tip {
  max-width: 20em;
  line-height: 1.8em;
  position: absolute;
  white-space: normal;
  background: #d82223;
  margin: 2em -1em 0 1px;
  z-index: 9999999;
  color: #fff;
  font-size: 12px;
  padding: 8px;
}
.wc_error_tip:after {
  content: '';
  display: block;
  border: 8px solid #d82223;
  border-left-color: transparent;
  border-right-color: transparent;
  border-top-color: transparent;
  position: absolute;
  top: -3px;
  right: 50%;
  margin: -1em -3px 0 0;
}
.dokan-label {
  display: inline;
  padding: 0.2em 0.6em 0.3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25em;
}
a.dokan-label:hover,
a.dokan-label:focus {
  color: #fff;
  text-decoration: none;
  cursor: pointer;
}
.dokan-label:empty {
  display: none;
}
.btn .dokan-label {
  position: relative;
  top: -1px;
}
.dokan-label-default {
  color: #fff;
  background-color: #777777;
}
.dokan-label-default[href]:hover,
.dokan-label-default[href]:focus {
  background-color: #5e5e5e;
}
.dokan-label-default[href]:hover,
.dokan-label-default[href]:focus {
  background-color: #5e5e5e;
}
.dokan-label-primary {
  color: #fff;
  background-color: #428bca;
}
.dokan-label-primary[href]:hover,
.dokan-label-primary[href]:focus {
  background-color: #3071a9;
}
.dokan-label-primary[href]:hover,
.dokan-label-primary[href]:focus {
  background-color: #3071a9;
}
.dokan-label-success {
  color: #fff;
  background-color: #22AD5C;
}
.dokan-label-success[href]:hover,
.dokan-label-success[href]:focus {
  background-color: #1a8245;
}
.dokan-label-success[href]:hover,
.dokan-label-success[href]:focus {
  background-color: #1a8245;
}
.dokan-label-info {
  color: #fff;
  background-color: #0B76B7;
}
.dokan-label-info[href]:hover,
.dokan-label-info[href]:focus {
  background-color: #085787;
}
.dokan-label-info[href]:hover,
.dokan-label-info[href]:focus {
  background-color: #085787;
}
.dokan-label-warning {
  color: #fff;
  background-color: #FBBF24;
}
.dokan-label-warning[href]:hover,
.dokan-label-warning[href]:focus {
  background-color: #e8a804;
}
.dokan-label-warning[href]:hover,
.dokan-label-warning[href]:focus {
  background-color: #e8a804;
}
.dokan-label-danger {
  color: #fff;
  background-color: #F23030;
}
.dokan-label-danger[href]:hover,
.dokan-label-danger[href]:focus {
  background-color: #e10e0e;
}
.dokan-label-danger[href]:hover,
.dokan-label-danger[href]:focus {
  background-color: #e10e0e;
}
.dokan-success {
  color: #fff;
  background-color: #22AD5C;
}
.dokan-success[href]:hover,
.dokan-success[href]:focus {
  background-color: #1a8245;
}
.dokan-success[href]:hover,
.dokan-success[href]:focus {
  background-color: #1a8245;
}
.dokan-info {
  color: #fff;
  background-color: #0B76B7;
}
.dokan-info[href]:hover,
.dokan-info[href]:focus {
  background-color: #085787;
}
.dokan-info[href]:hover,
.dokan-info[href]:focus {
  background-color: #085787;
}
.dokan-warning {
  color: #fff;
  background-color: #FBBF24;
}
.dokan-warning[href]:hover,
.dokan-warning[href]:focus {
  background-color: #e8a804;
}
.dokan-warning[href]:hover,
.dokan-warning[href]:focus {
  background-color: #e8a804;
}
.dokan-danger {
  color: #fff;
  background-color: #F23030;
}
.dokan-danger[href]:hover,
.dokan-danger[href]:focus {
  background-color: #e10e0e;
}
.dokan-danger[href]:hover,
.dokan-danger[href]:focus {
  background-color: #e10e0e;
}
.dokan-panel {
  margin-bottom: 20px;
  background-color: #fff;
  border: 1px solid transparent;
  border-radius: 4px;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}
.dokan-panel-body {
  padding: 15px;
}
.dokan-panel-heading {
  padding: 10px 15px;
  border-bottom: 1px solid transparent;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.dokan-panel-heading > .dropdown .dropdown-toggle {
  color: inherit;
}
.dokan-panel-title {
  margin-top: 0;
  margin-bottom: 0;
  color: inherit;
}
.dokan-panel-title > a {
  color: inherit;
}
.dokan-panel-footer {
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
}
.dokan-panel-default {
  border-color: #ddd;
}
.dokan-panel-default > .dokan-panel-heading {
  color: #333;
  background-color: #f5f5f5;
  border-color: #ddd;
}
.dokan-panel-default > .dokan-panel-heading + .dokan-panel-collapse > .dokan-panel-body {
  border-top-color: #ddd;
}
.dokan-panel-default > .dokan-panel-heading .dokan-badge {
  color: #f5f5f5;
  background-color: #333;
}
.dokan-panel-default > .dokan-panel-footer + .dokan-panel-collapse > .dokan-panel-body {
  border-bottom-color: #ddd;
}
.dokan-clearfix:before,
.dokan-clearfix:after {
  display: table;
  content: ' ';
}
.dokan-clearfix:after {
  clear: both;
}
.dokan-right {
  float: left !important;
}
.dokan-left {
  float: right !important;
}
.dokan-hide {
  display: none;
}
.content-half-part {
  width: 50%;
  float: right;
}
.content-half-part:first-child {
  padding-left: 10px;
}
.content-half-part:last-child {
  padding-right: 10px;
}
p.help-block {
  color: #aaa;
  padding-top: 8px;
  line-height: 1.42;
}
ul.subsubsub {
  font-size: 12px;
  color: #EDEDED;
  margin: 0 0 10px 0;
  padding: 0;
}
ul.subsubsub li {
  padding: 0 5px;
  line-height: 13px;
  border-left: 1px solid #EDEDED;
}
ul.subsubsub li a {
  color: #6d6d6d;
  transition: 0.2s linear;
}
ul.subsubsub li a:hover {
  color: #000;
}
ul.subsubsub li:last-child {
  border-left: none;
}
ul.subsubsub li.active a {
  color: #000;
}
.pagination-wrap ul.pagination {
  display: inline-block;
  padding-right: 0;
  margin: 20px 0;
  border-radius: 4px;
}
.pagination-wrap ul.pagination > li {
  display: inline;
}
.pagination-wrap ul.pagination > li > span {
  position: relative;
  float: right;
  padding: 6px 12px;
  margin-right: -1px;
  line-height: 1.42857143;
  text-decoration: none;
  background-color: #ffffff;
  border: 1px solid #dddddd;
}
.pagination-wrap ul.pagination > li > span.current {
  background-color: #eee;
  color: #999;
}
.pagination-wrap ul.pagination > li > a {
  position: relative;
  float: right;
  padding: 6px 12px;
  margin-right: -1px;
  line-height: 1.42857143;
  text-decoration: none;
  background-color: #ffffff;
  border: 1px solid #dddddd;
}
.pagination-wrap ul.pagination > li > a:hover {
  background-color: #eee;
  color: #999;
}
.dokan-form-inline .dokan-form-group {
  float: right;
  margin-left: 5px;
}
table.dokan-table .toggle-row {
  position: absolute;
  left: 8px;
  top: 0;
  display: none;
  padding: 0;
  width: 40px;
  height: 40px;
  border: none;
  outline: 0;
  background: 100% 0;
  color: #444;
}
table.dokan-table .toggle-row::before {
  content: '\f0d7';
  font-family: "Font Awesome\ 5 Free";
  font-weight: 900;
  font: normal normal normal 14px/1 FontAwesome;
  display: inline-block;
  font-size: inherit;
  text-rendering: auto;
}
table.dokan-table td a {
  color: #6d6d6d;
}
table.dokan-table td a:hover {
  color: #000;
}
table.dokan-table .is-expanded .toggle-row::before {
  content: '\f0d8';
}
@media (max-width: 430px) {
  table.dokan-table thead {
    display: none;
  }
  table.dokan-table td.column-thumb,
  table.dokan-table td.column-primary ~ td:not(.check-column) {
    display: none;
  }
  table.dokan-table .is-expanded td:not(.hidden) {
    display: block !important;
    overflow: hidden;
  }
  table.dokan-table td.column-primary {
    padding-left: 50px;
  }
  table.dokan-table td.column-primary strong {
    display: block;
    margin-bottom: 0.5em;
  }
  table.dokan-table td:not(.check-column) {
    position: relative;
    width: auto !important;
    clear: both;
  }
  table.dokan-table .is-expanded td.column-thumb,
  table.dokan-table td.column-primary ~ :not(.check-column) {
    display: block;
    text-align: left;
    padding: 3px 35% 3px 8px;
  }
  table.dokan-table td:not(.column-primary)::before {
    content: attr(data-title);
    position: absolute;
    right: 10px;
    width: 32%;
    white-space: nowrap;
    text-align: right;
    display: block;
  }
  table.dokan-table .toggle-row {
    display: block;
  }
  table.dokan-table .row-actions {
    display: grid;
    grid-template-columns: auto auto auto;
  }
}
span.dokan-loading {
  background: url(../js/../images/wpspin_light.gif) no-repeat;
  float: left;
  height: 16px;
  margin: 5px 5px 0;
  width: 16px;
}
span.error {
  color: #bb0000;
  display: inline-block;
}
.dokan-seller-search {
  box-sizing: border-box !important;
  border: 2px solid #ccc !important;
  border-radius: 4px !important;
  background-color: white !important;
  background-image: url(../js/../images/searchicon.png) !important;
  background-position: right 8px top 8px !important;
  background-repeat: no-repeat !important;
  padding: 5px 36px 5px 0 !important;
  -webkit-transition: width 0.4s ease-in-out !important;
  transition: width 0.4s ease-in-out !important;
  background-size: 16px !important;
}
.dokan-overlay {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 999;
}
.dokan-ajax-loader {
  height: 1em;
  width: 1em;
  position: absolute;
  top: 50%;
  right: 50%;
  margin-right: -0.5em;
  margin-top: -0.5em;
  display: block;
  content: '';
  -webkit-animation: spin 1s ease-in-out infinite;
  -moz-animation: spin 1s ease-in-out infinite;
  animation: spin 1s ease-in-out infinite;
  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAxNy4xLjAsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+DQo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4Ig0KCSB2aWV3Qm94PSIwIDAgOTEuMyA5MS4xIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA5MS4zIDkxLjEiIHhtbDpzcGFjZT0icHJlc2VydmUiPg0KPGNpcmNsZSBjeD0iNDUuNyIgY3k9IjQ1LjciIHI9IjQ1LjciLz4NCjxjaXJjbGUgZmlsbD0iI0ZGRkZGRiIgY3g9IjQ1LjciIGN5PSIyNC40IiByPSIxMi41Ii8+DQo8L3N2Zz4NCg==) center center;
  background-size: cover;
  line-height: 1;
  text-align: center;
  font-size: 2em;
  color: rgba(0, 0, 0, 0.75);
}
.dokan-category-menu {
  padding: 0;
  border: 1px solid #ece7e7;
}
.dokan-category-menu h3.widget-title {
  margin: 0;
  padding: 10px 15px 10px 10px;
  background: #eee;
  border: 1px solid #eee;
  color: #444;
}
.dokan-category-menu ul li {
  border-bottom: none;
}
.dokan-category-menu .cat-drop-stack ul .children {
  display: none;
}
.dokan-category-menu .cat-drop-stack ul li:last-child a {
  border-bottom: none !important;
}
.dokan-category-menu .cat-drop-stack ul li:last-child.has-children a {
  border-bottom: 1px solid #eee !important;
}
.dokan-category-menu .cat-drop-stack > ul {
  padding: 0px;
  margin: 0px;
}
.dokan-category-menu .cat-drop-stack > ul li {
  padding: 0;
}
.dokan-category-menu .cat-drop-stack > ul li.parent-cat-wrap {
  background: #fff;
}
.dokan-category-menu .cat-drop-stack > ul li.parent-cat-wrap a {
  border-bottom: 1px solid #eee;
  margin: 0px 15px;
  text-decoration: none;
}
.dokan-category-menu .cat-drop-stack > ul li.parent-cat-wrap ul.level-0 {
  background: #fafafa;
}
.dokan-category-menu .cat-drop-stack > ul li.parent-cat-wrap ul.level-0 li.has-children {
  border-bottom: none;
}
.dokan-category-menu .cat-drop-stack > ul li a {
  padding: 9px 0px;
  display: block;
  color: #3c3c3c;
  position: relative;
  font-size: 13px;
  text-decoration: none;
}
.dokan-category-menu .cat-drop-stack > ul li a .caret-icon {
  position: absolute;
  left: -2px;
  display: inline-block;
  width: 20px;
  vertical-align: middle;
  text-align: center;
}
.dokan-announcement-wrapper .dokan-no-announcement .annoument-no-wrapper {
  height: 100%;
  margin: 70px auto;
  text-align: center;
}
.dokan-announcement-wrapper .dokan-no-announcement .annoument-no-wrapper .dokan-announcement-icon {
  font-size: 165px;
  color: #e3e3e3;
  -moz-animation: ring 8s 1s ease-in-out infinite !important;
  -moz-transform-origin: 50% 4px;
  -webkit-animation: ring 8s 1s ease-in-out infinite !important;
  -webkit-transform-origin: 50% 4px;
  animation: ring 8s 1s ease-in-out infinite !important;
  transform-origin: 50% 4px;
}
.dokan-announcement-wrapper .dokan-no-announcement .annoument-no-wrapper p {
  margin-top: 10px;
  color: #a6a6a6;
  font-size: 30px;
}
.dokan-announcement-wrapper .dokan-announcement-wrapper-item {
  padding: 22px;
  background: #f3f3f3;
  border: 1px solid #eee;
  margin-bottom: 25px;
  position: relative;
  width: 98%;
}
.dokan-announcement-wrapper .dokan-announcement-wrapper-item .dokan-announcement-heading {
  margin-bottom: 5px;
}
.dokan-announcement-wrapper .dokan-announcement-wrapper-item .dokan-announcement-heading h3 {
  margin: 5px 0px;
  padding: 0px 0px 8px;
  font-weight: bold;
  font-size: 20px;
  color: #494949;
}
.dokan-announcement-wrapper .dokan-announcement-wrapper-item .dokan-annnouncement-date {
  width: 95px;
  height: 95px;
  color: #fff;
  margin-left: 25px;
  vertical-align: middle;
  background-color: #818181;
  text-align: center;
  border-radius: 300px;
  -webkit-border-radius: 300px;
  -moz-border-radius: 300px;
}
.dokan-announcement-wrapper .dokan-announcement-wrapper-item .dokan-annnouncement-date .announcement-day {
  font-size: 20px;
  font-weight: bold;
  margin-top: 4px;
}
.dokan-announcement-wrapper .dokan-announcement-wrapper-item .dokan-annnouncement-date .announcement-year {
  font-weight: bold;
}
.dokan-announcement-wrapper .dokan-announcement-wrapper-item .dokan-announcement-content-wrap {
  width: 80%;
  height: 100%;
}
.dokan-announcement-wrapper .dokan-announcement-wrapper-item .dokan-announcement-content-wrap .dokan-announcement-content {
  color: #656565;
  font-size: 13px;
}
.dokan-announcement-wrapper .dokan-announcement-wrapper-item .announcement-action {
  position: absolute;
  top: -12px;
  left: -8px;
  font-size: 12px;
  width: 25px;
  height: 25px;
  text-align: center;
  background-color: #818181;
  border-radius: 20px;
  line-height: 23px;
}
.dokan-announcement-wrapper .dokan-announcement-wrapper-item .announcement-action a {
  color: #fff;
  font-size: 20px;
}
@media (max-width: 992px) {
  .dokan-announcement-wrapper-item {
    padding: 15px;
    text-align: center;
  }
  .dokan-announcement-wrapper-item .dokan-annnouncement-date {
    float: none !important;
    margin: 0 auto 15px !important;
  }
  .dokan-announcement-wrapper-item .dokan-annnouncement-date .announcement-day {
    margin-top: 0px !important;
    padding-top: 16px;
  }
  .dokan-announcement-wrapper-item .dokan-announcement-content-wrap {
    float: none !important;
    width: 100% !important;
  }
}
article.dokan-notice-single-notice-area span.dokan-single-announcement-date {
  display: block;
  margin-bottom: 10px;
}
.dokan-announcement-uread {
  border: 1px solid var(--dokan-button-background-color, #7047EB) !important;
}
.dokan-announcement-uread .dokan-annnouncement-date {
  background-color: var(--dokan-button-background-color, #7047EB) !important;
}
.dokan-announcement-bg-uread {
  background-color: var(--dokan-button-background-color, #7047EB);
}
.dokan-dashboard .dokan-dash-sidebar {
  width: 17%;
  _float: right;
  flex: 1 auto;
  background-color: var(--dokan-sidebar-background-color, #322067);
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu {
  background: var(--dokan-sidebar-background-color, #322067);
  list-style: none;
  margin: 0 0 20px 0;
  padding: 0;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li {
  margin: 0;
  position: relative;
  cursor: pointer;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li a {
  padding: 10px 18px 10px 0;
  display: block;
  color: #fff;
  font-size: 14px;
  font-weight: normal;
  text-decoration: none;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li a i {
  font-size: 17px;
  padding-left: 15px;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li a i.menu-dropdown::before {
  padding-right: 15px;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li a svg {
  margin-left: 15px;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li:hover {
  background: #7047EB;
  color: #fff;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li:hover ul.navigation-submenu {
  position: relative;
  top: auto;
  right: auto;
  left: auto;
  bottom: auto;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li:hover:not(.active).has-submenu:after {
  left: 0;
  top: 1.3rem;
  border: transparent solid;
  content: ' ';
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-color: transparent transparent transparent var(--dokan-sidebar-background-color, #322067);
  border-right-color: var(--dokan-sidebar-background-color, #322067);
  border-width: 16px 0 16px 16px;
  margin-top: -16px;
  z-index: 990;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li:hover:not(.active) ul.navigation-submenu {
  position: absolute;
  bottom: 0;
  right: 100%;
  min-width: 165px;
  display: block;
  background: var(--dokan-sidebar-background-color, #322067);
  border-bottom: none;
  padding: 1.5px;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li:hover:not(.active) ul.navigation-submenu li {
  display: block;
  padding-right: 0.5em;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li:hover a i.menu-dropdown {
  display: none;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li.dokan-common-links:hover {
  background: none !important;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li.dokan-common-links a {
  display: inline-block !important;
  width: 33.333333%;
  padding: 6% 13%;
  float: right;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li.dokan-common-links a:hover {
  background: #7047EB;
  color: #fff;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li.dokan-common-links a:last-child {
  border-left: none;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li ul.navigation-submenu {
  list-style: none;
  position: absolute;
  top: -1000em;
  right: 160px;
  overflow: visible;
  word-wrap: break-word;
  z-index: 9999;
  box-shadow: 0 3px 5px rgb(0 0 0%);
  margin-right: 0;
  background: var(--dokan-sidebar-background-color, #322067);
  padding: 3px 0;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li ul.navigation-submenu li {
  padding-right: 1.3em;
  line-height: 1;
  background: var(--dokan-sidebar-background-color, #322067);
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li ul.navigation-submenu li a {
  font-size: 13px;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li ul.navigation-submenu li:hover:before,
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li ul.navigation-submenu li.current:before {
  right: 0.75%;
  top: 4%;
  border: transparent solid;
  content: ' ';
  height: 90%;
  position: absolute;
  pointer-events: none;
  border-color: #fff;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li ul.navigation-submenu li:hover a,
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li ul.navigation-submenu li.current a {
  font-weight: 800 !important;
  color: #fff;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li.active {
  position: relative;
  background: #7047EB;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li.active:after {
  right: 93%;
  top: 1.3rem;
  border: transparent solid;
  content: ' ';
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-color: transparent transparent transparent #fff;
  border-right-color: #fff;
  border-width: 16px 0 16px 16px;
  margin-top: -16px;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li.active ul.navigation-submenu {
  position: relative;
  z-index: 3;
  top: auto;
  right: auto;
  left: auto;
  bottom: auto;
  border: 0 none;
  border-bottom: 0.5px solid #7047EB;
  margin-top: 0;
  box-shadow: none;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li.active ul.navigation-submenu li:not(.current) a {
  font-weight: normal;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li.active ul.navigation-submenu li a:focus {
  outline: none;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li.active a {
  padding-left: 16px;
  font-weight: 800;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li.active a i.menu-dropdown {
  display: none;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li ul.sub-menu {
  background: #fff;
  margin: 0;
  list-style: none;
  padding: 0;
  position: absolute;
  left: -100%;
  top: -4px;
  z-index: 99;
  width: 165px;
  display: none;
  border-top: 1px solid #ececec;
  border-left: 1px solid #ececec;
  border-bottom: 1px solid #ececec;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li ul.sub-menu a {
  padding: 2px 10px 2px 0;
  font-size: 13px;
  color: #333;
  font-weight: normal;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li ul.sub-menu a:hover {
  background: #9475f0;
  color: #fff;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li ul.sub-menu li {
  border-bottom: 1px solid #ccc;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li ul.sub-menu li:last-child {
  border-bottom: none;
}
.dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li.settings i.pull-right {
  padding-top: 5px;
}
.dokan-dashboard .dokan-dash-sidebar #dokan-navigation #toggle-mobile-menu {
  display: none;
}
.dokan-dashboard .dokan-dash-sidebar #dokan-navigation #mobile-menu-icon {
  display: none;
}
.dokan-dashboard .dokan-dash-sidebar #dokan-navigation > #mobile-menu-icon {
  font-size: 17px;
  color: #fff;
  position: absolute;
  top: 0;
  left: 0;
  width: 33px;
  z-index: 1;
  cursor: pointer;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border: 1px solid #f0f0f0;
  border-radius: 3px;
  padding: 3px 8px;
}
.dokan-dashboard .dokan-dash-sidebar #dokan-navigation > input:checked + ul.dokan-dashboard-menu {
  display: block !important;
  background: #000;
  color: #fff;
  animation: showNav 350ms ease-in-out both;
}
@keyframes showNav {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@media only screen and (max-width: 450px) {
  .dokan-dashboard .dokan-dash-sidebar #dokan-navigation {
    height: 33px;
    position: relative;
  }
  .dokan-dashboard .dokan-dash-sidebar #dokan-navigation #mobile-menu-icon {
    display: block !important;
  }
  .dokan-dashboard .dokan-dash-sidebar #dokan-navigation #toggle-mobile-menu {
    display: none !important;
  }
  .dokan-dashboard .dokan-dash-sidebar #dokan-navigation ul.dokan-dashboard-menu {
    display: none;
    height: auto !important;
    padding-top: 48px !important;
  }
}
html,
body {
  -webkit-backface-visibility: hidden;
}
div.media-sidebar a.edit-attachment {
  display: none;
}
.daterangepicker .calendar-table th,
.daterangepicker .calendar-table td {
  padding: 5px 10px;
}
.dokan-dashboard .dokan-dashboard-wrap {
  display: flex;
  display: -webkit-flex;
  display: -ms-flexbox;
  flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  width: 100%;
}
.dokan-dashboard .dokan-dashboard-wrap * {
  box-sizing: border-box;
}
.dokan-dashboard .dokan-dashboard-wrap a:focus {
  outline-color: var(--dokan-button-background-color, #7047EB);
}
.dokan-dashboard .dokan-dashboard-wrap input[type=checkbox]:not(.dokan-layout input[type=checkbox]) {
  -webkit-appearance: revert;
  -moz-appearance: revert;
  appearance: revert;
}
.dokan-dashboard header.dokan-dashboard-header {
  margin: 0 0 15px 0;
}
.dokan-dashboard header.dokan-dashboard-header h1 {
  margin: 0 0 10px 0;
  border-bottom: 1px solid #EDEDED;
  padding: 0 0 10px 0;
  line-height: 1.25;
}
.dokan-dashboard header.dokan-dashboard-header .dokan-add-product-link .dokan-btn {
  margin-left: 10px;
}
.dokan-dashboard .dokan-dashboard-content {
  padding: 4px 25px 4px 4px;
  overflow: hidden;
  margin-top: 0px;
  width: 83%;
  flex: 5 auto;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area:before,
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area:after {
  content: " ";
  display: table;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area:after {
  clear: both;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dokan-dash-left {
  padding-left: 15px;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget {
  background: #fff;
  border: 1px solid #EBEBEB;
  padding: 0px 10px;
  position: relative;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  margin-bottom: 15px;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget a {
  font-size: 13px;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget .widget-title {
  font-weight: bold;
  font-size: 15px;
  border-bottom: 1px solid #EBEBEB;
  padding: 6px 0;
  margin-bottom: 6px;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget .widget-title i {
  color: #ccc;
  padding-left: 5px;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget .widget-title .pull-right {
  float: left;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget .widget-title .pull-right a {
  color: #6d6d6d;
  transition: 0.2s linear;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget .widget-title .pull-right a:hover {
  color: #000;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget.big-counter {
  text-align: center;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget.big-counter ul {
  margin: 0px;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget.big-counter li {
  width: 100%;
  display: block;
  margin: 0 auto;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget.big-counter .title {
  font-size: 15px;
  padding-top: 10px;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget.big-counter .count {
  font-size: 18px;
  border-bottom: 1px solid #EBEBEB;
  font-weight: 600;
  padding-bottom: 10px;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget.orders {
  overflow: hidden;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget.orders .content-half-part {
  width: 50%;
  padding: 0;
  float: right;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget.orders #order-stats {
  padding: 20px;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget.products .pull-right a {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
  margin-top: -6px;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget.sells-graph {
  padding-bottom: 7px;
  width: 100%;
  position: relative;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget .chart-placeholder.main {
  height: 347px;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget .list-count a {
  color: #6d6d6d;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .dashboard-widget .list-count .count {
  float: left;
}
.dokan-dashboard .dokan-dashboard-content article.dashboard-content-area .chart-tooltip {
  position: absolute;
  display: none;
  line-height: 1;
  background: #333;
  color: #fff;
  padding: 3px 5px;
  font-size: 11px;
  border-radius: 3px;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-map-wrap {
  border: 1px solid #EDEDED;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-map-wrap .dokan-map-search-bar {
  position: relative;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-map-wrap .dokan-map-find-btn {
  display: none;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-map-wrap .dokan-map-search {
  border: none;
  width: 100%;
  padding: 5px 10px;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-map-wrap .dokan-google-map {
  width: 100%;
  height: 300px;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary #vendor-dashboard-payment-settings-error {
  padding: 25px;
  display: none;
  background-color: palevioletred;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary div.payment-methods-listing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #EEEEEE;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary div.payment-methods-listing-header > h2 {
  margin: 5px 0;
  flex-grow: 1;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary div.payment-methods-listing-header > div {
  flex-grow: 2;
  text-align: left;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary div.payment-methods-listing-header > div #vendor-dashboard-payment-settings-toggle-dropdown {
  display: inline-block;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary div.payment-methods-listing-header > div #vendor-dashboard-payment-settings-toggle-dropdown #toggle-vendor-payment-method-drop-down {
  color: #333333;
  cursor: pointer;
  padding: 10px 10px 10px 35px;
  white-space: nowrap;
  border-radius: 3px;
  background: #DDDDDD;
  position: relative;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary div.payment-methods-listing-header > div #vendor-dashboard-payment-settings-toggle-dropdown #toggle-vendor-payment-method-drop-down::after {
  content: '\25BC';
  display: inline-block;
  transition: transform 300ms ease-in-out;
  position: absolute;
  left: 5px;
  top: 20%;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary div.payment-methods-listing-header > div #vendor-dashboard-payment-settings-toggle-dropdown #vendor-payment-method-drop-down-wrapper {
  position: relative;
  top: 5px;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary div.payment-methods-listing-header > div #vendor-dashboard-payment-settings-toggle-dropdown #vendor-payment-method-drop-down {
  display: none;
  max-height: 200px;
  border: 1px black solid;
  position: absolute;
  top: 0;
  left: 0;
  background-color: white;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary div.payment-methods-listing-header > div #vendor-dashboard-payment-settings-toggle-dropdown #vendor-payment-method-drop-down ul {
  list-style: none;
  margin: 0;
  max-height: 199px;
  overflow-y: auto;
  overflow-x: hidden;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary div.payment-methods-listing-header > div #vendor-dashboard-payment-settings-toggle-dropdown #vendor-payment-method-drop-down ul li:not(:last-child) {
  border-bottom: 1px #dddddd solid;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary div.payment-methods-listing-header > div #vendor-dashboard-payment-settings-toggle-dropdown #vendor-payment-method-drop-down ul li div {
  white-space: nowrap;
  display: flex;
  align-items: center;
  padding: 10px 10px 10px 20px;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary div.payment-methods-listing-header > div #vendor-dashboard-payment-settings-toggle-dropdown #vendor-payment-method-drop-down ul li div img {
  width: calc(12px + 1.5vw);
  border: 1px solid lightgray;
  border-radius: 50%;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary div.payment-methods-listing-header > div #vendor-dashboard-payment-settings-toggle-dropdown #vendor-payment-method-drop-down ul li div span {
  padding-left: 30px;
  margin-right: 10px;
  color: #333333;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary div.payment-methods-listing-header > div #vendor-dashboard-payment-settings-toggle-dropdown #vendor-payment-method-drop-down ul li:hover {
  background-color: #EFEFEF;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary div.payment-methods-listing-header > div #vendor-dashboard-payment-settings-toggle-dropdown #vendor-payment-method-drop-down .no-content {
  padding: 20px;
  width: 300px;
  text-align: center;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary div.payment-methods-listing-header > div #vendor-dashboard-payment-settings-toggle-dropdown:hover #toggle-vendor-payment-method-drop-down {
  background-color: #CCCCCC;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary div.payment-methods-listing-header > div #vendor-dashboard-payment-settings-toggle-dropdown:hover #toggle-vendor-payment-method-drop-down::after {
  transform: rotate(-180deg);
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary div.payment-methods-listing-header > div #vendor-dashboard-payment-settings-toggle-dropdown:hover #vendor-payment-method-drop-down {
  display: block;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary > ul {
  list-style: none;
  min-height: 200px;
  margin-right: 0;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary > ul li > div {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: #CCCCCC solid 1px;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary > ul li > div > div {
  display: flex;
  align-items: center;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary > ul li > div > div img {
  width: calc(12px + 3vw);
  border: 1px solid grey;
  border-radius: 50%;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary > ul li > div > div span {
  margin-right: 10px;
  color: #333333;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary > ul li > div > div button {
  margin-bottom: 3px !important;
  min-height: 30px !important;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary > ul li > div > div a button {
  margin-right: 3px !important;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area .dokan-payment-settings-summary > .no-content {
  min-height: 200px;
  padding: 20px;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area #payment-form .payment-field-bank > div.dokan-form-group > div {
  background-color: white;
  box-shadow: 0 0 5px #bbbbbb;
  margin-left: 10px;
  padding: 20px;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area #payment-form .payment-field-bank > div.dokan-form-group > div div.dokan-form-group > div {
  text-align: right;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area #payment-form .payment-field-bank > div.dokan-form-group > div div.dokan-form-group > div label {
  color: black;
  font-weight: bold;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area #payment-form .payment-field-bank > div.dokan-form-group > div div.dokan-form-group > div select {
  padding: 10px 15px;
  border-radius: 5px;
  min-height: 50px;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area #payment-form .payment-field-bank > div.dokan-form-group > div div.data-warning {
  display: flex;
  box-shadow: 0 0 5px #AAAAAA;
  padding: 10px;
  margin-bottom: 10px;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area #payment-form .payment-field-bank > div.dokan-form-group > div div.data-warning div.left-icon-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 5px;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area #payment-form .payment-field-bank > div.dokan-form-group > div div.data-warning div.left-icon-container i {
  color: orange;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area #payment-form .payment-field-bank > div.dokan-form-group > div div.data-warning div.vr-separator {
  margin: 0 10px;
  border-right: 1px #dddddd solid;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area #payment-form .payment-field-bank > div.dokan-form-group > div .bottom-note {
  text-align: right;
  margin-bottom: 2em;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area #payment-form .payment-field-bank > div.dokan-form-group > div .bottom-actions {
  background-color: #EEEEEE;
  text-align: right;
  border-top: 1px #cccccc solid;
  padding: 20px;
  margin: -20px;
  position: relative;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area #payment-form .payment-field-bank > div.dokan-form-group > div .bottom-actions button.dokan-btn-danger {
  position: absolute;
  left: 20px;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area #payment-form .payment-field-bank > div.dokan-form-group > div .bottom-actions a {
  margin-right: 10px;
  text-decoration: none;
  color: #2B78E4;
}
.dokan-dashboard .dokan-dashboard-content article.dokan-settings-area #payment-form > div.dokan-form-group > div.ajax_prev.dokan-w4 {
  margin-right: 24%;
}
.dokan-dashboard .dokan-dashboard-content .edit-account fieldset {
  margin-top: 30px;
}
.dokan-dashboard .dokan-dashboard-content .edit-account fieldset legend {
  font-weight: bold;
}
.dokan-dashboard .dokan-dashboard-content article {
  border-bottom: none;
}
.dokan-dashboard .dokan-dashboard-content ul li {
  list-style: none;
  margin: 0px;
  padding: 0px;
}
.dokan-dashboard .dokan-dashboard-content a {
  text-decoration: none;
}
.dokan-dashboard .dokan-dashboard-content .dokan-page-help {
  display: block;
  font-style: italic;
  color: #888;
  margin-bottom: 30px;
}
.dokan-dashboard .dokan-dashboard-content .dokan-page-help p {
  margin-bottom: 10px;
}
.dokan-dashboard div.chart-container > div.chart-placeholder > div.legend table {
  border-spacing: 0.5em;
  width: auto;
  margin: 0;
  border-collapse: separate;
}
.dokan-dashboard div.chart-container > div.chart-placeholder > div.legend table td {
  padding: 0;
  text-align: right;
  vertical-align: middle;
  background: none;
}
.dokan-dashboard div.chart-container > div.chart-legend-container > table td {
  padding: 0;
  padding-right: 5px;
  padding-left: 5px;
}
.dokan-column-name-with-avatar {
  position: relative;
  padding-right: 46px !important;
}
.dokan-column-name-with-avatar img {
  position: absolute;
  top: 3px;
  right: 8px;
  border-radius: 50%;
  border: 1px solid #fff;
  box-shadow: 0 1px 0 0 #e0e0e0;
}
.dokan-orders-content .dokan-orders-area .dokan-order-left-content {
  margin-left: 3%;
}
.dokan-orders-content .dokan-orders-area .dokan-order-left-content .dokan-order-billing-address {
  min-width: 49%;
  margin-left: 2%;
}
.dokan-orders-content .dokan-orders-area .dokan-order-left-content .dokan-order-shipping-address {
  min-width: 49%;
}
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach {
  padding: 10px 7px;
}
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left {
  width: 66%!important;
}
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group button,
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group a,
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group input,
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group select,
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group .select2-container {
  height: 35px !important;
}
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group button .select2-selection--single,
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group a .select2-selection--single,
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group input .select2-selection--single,
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group select .select2-selection--single,
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group .select2-container .select2-selection--single {
  height: 35px !important;
}
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group button {
  margin-left: 5px;
  border: none !important;
  padding: 3px 10px !important;
}
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group input,
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group select,
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group .select2-container {
  width: calc(33% - 54px) !important;
  margin-left: 5px;
}
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group a {
  display: flex;
  align-items: center;
}
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group a .fa {
  margin-left: 3px;
}
@media screen and (max-width: 576px) {
  .dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group select,
  .dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group .select2-container,
  .dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group input {
    width: 100% !important;
    margin-left: 0;
  }
  .dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group select:not(:last-child),
  .dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group .select2-container:not(:last-child),
  .dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left .dokan-form-group input:not(:last-child) {
    margin-bottom: 5px;
  }
}
.dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-right {
  width: 33%!important;
}
@media screen and (max-width: 768px) {
  .dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-left {
    width: 100%!important;
  }
  .dokan-orders-content .dokan-orders-area .dokan-order-filter-serach .dokan-right {
    width: 100%!important;
  }
}
.dokan-orders-content .dokan-orders-area td.dokan-order-action {
  gap: 5px;
  width: 100%;
  display: grid;
  grid-gap: 5px;
  flex-direction: column;
  grid-template-columns: repeat(3, 1fr);
}
.dokan-orders-content .dokan-orders-area td.dokan-order-action .wpo_wcpdf {
  border: 1px solid transparent;
  padding: 5px 10px;
  border-color: #ccc;
  border-radius: 3px;
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center;
  height: 40px;
  width: 40px;
}
.dokan-orders-content .dokan-orders-area td.dokan-order-action .wpo_wcpdf img {
  width: 22px !important;
}
.dokan-orders-content .dokan-orders-area td img {
  padding: 2px;
  margin: 0;
  border: 1px solid #dfdfdf;
  vertical-align: middle;
  width: 46px;
  height: auto;
}
.dokan-orders-content .dokan-orders-area td a {
  color: #6d6d6d;
  transition: 0.2s linear;
}
.dokan-orders-content .dokan-orders-area td a:hover {
  color: #000;
}
.dokan-orders-content .dokan-orders-area table.table.order-items {
  margin-bottom: 0;
}
.dokan-orders-content .dokan-orders-area .general-details ul.order-status {
  border-bottom: 1px solid #EDEDED;
  margin-bottom: 3px;
  padding-bottom: 3px;
  margin-right: 0px;
  padding-right: 0px;
}
.dokan-orders-content .dokan-orders-area .general-details ul.customer-details {
  margin-right: 0px;
  padding-right: 0px;
}
.dokan-orders-content .dokan-orders-area .general-details span {
  font-weight: bold;
}
.dokan-orders-content .dokan-orders-area .alert-success.customer-note {
  background-color: #dff0d8;
  background: #dff0d8;
  margin-bottom: 5px;
}
.dokan-orders-content .dokan-orders-area .alert-success.customer-note strong {
  font-size: 12px;
}
.dokan-orders-content .dokan-orders-area .order_note_type {
  padding-right: 0;
}
.dokan-orders-content .dokan-orders-area #dokan-order-status-form {
  margin: 10px 0;
}
.dokan-orders-content .dokan-orders-area #dokan-order-status-form select.form-control {
  display: inline-block;
  width: 100%;
  margin-left: 10px;
  font-size: 13px;
}
.dokan-orders-content .dokan-orders-area ul.order-statuses-filter {
  font-size: 12px;
  color: #EDEDED;
}
.dokan-orders-content .dokan-orders-area ul.order-statuses-filter li {
  display: inline-block;
  line-height: 13px;
  padding: 0 5px;
  border-left: 1px solid #EDEDED;
}
.dokan-orders-content .dokan-orders-area ul.order-statuses-filter li a {
  color: #6d6d6d;
  transition: 0.2s linear;
}
.dokan-orders-content .dokan-orders-area ul.order-statuses-filter li:last-child {
  border-left: none;
}
.dokan-orders-content .dokan-orders-area ul.order-statuses-filter li.active a {
  color: #000;
}
.dokan-orders-content .dokan-orders-area ul.order_notes {
  font-size: 13px;
}
.dokan-orders-content .dokan-orders-area ul.order_notes p.meta {
  font-size: 11px;
}
.dokan-orders-content .dokan-orders-area ul.order_notes .note_content {
  position: relative;
  background: #efefef;
  padding: 3px 10px;
  margin-bottom: 10px;
}
.dokan-orders-content .dokan-orders-area ul.order_notes .note_content p {
  margin: 0;
  padding: 0;
  word-wrap: break-word;
}
.dokan-orders-content .dokan-orders-area ul.order_notes .note_content:after {
  content: "";
  display: block;
  position: absolute;
  bottom: -15px;
  right: 30px;
  width: 0;
  height: 0;
  border-width: 15px 0 0 15px;
  border-style: solid;
  border-color: #efefef transparent;
}
.dokan-orders-content .dokan-orders-area ul.order_notes li.customer-note .note_content {
  background: #d7cad2;
}
.dokan-orders-content .dokan-orders-area ul.order_notes li.customer-note .note_content:after {
  border-color: #d7cad2 transparent;
}
.dokan-orders-content .dokan-orders-area tfoot td.value {
  border-right: 1px solid #EDEDED;
  text-align: left;
}
.dokan-orders-content .dokan-orders-area .order_download_permissions label {
  font-weight: normal;
}
.dokan-orders-content .dokan-orders-area .order_download_permissions button.revoke_access {
  padding: 3px 10px;
  margin-top: -2px;
}
.dokan-orders-content .dokan-orders-area .order_download_permissions .toolbar {
  margin-top: 15px;
}
.dokan-orders-content .dokan-orders-area .chosen-container-multi .chosen-choices li.search-field input[type="text"] {
  min-height: 27px;
}
.vendor-dashboard-orders-page .select2-search__field {
  min-width: auto !important;
}
.dokan-product-listing .dokan-product-listing-area {
  padding: 0px 15px;
}
.dokan-product-listing .dokan-product-listing-area .row-actions {
  visibility: hidden;
  font-size: 12px;
  color: #ccc;
}
.dokan-product-listing .dokan-product-listing-area tr:hover .row-actions {
  visibility: visible;
}
.dokan-product-listing .dokan-product-listing-area table td img {
  width: auto;
  height: auto;
  max-width: 48px;
  max-height: 48px;
}
.dokan-product-listing .dokan-product-listing-area .product-listing-top {
  border-bottom: 1px solid #EDEDED;
  line-height: 50px;
  margin-bottom: 15px;
}
.dokan-product-listing .dokan-product-listing-area .product-listing-top ul.dokan-listing-filter {
  width: 60%;
}
.dokan-product-listing .dokan-product-listing-area .product-listing-top ul.dokan-listing-filter li {
  display: inline-block;
  padding: 0 5px;
}
.dokan-product-listing .dokan-product-listing-area .product-listing-top span.dokan-add-product-link {
  width: 36%;
  float: left;
  text-align: left;
}
.dokan-product-listing .dokan-product-listing-area form.dokan-product-search-form button[name='product_listing_search'] {
  float: left;
}
.dokan-product-listing .dokan-product-listing-area form.dokan-product-search-form .dokan-form-group {
  float: left;
}
.dokan-product-listing .dokan-product-listing-area table.product-listing-table span.product-type:before {
  font-family: "Font Awesome\ 5 Free";
  font-weight: 900;
  content: '\f133';
  display: block;
  text-align: center;
}
.dokan-product-listing .dokan-product-listing-area table.product-listing-table span.product-type.downloadable:before {
  font-family: "Font Awesome\ 5 Free";
  font-weight: 900;
  content: '\f019';
  display: block;
  text-align: center;
}
.dokan-product-listing .dokan-product-listing-area table.product-listing-table span.product-type.variable:before {
  font-family: "Font Awesome\ 5 Free";
  font-weight: 900;
  content: '\f02d';
  display: block;
  text-align: center;
}
.dokan-product-listing .dokan-product-listing-area table.product-listing-table span.product-type.simple:before {
  font-family: "Font Awesome\ 5 Free";
  font-weight: 900;
  content: '\f0c9';
  display: block;
  text-align: center;
}
.dokan-product-listing .dokan-product-listing-area table.product-listing-table span.product-type.virtual:before {
  font-family: "Font Awesome\ 5 Free";
  font-weight: 900;
  content: '\f0c2';
  display: block;
  text-align: center;
}
.dokan-product-listing .dokan-product-listing-area table.product-listing-table span.earning-info:before {
  font-family: "Font Awesome\ 5 Free";
  font-weight: 900;
  content: '\f05a';
  display: inline;
  margin-right: 5px;
}
.dokan-product-listing .dokan-product-listing-area table.product-listing-table mark.instock {
  color: #7ad03a;
  background: transparent;
  font-weight: bold;
}
.dokan-product-listing .dokan-product-listing-area table.product-listing-table td {
  vertical-align: top;
}
.dokan-product-listing .dokan-product-listing-area table.product-listing-table td.column-primary a,
.dokan-product-listing .dokan-product-listing-area table.product-listing-table td .row-actions a {
  color: #6d6d6d;
}
.dokan-product-listing .dokan-product-listing-area table.product-listing-table td.column-primary a:hover,
.dokan-product-listing .dokan-product-listing-area table.product-listing-table td .row-actions a:hover {
  color: #000;
}
.dokan-product-listing .dokan-product-listing-area table.product-listing-table td.column-primary .delete a:hover,
.dokan-product-listing .dokan-product-listing-area table.product-listing-table td .row-actions .delete a:hover {
  color: red;
}
.dokan-product-listing .dokan-product-listing-area table.product-listing-table p {
  margin-bottom: 0px;
  padding-bottom: 0px;
}
.dokan-product-listing .dokan-product-listing-area table.product-listing-table td.post-status label.draft {
  background: #ccc;
}
.dokan-product-listing .dokan-product-listing-area table.product-listing-table td.post-status label.publish {
  background: #65c265;
}
.dokan-product-listing .dokan-product-listing-area table.product-listing-table td.post-status label.pending {
  background: var(--dokan-button-background-color, #7047EB);
}
.dokan-product-listing .dokan-product-listing-area table.product-listing-table td.post-date {
  font-size: 13px;
}
.dokan-product-listing .dokan-product-listing-area del .amount {
  color: #dd5a43;
}
.dokan-product-listing .dokan-product-listing-area ins {
  text-decoration: none;
}
.dokan-product-listing .dokan-product-listing-area ins .amount {
  color: #69aa46;
  font-weight: bold;
}
.dokan-new-product-area .dokan-product-meta {
  width: 73.43%;
}
.dokan-new-product-area .featured-image {
  width: 25%;
}
.dokan-new-product-area .content-half-part.sale-price {
  display: inline-block;
}
.dokan-new-product-area .content-half-part.sale-price label.form-label {
  display: block;
}
.dokan-new-product-area .content-half-part.sale-price label.form-label a.sale_schedule,
.dokan-new-product-area .content-half-part.sale-price label.form-label a.cancel_sale_schedule {
  float: left;
}
.dokan-product-edit-area header.dokan-pro-edit-breadcrumb {
  display: block;
  border-bottom: 1px solid #EDEDED;
  padding: 0 0 10px 0;
  margin: 0 0 15px 0;
}
.dokan-product-edit-area header.dokan-pro-edit-breadcrumb h1 {
  font-size: 18px;
  line-height: 1.42;
}
.dokan-product-edit-area header.dokan-pro-edit-breadcrumb h1 .dokan-breadcrumb,
.dokan-product-edit-area header.dokan-pro-edit-breadcrumb h1 .dokan-breadcrumb a {
  color: #aaa;
}
.dokan-product-edit-area header.dokan-pro-edit-breadcrumb h1 .dokan-label {
  font-size: 11px;
  font-weight: normal;
}
.dokan-product-edit-area header.dokan-pro-edit-breadcrumb h1 a.view-product {
  background-color: #fafafa;
  border-color: #ebebeb;
}
.dokan-product-edit-area .dokan-product-meta {
  width: 65%;
}
.dokan-product-edit-area .featured-image {
  width: 35%;
}
.dokan-edit-row {
  background: #fff;
  border: 1px solid #ebebeb;
  margin-top: 15px;
}
.dokan-edit-row .dokan-section-heading {
  padding: 10px 15px;
  border-bottom: 1px solid #ebebeb;
  overflow: hidden;
  cursor: pointer;
}
.dokan-edit-row .dokan-section-heading i.fa {
  font-size: 15px;
}
.dokan-edit-row .dokan-section-heading i.fa.fa-flip-vertical {
  margin-top: 9px;
}
.dokan-edit-row .dokan-section-heading .dokan-section-toggle {
  float: left;
  color: #888;
  font-size: 15px;
}
.dokan-edit-row .dokan-section-heading h2 {
  margin: 0px;
  padding: 0px;
  float: right;
  font-size: 16px;
  font-weight: bold;
  line-height: 150%;
}
.dokan-edit-row .dokan-section-heading p {
  float: right;
  vertical-align: bottom;
  margin-bottom: 0px;
  margin-top: 2px;
  margin-right: 8px;
  color: #888;
  font-style: italic;
  font-size: 12px;
}
.product-edit-new-container .dokan-edit-row.dokan-other-options {
  margin-bottom: 20px;
}
.product-edit-new-container .dokan-edit-row .dokan-section-content {
  padding: 15px;
}
.product-edit-new-container .dokan-product-edit-form .dokan-new-product-featured-img {
  max-height: 300px;
}
.product-edit-new-container label {
  font-weight: normal;
  cursor: pointer;
}
.product-edit-new-container label input[type='checkbox'] {
  margin-left: 5px;
}
.product-edit-new-container label.form-label {
  font-weight: bold;
  margin-bottom: 5px;
  font-size: 13px;
  display: block;
}
.product-edit-new-container label.form-label span {
  font-weight: normal;
  color: #888;
}
.product-edit-new-container header.dokan-pro-edit-breadcrumb {
  display: block;
  border-bottom: 1px solid #EDEDED;
  padding: 0 0 10px 0;
  margin: 0 0 15px 0;
}
.product-edit-new-container header.dokan-pro-edit-breadcrumb h1 {
  font-size: 18px;
  line-height: 1.42;
}
.product-edit-new-container header.dokan-pro-edit-breadcrumb h1 .dokan-breadcrumb,
.product-edit-new-container header.dokan-pro-edit-breadcrumb h1 .dokan-breadcrumb a {
  color: #aaa;
}
.product-edit-new-container header.dokan-pro-edit-breadcrumb h1 .dokan-label {
  font-size: 11px;
  font-weight: normal;
}
.product-edit-new-container header.dokan-pro-edit-breadcrumb h1 a.view-product {
  background-color: #fafafa;
  border-color: #ebebeb;
}
.product-edit-new-container .dokan-price-container .sale_schedule,
.product-edit-new-container .dokan-price-container .cancel_sale_schedule {
  font-weight: normal;
  float: left;
}
.product-edit-new-container .dokan-form-top-area:before,
.product-edit-new-container .dokan-form-top-area:after {
  content: " ";
  display: table;
}
.product-edit-new-container .dokan-form-top-area:after {
  clear: both;
}
.product-edit-new-container .dokan-form-top-area .dokan-product-meta {
  width: 65%;
}
.product-edit-new-container .dokan-form-top-area .featured-image {
  width: 35%;
}
@media (max-width: 430px) {
  .product-edit-new-container .dokan-form-top-area .dokan-product-meta,
  .product-edit-new-container .dokan-form-top-area .featured-image {
    width: 100%;
  }
}
@media (max-width: 430px) {
  .product-edit-new-container .dokan-form-top-area {
    display: flex;
    flex-direction: column-reverse;
  }
}
.product-edit-new-container .dokan-product-short-description {
  margin-bottom: 20px;
  margin-top: 10px;
}
.product-edit-new-container .content-half-part.featured-image {
  padding-right: 25px;
}
.product-edit-new-container .content-half-part.sale-price {
  display: inline-block;
}
.product-edit-new-container .dokan-new-product-featured-img {
  border: 4px dashed #dddddd;
  height: 294px !important;
  width: 100% !important;
  margin-bottom: 10px;
  overflow: hidden;
  position: relative;
}
.product-edit-new-container .dokan-new-product-featured-img i.fa-cloud-upload {
  display: block;
  font-size: 80px;
  color: #dfdfdf;
}
.product-edit-new-container .dokan-new-product-featured-img a.dokan-feat-image-btn {
  color: #8f8f8f;
  text-shadow: 0 1px 1px #fff;
  background: #f0f0f0;
  padding: 3px 10px;
  font-size: 12px;
}
.product-edit-new-container .dokan-new-product-featured-img a.dokan-feat-image-btn:hover {
  color: #ececec;
  text-shadow: 0 1px 1px #6f6f6f;
  background: #b4b4b4;
}
.product-edit-new-container .dokan-new-product-featured-img a.close {
  background: #000000;
  color: #ff0000;
  font-size: 100px;
  height: 100% !important;
  opacity: 0.7;
  padding: 25% 0;
  position: absolute;
  left: 0;
  text-align: center;
  text-shadow: none;
  top: 0;
  width: 100% !important;
  display: none;
  cursor: pointer;
}
.product-edit-new-container .dokan-new-product-featured-img img {
  max-width: 100%;
  width: 100%;
  height: 100%;
}
.product-edit-new-container .dokan-new-product-featured-img img:hover a.close {
  display: block;
}
.product-edit-new-container .dokan-new-product-featured-img .image-wrap:hover a.close {
  display: block;
}
.product-edit-new-container .instruction-inside {
  text-align: center;
  padding-top: 20%;
  padding-bottom: 30%;
  color: #aaa;
}
.product-edit-new-container .wp-editor-wrap {
  border: 1px solid #e7e7e7;
}
.product-edit-new-container .dokan-shipping-container .dokan-shipping-dimention-options {
  margin-bottom: 20px;
}
.product-edit-new-container .dokan-shipping-container .dokan-shipping-dimention-options input {
  width: 23.3%;
  float: right;
  margin-left: 2%;
}
.product-edit-new-container .dokan-shipping-container .dokan-shipping-dimention-options input#_height {
  margin-left: 0px;
}
.product-edit-new-container .dokan-shipping-container .dokan-shipping-product-options .dokan-additional-shipping-wrap .dokan-w3 {
  width: 32%;
  margin-left: 2%;
}
.product-edit-new-container .dokan-shipping-container .dokan-shipping-product-options .dokan-additional-shipping-wrap .dokan-w3.last-child {
  margin-left: 0px;
}
.product-edit-new-container .dokan-stock-management-wrapper div.dokan-w3 {
  width: 32%;
  margin-left: 2%;
}
.product-edit-new-container .dokan-stock-management-wrapper div.dokan-w3.last-child {
  margin-left: 0px;
}
.product-edit-new-container .dokan-download-wrapper {
  margin-top: 15px;
}
.product-edit-new-container aside {
  border: 1px solid #eee;
  margin-bottom: 15px;
  border-radius: 3px;
}
.product-edit-new-container #dokan-product-images ul.product_images {
  margin: 0 0 10px 0;
  padding: 0;
}
.product-edit-new-container #dokan-product-images ul.product_images li.image,
.product-edit-new-container #dokan-product-images ul.product_images li.dokan-sortable-placeholder {
  width: 64px;
  height: 64px;
  overflow: hidden;
  float: right;
  box-sizing: border-box;
  position: relative;
  margin: 9px 0 0 4px;
  border: 1px solid #eee;
  border-radius: 2px;
  padding: 2px;
  cursor: move;
}
.product-edit-new-container #dokan-product-images ul.product_images li.image img,
.product-edit-new-container #dokan-product-images ul.product_images li.dokan-sortable-placeholder img {
  padding: 0;
  margin: 0;
  max-width: 100%;
}
.product-edit-new-container #dokan-product-images ul.product_images li.image a.action-delete,
.product-edit-new-container #dokan-product-images ul.product_images li.dokan-sortable-placeholder a.action-delete {
  position: absolute;
  top: 0;
  color: red;
  background: rgba(0, 0, 0, 0.6);
  margin: 0;
  font-size: 35px;
  width: 100%;
  text-align: center;
  height: 100%;
  padding: 25% 3px 0;
  font-weight: bold;
  display: none;
}
.product-edit-new-container #dokan-product-images ul.product_images li.image:hover a.action-delete,
.product-edit-new-container #dokan-product-images ul.product_images li.dokan-sortable-placeholder:hover a.action-delete {
  display: flex;
  align-items: center;
  justify-content: center;
}
.product-edit-new-container #dokan-product-images ul.product_images li.dokan-sortable-placeholder {
  border: 1px dashed #ccc;
}
.product-edit-new-container .product-dimension .form-control {
  display: inline-block;
  width: 20%;
  margin-left: 5px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-attribute-wrapper .dokan-attribute-type {
  margin-bottom: 15px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-attribute-wrapper .dokan-attribute-type select {
  margin-left: 10px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-attribute-wrapper .dokan-attribute-type .dokan-attribute-spinner {
  margin-right: 10px;
  margin-top: 5px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-attribute-wrapper ul {
  padding-right: 0px;
  margin-right: 0px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-attribute-wrapper ul li.product-attribute-list {
  border-bottom: 1px solid #e3e3e3;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-attribute-wrapper ul li.product-attribute-list:first-child {
  border-top: 1px solid #e3e3e3;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-attribute-wrapper ul li.product-attribute-list .dokan-product-attribute-heading {
  padding: 10px 10px;
  cursor: move;
  position: relative;
  background-color: rgba(241, 241, 241, 0.61);
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-attribute-wrapper ul li.product-attribute-list .dokan-product-attribute-heading a.dokan-product-remove-attribute {
  position: absolute;
  top: 9px;
  left: 15px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-attribute-wrapper ul li.product-attribute-list .dokan-product-attribute-heading a.dokan-product-toggle-attribute {
  position: absolute;
  top: 7px;
  left: 80px;
  color: #222;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-attribute-wrapper ul li.product-attribute-list .dokan-product-attribute-item {
  padding: 10px 0px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-attribute-wrapper ul li.product-attribute-list .dokan-product-attribute-item .dokan-product-attribute-name {
  margin-bottom: 10px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-attribute-wrapper ul li.product-attribute-list .dokan-product-attribute-item .checkbox-item {
  font-weight: normal !important;
  margin-bottom: 0px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-attribute-wrapper ul li.product-attribute-list .dokan-product-attribute-item .dokan-pre-defined-attribute-btn-group {
  margin-top: 10px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper {
  margin-top: 20px;
  border-top: 1px solid #eee;
  padding-top: 15px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variation-top-toolbar {
  vertical-align: middle;
  line-height: 31px;
  margin-bottom: 20px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variation-top-toolbar select.variation-actions {
  margin-left: 5px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container {
  margin-bottom: 20px;
  /** Load all variation styles **/
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes {
  cursor: pointer;
  position: relative;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes .actions {
  position: absolute;
  top: 15px;
  left: 15px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes .actions i.fa {
  margin-left: 10px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes .actions i.fa.fa-bars {
  cursor: move;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes .actions i.fa.fa-sort-desc {
  cursor: pointer;
  padding-bottom: 4px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes h3.variation-topbar-heading {
  margin: 0 !important;
  font-size: inherit;
  position: relative;
  line-height: 35px;
  padding: 8px 10px;
  border-bottom: 1px solid #eee;
  background-color: rgba(241, 241, 241, 0.61);
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes h3.variation-topbar-heading strong {
  float: right;
  margin-left: 10px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes h3.variation-topbar-heading select {
  float: right;
  margin-left: 10px;
  width: 20%;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes .dokan-variable-attributes {
  margin-top: 15px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes .dokan-variable-attributes .thumbnail-checkbox-options {
  margin-bottom: 15px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes .dokan-variable-attributes .thumbnail-checkbox-options .upload_image {
  width: 130px;
  height: 130px;
  float: right;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes .dokan-variable-attributes .thumbnail-checkbox-options .upload_image .upload_image_button {
  display: block;
  width: 100%;
  height: 100%;
  margin-left: 20px;
  position: relative;
  cursor: pointer;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes .dokan-variable-attributes .thumbnail-checkbox-options .upload_image .upload_image_button img {
  max-width: 130px;
  max-height: 130px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes .dokan-variable-attributes .thumbnail-checkbox-options .upload_image .upload_image_button.dokan-img-remove img {
  display: block;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes .dokan-variable-attributes .thumbnail-checkbox-options .upload_image .upload_image_button.dokan-img-remove:before {
  content: 'X';
  display: none;
  position: absolute;
  font-size: 50px;
  top: 35%;
  right: 40%;
  color: #f35000;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes .dokan-variable-attributes .thumbnail-checkbox-options .upload_image .upload_image_button.dokan-img-remove:hover:before {
  display: block;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes .dokan-variable-attributes .thumbnail-checkbox-options .options {
  float: right;
  margin-right: 20px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes .dokan-variable-attributes .thumbnail-checkbox-options .options label {
  display: block;
  margin-bottom: 10px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes .dokan-variable-attributes .thumbnail-checkbox-options .options label:last-child {
  margin-bottom: 0px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes .dokan-variable-attributes .variable_pricing {
  margin-bottom: 10px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes .dokan-variable-attributes .weight-dimension {
  margin-bottom: 10px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes .dokan-variable-attributes .weight-dimension .dimensions_field .dokan-w3 {
  margin-left: 6px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variations-container .dokan-product-variation-itmes .dokan-variable-attributes .weight-dimension .dimensions_field .dokan-w3:last-child {
  margin-left: 0px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variation-default-toolbar {
  text-align: left;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variation-default-toolbar span.dokan-variation-default-label,
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variation-default-toolbar .dokan-variation-default-select {
  margin-left: 5px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variation-default-toolbar .float-none {
  display: inline-block;
  float: none !important;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variation-action-toolbar button.dokan-btn-default[disabled] {
  background-color: #fafafa;
  color: #b3b3b3;
  border-color: #ccc;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variation-action-toolbar .dokan-variations-pagenav .displaying-num,
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variation-action-toolbar .dokan-variations-pagenav .expand-close {
  font-style: italic;
  font-size: 13px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variation-action-toolbar .dokan-variations-pagenav span.pagination-links {
  margin-right: 10px;
}
.product-edit-new-container .dokan-attribute-variation-options .dokan-product-variation-wrapper .dokan-variation-action-toolbar .dokan-variations-pagenav span.pagination-links a {
  padding: 0px 8px;
  background: #eee;
  font-size: 14px;
}
.product-edit-new-container .dokan-product-type-container .content-half-part.virtual-checkbox {
  padding-right: 10px;
}
@media (max-width: 430px) {
  .product-edit-new-container .dokan-product-type-container .content-half-part.virtual-checkbox {
    padding-right: 0;
  }
}
.product-edit-container {
  font-size: 13px;
}
.product-edit-container #editable-post-name-full {
  display: none;
}
.product-edit-container #edit-slug-box {
  margin-top: 10px;
}
.product-edit-container .dokan-product-title-alert,
.product-edit-container .dokan-product-cat-alert {
  color: var(--dokan-button-background-color, #7047EB);
  font-style: italic;
  margin-top: 5px;
}
.product-edit-container .dokan-product-less-price-alert {
  color: var(--dokan-button-background-color, #7047EB);
  font-style: italic;
  margin-top: 5px;
}
.product-edit-container .tab-pane {
  padding: 15px 0;
}
.product-edit-container label {
  font-weight: normal;
  cursor: pointer;
}
.product-edit-container .dokan-feat-image-upload {
  border: 4px dashed #dddddd;
  height: 200px;
  width: 200px;
  margin-bottom: 20px;
  overflow: hidden;
  position: relative;
}
.product-edit-container .dokan-feat-image-upload i.fa-cloud-upload {
  display: block;
  font-size: 80px;
  color: #dfdfdf;
}
.product-edit-container .dokan-feat-image-upload a.dokan-feat-image-btn {
  color: #8f8f8f;
  text-shadow: 0 1px 1px #fff;
  background: #f0f0f0;
  padding: 3px 10px;
  font-size: 12px;
}
.product-edit-container .dokan-feat-image-upload a.dokan-feat-image-btn:hover {
  color: #ececec;
  text-shadow: 0 1px 1px #6f6f6f;
  background: #b4b4b4;
}
.product-edit-container .dokan-feat-image-upload a.close {
  background: #000000;
  color: #ff0000;
  font-size: 100px;
  height: 200px;
  opacity: 0.7;
  padding: 25% 0;
  position: absolute;
  left: 0;
  text-align: center;
  text-shadow: none;
  top: 0;
  width: 200px;
  display: none;
}
.product-edit-container .dokan-feat-image-upload img {
  max-width: 100%;
}
.product-edit-container .dokan-feat-image-upload img:hover a.close {
  display: block;
}
.product-edit-container .dokan-feat-image-upload .image-wrap:hover a.close {
  display: block;
}
.product-edit-container .dokan-feat-image-upload .container-image-and-badge {
  position: inherit;
}
@media (max-width: 430px) {
  .product-edit-container .dokan-feat-image-upload a.close {
    display: block !important;
    height: 80px !important;
    width: 80px !important;
    top: 0;
    left: 0;
    padding: 0;
    margin: 0;
    font-size: 50px;
  }
}
.product-edit-container .dokan-list-category-box {
  border: 1px solid #ccc;
  background: #fff;
  padding: 0px 15px 15px;
  max-height: 200px;
  min-height: 45px;
  overflow: scroll;
}
.product-edit-container .dokan-list-category-box ul.dokan-checkbox-cat {
  padding: 0;
  margin: 0;
}
.product-edit-container .dokan-list-category-box ul.dokan-checkbox-cat li {
  line-height: 20px;
}
.product-edit-container .instruction-inside {
  text-align: center;
  padding-top: 40px;
  color: #aaa;
}
.product-edit-container .wp-editor-wrap {
  border: 1px solid #e7e7e7;
}
.product-edit-container aside {
  border: 1px solid #eee;
  margin-bottom: 15px;
  border-radius: 3px;
}
.product-edit-container .dokan-edit-sidebar {
  margin-top: 35px;
}
.product-edit-container .dokan-edit-sidebar .dokan-side-head {
  background: #f5f5f5;
  padding: 10px;
}
.product-edit-container .dokan-edit-sidebar .dokan-side-body {
  padding: 10px;
}
.product-edit-container .dokan-edit-sidebar .dokan-side-body .dokan-form-control {
  width: 90%;
}
.product-edit-container .dokan-edit-sidebar .downloadable_files td {
  border-top: none;
  border-bottom: 1px solid #EDEDED;
}
.product-edit-container .dokan-edit-sidebar .downloadable_files td label {
  font-weight: normal;
}
.product-edit-container .dokan-edit-sidebar .downloadable_files td input {
  width: 90%;
}
.product-edit-container .dokan-edit-sidebar .downloadable_files ul {
  margin: 0px;
  padding: 0px;
}
.product-edit-container .dokan-edit-sidebar .downloadable_files ul li {
  margin-bottom: 15px;
}
.product-edit-container ul.label-on-left label {
  min-width: 200px;
  display: inline-block;
}
.product-edit-container ul.label-on-left li {
  clear: both;
}
.product-edit-container #product-attributes h4 {
  margin-bottom: 15px;
}
.product-edit-container #product-attributes .select-attribute.form-control {
  display: inline;
  width: auto;
}
.product-edit-container #variants-holder .inputs-box {
  background: #f5f5f5;
  margin-bottom: 20px;
}
.product-edit-container #variants-holder .inputs-box .box-header {
  background: #e5e5e5;
  padding: 5px 10px;
  font-size: 13px;
  margin-bottom: 10px;
}
.product-edit-container #variants-holder .inputs-box .option-couplet {
  margin-right: 15px;
}
.product-edit-container #variants-holder .inputs-box .option-couplet li:before {
  content: '-';
}
.product-edit-container #variants-holder .inputs-box .option-couplet li {
  margin-bottom: 8px;
}
.product-edit-container #variants-holder .inputs-box .box-inside {
  padding: 10px;
}
.product-edit-container #variants-holder .inputs-box .box-inside .attribute-config {
  width: 35%;
  float: right;
}
.product-edit-container #variants-holder .inputs-box .box-inside .attribute-options {
  float: right;
  width: 60%;
}
.product-edit-container #variants-holder .actions a {
  color: #666;
  padding: 0 4px 2px;
  font-weight: bold;
  border: 1px solid #ddd;
  text-decoration: none;
  background: #fff;
}
.product-edit-container #variants-holder .actions a:hover {
  background: #666;
  color: #fff;
}
.product-edit-container #product-variations .wc-metabox {
  border-radius: 3px;
  background: #f5f5f5;
  margin-bottom: 10px;
  font-size: 13px;
  padding-bottom: 3px;
}
.product-edit-container #product-variations .wc-metabox h3 {
  background: #e5e5e5;
  padding: 10px;
  font-size: 14px;
}
.product-edit-container #product-variations .wc-metabox h3 button {
  float: left;
  font-size: 12px;
  margin-top: -5px;
}
.product-edit-container #product-variations .wc-metabox table {
  margin: 10px;
}
.product-edit-container #product-variations .wc-metabox table td {
  vertical-align: top;
  width: 50%;
}
.product-edit-container #product-variations .wc-metabox table td.upload_image img {
  width: 100px;
  max-width: 100px;
  max-height: 100px;
}
.product-edit-container #product-variations .wc-metabox table td.options label {
  display: block;
}
.product-edit-container #product-variations .wc-metabox table td.sku,
.product-edit-container #product-variations .wc-metabox table td.upload_image,
.product-edit-container #product-variations .wc-metabox table td.options {
  width: 15%;
}
.product-edit-container #product-variations .wc-metabox td.data {
  padding: 0 10px 0 0;
  background: #fff;
  margin-right: 20px;
}
.product-edit-container #product-variations .wc-metabox table.data_table {
  margin: 0;
  width: 100%;
}
.product-edit-container #product-variations .wc-metabox table.data_table td {
  padding: 0 0 6px 6px;
  width: 50%;
}
.product-edit-container #product-variations .wc-metabox table.data_table td input {
  width: 100%;
  padding: 5px 10px;
}
.product-edit-container #product-variations .wc-metabox table.data_table td input.upload_file_button {
  width: auto;
  margin-top: 5px;
}
.product-edit-container #product-variations .wc-metabox table.data_table td.dimensions_field input {
  width: 25%;
  padding: 3px;
}
.product-edit-container #product-variations .wc-metabox table.data_table label {
  display: block;
}
.product-edit-container #product-variations .wc-metabox label,
.product-edit-container #product-variations .wc-metabox select,
.product-edit-container #product-variations .wc-metabox input {
  font-size: 12px;
}
.product-edit-container #product-variations .wc-metabox input[type='text'],
.product-edit-container #product-variations .wc-metabox input[type='number'],
.product-edit-container #product-variations .wc-metabox select {
  border: 1px solid #EDEDED;
}
.product-edit-container #product-variations .wc-metabox select {
  width: auto;
  display: inline-block;
}
.product-edit-container #product-variations p.toolbar {
  padding-top: 10px;
}
.product-edit-container #product-variations p.toolbar select {
  border: 1px solid #EDEDED;
}
.product-edit-container #dokan-product-images ul.product_images {
  margin: 0 0 10px 0;
  padding: 0;
}
.product-edit-container #dokan-product-images ul.product_images li.image,
.product-edit-container #dokan-product-images ul.product_images li.dokan-sortable-placeholder,
.product-edit-container #dokan-product-images ul.product_images li.add-image {
  width: 64px;
  height: 64px;
  overflow: hidden;
  float: right;
  box-sizing: border-box;
  position: relative;
  margin: 9px 0 0 4px;
  border: 1px solid #eee;
  border-radius: 2px;
  padding: 2px;
  cursor: move;
}
.product-edit-container #dokan-product-images ul.product_images li.image.add-image,
.product-edit-container #dokan-product-images ul.product_images li.dokan-sortable-placeholder.add-image,
.product-edit-container #dokan-product-images ul.product_images li.add-image.add-image {
  cursor: pointer;
  text-align: center;
  line-height: 50px;
  border: 2px dashed #ddd;
}
.product-edit-container #dokan-product-images ul.product_images li.image.add-image a,
.product-edit-container #dokan-product-images ul.product_images li.dokan-sortable-placeholder.add-image a,
.product-edit-container #dokan-product-images ul.product_images li.add-image.add-image a {
  width: 100%;
  height: 100%;
  color: #afafaf;
  font-size: 18px;
}
.product-edit-container #dokan-product-images ul.product_images li.image img,
.product-edit-container #dokan-product-images ul.product_images li.dokan-sortable-placeholder img,
.product-edit-container #dokan-product-images ul.product_images li.add-image img {
  padding: 0;
  margin: 0;
  max-width: 100%;
}
.product-edit-container #dokan-product-images ul.product_images li.image a.action-delete,
.product-edit-container #dokan-product-images ul.product_images li.dokan-sortable-placeholder a.action-delete,
.product-edit-container #dokan-product-images ul.product_images li.add-image a.action-delete {
  position: absolute;
  top: 0;
  color: red;
  background: rgba(0, 0, 0, 0.6);
  margin: 0;
  padding: 0 3px;
  font-size: 35px;
  width: 100%;
  text-align: center;
  height: 100%;
  font-weight: bold;
  display: none;
}
.product-edit-container #dokan-product-images ul.product_images li.image:hover a.action-delete,
.product-edit-container #dokan-product-images ul.product_images li.dokan-sortable-placeholder:hover a.action-delete,
.product-edit-container #dokan-product-images ul.product_images li.add-image:hover a.action-delete {
  display: flex;
  align-items: center;
  justify-content: center;
}
.product-edit-container #dokan-product-images ul.product_images li.dokan-sortable-placeholder {
  border: 1px dashed #ccc;
}
.product-edit-container .product-dimension .form-control {
  display: inline-block;
  width: 25%;
  margin-left: 5px;
}
.toggle-sidebar-container {
  padding: 8px 0;
  border-bottom: 1px solid #dfdfdf;
  margin-bottom: 15px;
}
.toggle-sidebar-container .dokan-toggle-sidebar {
  display: block;
  margin-bottom: 5px;
}
.dokan-product-edit header.dokan-dashboard-header h1.entry-title span.dokan-product-status-label {
  font-size: 11px;
  margin-right: 15px;
  vertical-align: middle;
}
.dokan-product-edit header.dokan-dashboard-header h1.entry-title span.dokan-product-hidden-label {
  font-size: 13px;
  margin-left: 30px;
  margin-top: 9px;
}
.dokan-product-edit header.dokan-dashboard-header h1.entry-title a.view-product {
  background-color: #fafafa;
  border-color: #ebebeb;
}
.dokan-product-edit-area .dokan-product-edit-left {
  width: 67%;
  float: right;
  margin-left: 4%;
}
.dokan-product-edit-area .dokan-product-edit-right {
  width: 27%;
  float: right;
}
.dokan-product-edit-area .discount-price label {
  font-weight: normal;
  cursor: pointer;
}
.white-popup {
  position: relative;
  background: #fff;
  padding: 0px;
  width: auto;
  max-width: 690px;
  margin: 20px auto;
}
.dokan-dashboard-not-product-found {
  text-align: center;
  margin-top: 50px;
}
.dokan-dashboard-not-product-found .no-product-found-icon {
  margin: 20px auto;
  max-width: 100%;
}
.dokan-dashboard-not-product-found .dokan-blank-product-message {
  font-size: 20px;
  margin-bottom: 20px;
}
.dokan-add-new-product-popup h2 {
  padding: 0px 20px 16px;
  border-bottom: 1px solid #eee;
  margin-bottom: 0px;
  font-size: 22px;
  color: #5d5d5d;
  margin-top: 15px;
  position: absolute;
  overflow: hidden;
  width: 690px;
  z-index: 999999;
  background: #fff;
  margin-top: 0px;
  padding-top: 16px;
  -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  -moz-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}
.dokan-add-new-product-popup .mfp-close {
  top: 10px;
  left: 5px;
  z-index: 999999;
}
.dokan-add-new-product-popup .product-form-container {
  margin-top: 30px;
  padding: 20px;
  height: 550px;
}
.dokan-add-new-product-popup .product-form-container .dokan-feat-image-upload {
  border: 3px dashed #dddddd;
  height: 65%;
  width: 98%;
  margin-bottom: 10px;
  margin-left: 15px;
  overflow: hidden;
  position: relative;
}
.dokan-add-new-product-popup .product-form-container .dokan-feat-image-upload i.fa-cloud-upload {
  display: block;
  font-size: 80px;
  color: #dfdfdf;
}
.dokan-add-new-product-popup .product-form-container .dokan-feat-image-upload .instruction-inside {
  text-align: center;
  padding-top: 20%;
  padding-bottom: 30%;
  color: #aaa;
}
.dokan-add-new-product-popup .product-form-container .dokan-feat-image-upload a.dokan-feat-image-btn {
  color: #8f8f8f;
  text-shadow: 0 1px 1px #fff;
  background: #f0f0f0;
  padding: 3px 10px;
  font-size: 12px;
}
.dokan-add-new-product-popup .product-form-container .dokan-feat-image-upload a.dokan-feat-image-btn:hover {
  color: #ececec;
  text-shadow: 0 1px 1px #6f6f6f;
  background: #b4b4b4;
}
.dokan-add-new-product-popup .product-form-container .dokan-feat-image-upload a.close {
  background: #000000;
  color: #ff0000;
  font-size: 100px;
  height: 100%;
  opacity: 0.7;
  padding: 25% 0;
  position: absolute;
  left: 0;
  text-align: center;
  text-shadow: none;
  top: 0;
  width: 100%;
  display: none;
  cursor: pointer;
}
.dokan-add-new-product-popup .product-form-container .dokan-feat-image-upload img {
  max-width: 100%;
  width: 292px;
  height: 212px;
  object-fit: cover;
}
.dokan-add-new-product-popup .product-form-container .dokan-feat-image-upload img:hover a.close {
  display: block;
}
.dokan-add-new-product-popup .product-form-container .dokan-feat-image-upload .image-wrap:hover a.close {
  display: block;
}
.dokan-add-new-product-popup .dokan-feat-image-content {
  width: 35%;
  margin-left: 15px;
}
.dokan-add-new-product-popup .dokan-feat-image-content ul > li {
  list-style: none;
}
.dokan-add-new-product-popup .dokan-feat-image-content ul > li > a {
  display: block;
  width: 100%;
  height: 100%;
}
.dokan-add-new-product-popup #dokan-product-images ul.product_images {
  margin: 0 0 10px 0;
  padding: 0;
}
.dokan-add-new-product-popup #dokan-product-images ul.product_images li.image,
.dokan-add-new-product-popup #dokan-product-images ul.product_images li.dokan-sortable-placeholder,
.dokan-add-new-product-popup #dokan-product-images ul.product_images li.add-image {
  width: 50px;
  height: 50px;
  overflow: hidden;
  float: right;
  box-sizing: border-box;
  position: relative;
  margin: 9px 0 0 4px;
  border: 1px solid #eee;
  border-radius: 2px;
  padding: 2px;
  cursor: move;
}
.dokan-add-new-product-popup #dokan-product-images ul.product_images li.image.add-image,
.dokan-add-new-product-popup #dokan-product-images ul.product_images li.dokan-sortable-placeholder.add-image,
.dokan-add-new-product-popup #dokan-product-images ul.product_images li.add-image.add-image {
  cursor: pointer;
  text-align: center;
  line-height: 40px;
  border: 2px dashed #ddd;
}
.dokan-add-new-product-popup #dokan-product-images ul.product_images li.image.add-image a,
.dokan-add-new-product-popup #dokan-product-images ul.product_images li.dokan-sortable-placeholder.add-image a,
.dokan-add-new-product-popup #dokan-product-images ul.product_images li.add-image.add-image a {
  width: 100%;
  height: 100%;
  color: #afafaf;
  font-size: 18px;
}
.dokan-add-new-product-popup #dokan-product-images ul.product_images li.image img,
.dokan-add-new-product-popup #dokan-product-images ul.product_images li.dokan-sortable-placeholder img,
.dokan-add-new-product-popup #dokan-product-images ul.product_images li.add-image img {
  padding: 0;
  margin: 0;
  max-width: 100%;
}
.dokan-add-new-product-popup #dokan-product-images ul.product_images li.image a.action-delete,
.dokan-add-new-product-popup #dokan-product-images ul.product_images li.dokan-sortable-placeholder a.action-delete,
.dokan-add-new-product-popup #dokan-product-images ul.product_images li.add-image a.action-delete {
  position: absolute;
  top: 0;
  color: red;
  background: rgba(0, 0, 0, 0.6);
  margin: 0;
  font-size: 50px;
  width: 100%;
  text-align: center;
  height: 100%;
  font-weight: bold;
  display: none;
}
.dokan-add-new-product-popup #dokan-product-images ul.product_images li.image:hover a.action-delete,
.dokan-add-new-product-popup #dokan-product-images ul.product_images li.dokan-sortable-placeholder:hover a.action-delete,
.dokan-add-new-product-popup #dokan-product-images ul.product_images li.add-image:hover a.action-delete {
  display: flex;
  align-items: center;
  justify-content: center;
}
.dokan-add-new-product-popup #dokan-product-images ul.product_images li.dokan-sortable-placeholder {
  border: 1px dashed #ccc;
}
.dokan-add-new-product-popup .dokan-product-field-content {
  width: 62.5%;
}
.dokan-add-new-product-popup .dokan-product-field-content label {
  font-weight: normal;
}
.dokan-add-new-product-popup .dokan-product-field-content input[type='checkbox'] {
  margin-left: 4px;
}
.dokan-add-new-product-popup .product-full-container {
  margin-top: 10px;
}
.dokan-add-new-product-popup .product-container-footer {
  padding: 20px 20px;
  border-top: 1px solid #eee;
  text-align: left;
}
.dokan-add-new-product-popup .product-container-footer .dokan-add-new-product-spinner {
  position: static;
  margin-left: 8px;
  vertical-align: middle;
}
.dokan-add-new-product-popup .product-container-footer .dokan-show-add-product-error {
  color: #d9534f;
  margin-left: 8px;
  vertical-align: middle;
  float: right;
  display: block;
}
.dokan-add-new-product-popup .product-container-footer .dokan-show-add-product-success {
  color: #5cb85c;
  margin-left: 8px;
  vertical-align: middle;
  float: right;
  display: block;
}
.select2-container--open .select2-dropdown--below {
  margin-top: 32px;
}
.select2-container--open .select2-dropdown--above {
  margin-top: 32px;
}
.dokan-vendor-info-wrap {
  border: 1px solid #E7E7E7;
  padding: 15px;
  display: flex;
  align-items: center;
  margin-top: 10px;
}
.dokan-vendor-info-wrap h5,
.dokan-vendor-info-wrap p {
  margin: 0;
}
.dokan-vendor-info-wrap a {
  text-decoration: none!important;
  background: transparent!important;
  box-shadow: unset!important;
  padding: 0!important;
}
.dokan-vendor-info-wrap .dokan-vendor-image {
  width: 50px;
  height: 50px;
  margin-left: 13px;
}
.dokan-vendor-info-wrap .dokan-vendor-image img {
  border-radius: 50%;
}
.dokan-vendor-info-wrap .dokan-vendor-info .dokan-vendor-name {
  display: flex;
  align-items: center;
}
.dokan-vendor-info-wrap .dokan-vendor-info .dokan-vendor-name h5 {
  font-size: 16px;
  font-weight: 500;
  color: #000;
  margin-left: 10px;
}
.dokan-vendor-info-wrap .dokan-vendor-info .dokan-vendor-rating {
  display: flex;
  align-items: center;
}
.dokan-vendor-info-wrap .dokan-vendor-info .dokan-vendor-rating p {
  color: #C1C1C1;
  font-size: 14px;
  font-weight: 500;
  margin-left: 6px;
}
.dokan-vendor-info-wrap .dokan-vendor-info .dokan-vendor-rating .dashicons-star-empty {
  color: #e0e0e0;
  font-size: 16px;
}
.dokan-vendor-info-wrap .dokan-vendor-info .dokan-vendor-rating .dashicons-star-filled,
.dokan-vendor-info-wrap .dokan-vendor-info .dokan-vendor-rating .dashicons-star-half {
  color: #ffc239;
  font-size: 16px;
}
.dokan-vendor-info-wrap .dokan-vendor-info .dokan-ratings-count {
  color: #C1C1C1;
  font-size: 11px;
  font-weight: normal;
}
.dokan-settings-content .dokan-settings-area {
  position: relative;
}
.dokan-settings-content .dokan-settings-area .dokan-store-settign-header-wrap {
  width: 70%;
  float: right;
}
.dokan-settings-content .dokan-settings-area .dokan-update-setting-top {
  width: 30%;
  float: left;
}
.dokan-settings-content .dokan-settings-area .dokan-dashboard-header {
  margin: 0 0 10px 0;
  border-bottom: 1px solid #EDEDED;
  padding: 0 0 10px 0;
}
.dokan-settings-content .dokan-settings-area .dokan-dashboard-header h1 {
  margin: 0 0 0px 0;
  border-bottom: none;
}
.dokan-settings-content .dokan-settings-area .dokan-dashboard-header:after {
  content: "";
  display: table;
  clear: both;
}
.dokan-settings-content .dokan-settings-area h1 {
  margin: 0px;
  padding: 0px;
}
.dokan-settings-content .dokan-settings-area .dokan-banner {
  border: 4px dashed #d8d8d8;
  margin: 0 auto 35px;
  max-width: 850px;
  text-align: center;
  overflow: hidden;
  position: relative;
  min-height: 300px;
}
.dokan-settings-content .dokan-settings-area .dokan-banner img {
  max-width: 100%;
}
.dokan-settings-content .dokan-settings-area .dokan-banner .dokan-remove-banner-image {
  position: absolute;
  width: 100%;
  height: 300px;
  background: #000;
  top: 0;
  right: 0;
  opacity: 0.7;
  font-size: 100px;
  color: red;
  padding-top: 70px;
  display: none;
}
.dokan-settings-content .dokan-settings-area .dokan-banner:hover {
  cursor: pointer;
}
.dokan-settings-content .dokan-settings-area .dokan-banner:hover .dokan-remove-banner-image {
  display: block;
}
.dokan-settings-content .dokan-settings-area .dokan-gravatar {
  position: relative;
}
.dokan-settings-content .dokan-settings-area .dokan-gravatar .dokan-remove-gravatar-image {
  position: absolute;
  width: 80px;
  height: 80px;
  background: #000;
  top: 0;
  right: 0;
  opacity: 0.7;
  font-size: 70px;
  text-align: center;
  color: #f00;
  padding-top: 5px;
  display: none;
  border-radius: 50%;
}
.dokan-settings-content .dokan-settings-area .dokan-gravatar .gravatar-wrap .dokan-gravatar-img {
  border-radius: 50%;
  height: 80px !important;
  width: 80px;
}
.dokan-settings-content .dokan-settings-area .dokan-gravatar:hover .dokan-remove-gravatar-image {
  display: block;
}
.dokan-settings-content .dokan-settings-area .button-area {
  margin-top: 35px;
}
.dokan-settings-content .dokan-settings-area .button-area i.fa-cloud-upload {
  display: block;
  font-size: 80px;
  color: #dfdfdf;
}
.dokan-settings-content .dokan-settings-area .button-area .help-block {
  font-size: 12px;
  color: #b1b1b1;
}
.dokan-settings-content .dokan-settings-area ul.dokan-categories {
  list-style: none;
  padding: 0;
  margin: 0;
}
.dokan-settings-content .dokan-settings-area ul.dokan-categories label {
  font-weight: normal;
}
.dokan-settings-content .dokan-settings-area .dokan-address-fields label {
  font-weight: normal;
  font-size: 12px;
}
.dokan-settings-content .dokan-settings-area .dokan-form-group .checkbox {
  margin-top: 0px;
}
.store-open-close .dokan-form-group {
  text-align: right;
  display: flex;
}
.store-open-close label.day {
  width: 200px;
}
.store-open-close label.time {
  padding-right: 5px;
}
.store-open-close select.dokan-form-control {
  width: auto;
}
@media only screen and (max-width: 415px) {
  .store-open-close label:first-child {
    width: 100%;
    text-align: right;
  }
  .store-open-close .time input {
    width: 75px;
  }
  .store-open-close .dokan-form-group:first-child {
    margin-top: 50px;
  }
  .store-open-close label.day.control-label {
    padding-left: 85px;
  }
}
.dokan-store.dokan-theme-twentytwelve .site-content {
  float: none;
  width: 100%;
}
.dokan-store-wrap {
  display: flex;
  margin: 20px 0;
  flex-direction: column;
}
.dokan-store-wrap.layout-left {
  flex-direction: column-reverse;
}
@media (min-width: 1000px) {
  .dokan-store-wrap.layout-left,
  .dokan-store-wrap.layout-right {
    flex-direction: row;
  }
  .dokan-store-wrap .dokan-store-sidebar {
    flex: 0 1 26%;
  }
  .dokan-store-wrap .dokan-single-store {
    flex: 0 1 100%;
  }
  .dokan-store-wrap.layout-left .dokan-store-sidebar {
    margin-left: 4%;
  }
  .dokan-store-wrap.layout-right .dokan-store-sidebar {
    margin-right: 4%;
  }
}
.dokan-store-products-filter-area {
  margin-bottom: 30px;
}
.dokan-store-products-filter-area .product-name-search {
  height: 40px;
  border: solid 1px #eee;
  background-color: #fff;
  float: right;
  width: 250px;
}
.dokan-store-products-filter-area .product-name-search:focus {
  outline: 0px;
}
.dokan-store-products-filter-area .orderby-search {
  height: 40px;
  border: solid 1px #eee;
  background-color: #fff;
  float: left;
}
.dokan-store-products-filter-area .search-store-products {
  height: 40px;
  border: solid 1px #ccc;
  background-color: #eee;
  float: right;
  line-height: 40px;
  padding: 0px 25px;
}
.dokan-store-products-filter-area .dokan-store-products-ordeby {
  width: 100%;
  margin-left: 0px;
  position: relative;
}
.dokan-store-products-filter-area .dokan-store-products-search-has-results {
  border: 1px solid #e0e0e0;
}
.dokan-store-products-filter-area #dokan-store-products-search-result {
  display: none;
  position: absolute;
  width: 100%;
  background: #ffffff;
  z-index: 15;
  transform: translateY(-1px);
  max-width: 349px;
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  top: 42px;
}
.dokan-store-products-filter-area #dokan-store-products-search-result ul {
  list-style: none;
  margin: 0 !important;
  padding: 15px;
}
.dokan-store-products-filter-area #dokan-store-products-search-result li {
  display: block;
  padding: 8px 0;
  position: relative;
  border-bottom: 1px dashed #e0e0e0;
  margin-right: 0px;
}
.dokan-store-products-filter-area #dokan-store-products-search-result li:last-child {
  border-bottom: none;
}
.dokan-store-products-filter-area #dokan-store-products-search-result a {
  display: table;
  width: 100%;
}
.dokan-store-products-filter-area #dokan-store-products-search-result a > * {
  display: table-cell;
  vertical-align: top;
}
.dokan-store-products-filter-area #dokan-store-products-search-result .dokan-ls-product-image {
  width: 40px;
  max-width: 40px;
}
.dokan-store-products-filter-area #dokan-store-products-search-result .dokan-ls-product-data {
  padding-right: 20px;
}
.dokan-store-products-filter-area #dokan-store-products-search-result .dokan-ls-product-data div:not(.dokan-ls-product-categories) {
  display: inline-block;
  vertical-align: middle;
}
.dokan-store-products-filter-area #dokan-store-products-search-result .dokan-ls-product-data .dokan-ls-product-price {
  position: absolute;
  top: 12px;
  left: 0;
}
.dokan-store-products-filter-area #dokan-store-products-search-result .dokan-ls-product-data .dokan-ls-product-stock {
  padding: 4px 8px;
  background: #eeeeee;
  border-radius: 4px;
  position: absolute;
  bottom: 10px;
  left: 0;
}
.dokan-store-products-filter-area #dokan-store-products-search-result h3 {
  display: block;
  margin: 0px;
  font-size: 15px;
}
.dokan-store-products-filter-area #dokan-store-products-search-result .dokan-ls-product-categories span {
  display: inline-block;
  margin-left: 4px;
  font-size: 13px;
}
.dokan-store-products-filter-area #dokan-store-products-search-result .dokan-ls-product-categories span:after {
  content: ",";
}
.dokan-store-products-filter-area #dokan-store-products-search-result .dokan-ls-product-categories span:last-child:after {
  content: "";
}
.dokan-store-products-filter-area #dokan-store-products-search-result .dokan-ls-product-categories span:last-child {
  margin-left: 0;
}
.dokan-store-products-filter-area #dokan-store-products-search-result .product-price {
  font-size: 14px;
  font-weight: bold;
  bottom: 10px;
  text-align: left;
}
.dokan-store-products-filter-area #dokan-store-products-search-result .dokan-ls-sale-price {
  color: #8f949b;
  text-decoration: line-through;
  margin-right: 8px;
}
.dokan-store-products-filter-area .dokan-ajax-search-loader {
  background-image: url(../js/../images/spinner-2x.gif);
  background-position: left center;
  background-repeat: no-repeat;
  background-size: 25px 25px;
  z-index: 999;
}
.dokan-single-store {
  margin: 0;
}
.dokan-single-store .profile-frame {
  padding: 0px;
  position: relative;
  background-size: cover;
  background-color: #eee;
}
.dokan-single-store .profile-frame .profile-info-box {
  position: relative;
}
.dokan-single-store .profile-frame .profile-info-box a {
  color: inherit;
}
.dokan-single-store .profile-frame .profile-info-box:hover {
  color: #fff;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-img {
  width: 100%;
  height: auto;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-img.dummy-image {
  background-image: url(../js/../images/default-store-banner.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.dokan-single-store .profile-frame .profile-info-box.profile-layout-default .profile-info-img {
  height: 100% !important;
  object-fit: cover;
}
.dokan-single-store .profile-frame .profile-info-box.profile-layout-default .profile-info-img.dummy-image {
  position: absolute;
  right: 0;
  top: 0;
  background-size: cover;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper {
  position: absolute;
  top: 0;
  right: 0;
  color: #fff;
  width: 100%;
  height: 100%;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery {
  width: 320px;
  height: 100%;
  padding-top: 25px;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info-head .profile-img {
  text-align: center;
  margin-bottom: 12px;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info-head .profile-img.profile-img-circle img {
  border-radius: 50%;
  background: #fff;
  width: 80px;
  height: 80px;
  margin-right: auto;
  margin-left: auto;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info-head .profile-img.profile-img-square img {
  background-color: #fff;
  border: 1px solid #EDEDED;
  border-radius: 0;
  padding: 4px;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info-head .store-name {
  font-weight: bold;
  padding: 0;
  font-size: 20px;
  margin: 0 0 18px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info-head .store-name svg {
  margin-right: 5px;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info {
  padding: 0 30px;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info {
  cursor: pointer;
  position: relative;
  list-style: none;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-phone a,
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-email a {
  text-decoration: none;
  color: inherit;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info span.fa-angle-down {
  margin-right: 10px;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice .store-notice {
  min-width: 96px;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice #vendor-store-times {
  top: 110%;
  right: -20%;
  color: #3E474F;
  width: 310px;
  z-index: 1;
  display: none;
  padding: 1.5em 2em;
  overflow: auto;
  position: absolute;
  max-height: 435px;
  background: #fff;
  box-shadow: 0 10px 15px -3px #00000040, 0 4px 6px -4px #00000010;
  border-radius: 6px;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice #vendor-store-times .store-times-heading {
  margin: 10px 0 25px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice #vendor-store-times .store-times-heading i.fa-calendar-day {
  color: #2471A9;
  width: 30px;
  margin: 0 -10px 0 10px;
  height: 30px;
  border: 1px solid #CBD9DC;
  display: flex;
  align-items: center;
  border-radius: 50%;
  justify-content: center;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice #vendor-store-times .store-times-heading h4 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 0;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice #vendor-store-times span {
  margin-right: 10px;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice #vendor-store-times .store-time-tags {
  display: flex;
  font-size: 15px;
  margin-bottom: 12px;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice #vendor-store-times .store-time-tags .store-days {
  flex: 2.3;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice #vendor-store-times .store-time-tags .current_day,
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice #vendor-store-times .store-time-tags .current_time {
  font-weight: 700;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice #vendor-store-times .store-time-tags .store-times {
  flex: 4;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice #vendor-store-times .store-time-tags .store-times .store-open,
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice #vendor-store-times .store-time-tags .store-times .store-close {
  cursor: pointer;
  display: inline-block;
  padding: 2px 3px 3.5px;
  transition: 0.5s ease;
  list-style: none;
  margin-bottom: 5.5px;
  border-radius: 0.375rem;
  text-decoration: none;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice #vendor-store-times .store-time-tags .store-times .store-close {
  color: #D07272;
  padding: 1px 12px;
  border-radius: 16px;
  background-color: #F8E8E7;
  max-width: 75px;
  text-align: center;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice #vendor-store-times .store-time-tags:last-child {
  margin-bottom: 0;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice #vendor-store-times::-webkit-scrollbar {
  width: 10px;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice #vendor-store-times::-webkit-scrollbar-thumb {
  background: #8d9399;
  border-radius: 5px;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice #vendor-store-times::-webkit-scrollbar-thumb:hover {
  background: #555;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice #vendor-store-times:hover {
  display: block;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice:hover #vendor-store-times {
  display: block;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .dokan-store-open-close .store-open-close-notice:hover span.fa-angle-down:after {
  content: "";
  top: 75%;
  left: 44%;
  width: 25px;
  height: 40px;
  z-index: 1;
  position: absolute;
  transform: rotate(-45deg);
  border-radius: 5px;
  background-color: #fff;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info li {
  padding-bottom: 8px;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info i {
  font-weight: bold;
  float: right;
  margin-right: -25px;
  margin-top: 4px;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info ul.store-social {
  list-style: none;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info ul.store-social li {
  display: inline-block;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info ul.store-social i {
  font-size: 20px;
  float: none;
  font-weight: normal;
  margin: 0;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info ul.store-social i.fa-facebook-square {
  color: #3b5998;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info ul.store-social i.fa-google-plus-square {
  color: #dd4b39;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info ul.store-social i.fa-twitter-square {
  color: #55acee;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info ul.store-social i.fa-pinterest-square {
  color: #bd081c;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info ul.store-social i.fa-linkedin {
  color: #007bb5;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info ul.store-social i.fa-youtube-square {
  color: #bb0000;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info ul.store-social i.fa-instagram {
  color: #125688;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info ul.store-social i.fa-flickr {
  color: #ff0084;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info ul.store-social i.fa-threads {
  background: linear-gradient(-45deg, #833ab4, #fd1d1d, #fcb045);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .seller-rating {
  display: none;
}
.dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper {
  position: relative;
  background-color: #fff;
  color: inherit;
  border: 1px solid #EDEDED;
  padding: 0 15px;
}
.dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery {
  color: #444;
  width: 100%;
  margin-top: -55px;
  padding: 0;
  position: relative;
  background: none;
}
.dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info-head {
  float: right;
}
.dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info-head .profile-img {
  margin-bottom: 0;
}
.dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info-head .profile-img img {
  width: 150px;
  height: 150px;
}
.dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info {
  float: right;
  padding: 0;
}
.dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-name {
  color: #fff;
  margin: 10px 15px 25px 0;
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.8);
  font-weight: bold;
  font-size: 28px;
  line-height: 1.1;
}
.dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info {
  padding-right: 40px;
  font-size: 14px;
  line-height: 21px;
  margin-right: 0;
}
.dokan-single-store .profile-frame.profile-frame-no-banner {
  background-color: transparent;
}
.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-img {
  display: none;
}
.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-summery-wrapper {
  border: 1px solid #EDEDED;
  position: relative;
}
.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery {
  background-color: #fff;
  width: 100%;
  padding: 10px;
}
.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info-head {
  width: 20%;
  float: right;
}
.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info-head .profile-img img {
  background: #fff;
  border: 1px solid #ddd;
  padding: 2px;
  width: 150px;
  height: 150px;
}
.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info {
  float: right;
  padding: 0 25px;
  color: #444;
  width: 80%;
}
.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-name {
  margin-top: 5px;
  font-size: 30px;
  font-weight: bold;
}
.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info {
  margin: 0;
  padding-right: 25px;
  clear: both;
}
.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .store-open-close-notice #vendor-store-times {
  right: -30%;
}
.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .store-open-close-notice:hover span.fa-angle-down:after {
  left: -2.5px;
}
.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li {
  float: right;
  margin-left: 40px;
  position: relative;
}
.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li i {
  margin-right: -22px;
}
.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li:before {
  content: "·";
  position: absolute;
  top: 10px;
  right: -35px;
  color: #afafaf;
  font-size: 25px;
  line-height: 0;
}
.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li:first-child:before {
  content: "";
}
.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper {
  clear: both;
  position: relative;
}
.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper .store-social {
  display: flex;
  padding-right: 0;
}
.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper .store-social li {
  margin: 0 0 0 8px;
}
.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper .store-social li a i {
  text-shadow: none;
}
.dokan-single-store .dokan-store-tabs {
  text-align: center;
}
.dokan-single-store .dokan-store-tabs ul.dokan-modules-button {
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row-reverse;
  justify-content: center;
  margin: 0;
  padding: 10px 0;
  border: 1px solid #EDEDED;
}
.dokan-single-store .dokan-store-tabs ul.dokan-modules-button li {
  display: inline-block;
}
.dokan-single-store .dokan-store-tabs ul.dokan-list-inline {
  display: flex;
  background-color: #fff;
  border-width: 0 1px 1px 1px;
  border-color: #EDEDED;
  border-style: solid;
  margin: 0;
  padding: 0;
  margin-bottom: 20px;
  font-size: 14px;
}
.dokan-single-store .dokan-store-tabs ul.dokan-list-inline li {
  display: inline-block;
  flex: auto;
  border-left: 1px solid #EDEDED;
  margin-left: -4px;
}
.dokan-single-store .dokan-store-tabs ul.dokan-list-inline li:last-child {
  border-left: unset;
}
.dokan-single-store .dokan-store-tabs ul.dokan-list-inline li a {
  width: 100%;
  text-decoration: none;
  color: #666;
  padding: 10px 15px;
  display: inline-block;
}
.dokan-single-store .dokan-store-tabs ul.dokan-list-inline li a:hover {
  background: #eee;
}
.dokan-store-widget input[type=text],
.dokan-store-widget input[type=email],
.dokan-store-widget textarea {
  width: 100%;
}
.dokan-store-widget form.seller-form ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.dokan-store-widget form.seller-form ul li {
  margin-bottom: 8px;
  border-bottom: none;
}
.dokan-store-widget form.seller-form ul li label.error {
  font-size: 12px;
  margin-top: 2px;
  margin-bottom: 0px;
}
.dokan-store-widget #dokan-store-location {
  width: 100%;
  height: 200px;
}
.dokan-store-widget form#dokan-form-contact-seller .dokan-privacy-policy-text p {
  word-break: break-all;
}
.dokan-seller-search-form {
  margin: 30px 0px;
}
.dokan-seller-search-form .dokan-w4 {
  padding: 0 15px;
}
.dokan-seller-search-form .dokan-w4 input[type=search],
.dokan-seller-search-form .dokan-w4 select.dokan-form-control {
  height: 40px !important;
  border: 1px solid #e2e2e2 !important;
  box-shadow: none !important;
  border-radius: 4px !important;
}
.dokan-seller-search-form .dokan-w4 input[type=search] {
  background-position-y: 11px !important;
}
.dokan-seller-listing {
  position: relative;
}
.dokan-seller-listing .dokan-overlay {
  position: absolute;
  background: rgba(255, 255, 255, 0.3);
}
#dokan-seller-listing-wrap {
  margin-right: -15px;
  margin-left: -15px;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap {
  list-style: none;
  margin: 20px 0px;
  padding: 0;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap:before,
#dokan-seller-listing-wrap ul.dokan-seller-wrap:after {
  content: " ";
  display: table;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap:after {
  clear: both;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li {
  list-style-type: none;
  float: right;
  margin: 0 0 20px 0;
  padding-right: 15px;
  padding-left: 15px;
  margin-bottom: 20px;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li.coloum-2 {
  width: 50%;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li.coloum-3 {
  width: 33.33%;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-wrapper {
  box-shadow: 0px 0px 25px 0px #ddd;
}
@media (max-width: 767px) {
  #dokan-seller-listing-wrap ul.dokan-seller-wrap li {
    width: 100% !important;
    float: none;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  #dokan-seller-listing-wrap ul.dokan-seller-wrap li {
    width: 50% !important;
  }
}
@media (min-width: 992px) {
  #dokan-seller-listing-wrap ul.dokan-seller-wrap li {
    width: 33.33%;
  }
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-content {
  max-width: 100%;
}
@media (max-width: 767px) {
  #dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-content {
    text-align: right;
  }
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-content .store-info {
  height: 100%;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: 100% 50%;
  height: 220px;
  position: relative;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-content .store-info .store-data-container {
  height: 100%;
  background-color: rgba(0, 0, 0, 0.45);
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-content .store-info .featured-favourite {
  padding: 20px 20px 10px;
  overflow: hidden;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-content .store-info .featured-favourite .featured-label {
  float: right;
  padding: 2px 10px;
  background: #2d54a3;
  color: #fff;
  border-radius: 3px;
  box-shadow: 0px 0px 25px -5px #afafaf;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-content .store-info .featured-favourite .favourite-label {
  float: left;
  padding: 2px;
  background: #fff;
  width: 55px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0px 0px 25px -5px #afafaf;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-content .store-info .featured-favourite .favourite-label i.fa {
  color: #e74c3c;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-content .store-info .store-data {
  padding: 0px 20px;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-content .store-info .store-data h2 {
  margin: 9px 0px;
  padding: 0px;
  font-size: 20px;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-content .store-info .store-data h2 a {
  color: #FFF;
  text-shadow: rgba(0, 0, 0, 0.8) 0 1px 0;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-content .store-info .store-data h2 a:hover {
  color: #fafafa;
  text-shadow: rgba(0, 0, 0, 0.8) 0 1px 0;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-content .store-info .store-data p {
  margin-bottom: 2px;
  color: #FFF;
  text-shadow: rgba(0, 0, 0, 0.8) 0 1px 0;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-content .store-info .store-data .dokan-seller-rating {
  float: none !important;
  margin: 12px 0px 10px;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-content .store-info .store-data .dokan-seller-rating:before {
  color: #adb5b6;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-content .store-info .store-data .dokan-seller-rating span:before {
  color: #fa9a00;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-content .store-info .store-data p.store-address {
  line-height: 23px;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-footer {
  background: #f7fbfc;
  position: relative;
  padding: 15px 20px;
  border-top: 1px solid #eee;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-footer a {
  text-decoration: none;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-footer .seller-avatar {
  padding: 6px;
  background: #fff;
  position: absolute;
  width: 80px;
  height: 80px;
  top: -70px;
  left: 20px;
  border-radius: 40px;
  box-shadow: 0px 0px 30px -6px #afafaf;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-footer .seller-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 40px;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li.no-banner-img .store-content .store-info .store-data-container {
  height: 100%;
  background: none;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li.no-banner-img .store-content .store-info .featured-favourite .featured-label {
  background: #d1dbf0;
  color: #2d54a3;
  box-shadow: none;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li.no-banner-img .store-content .store-data h2 a {
  color: #526b6f;
  text-shadow: none;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li.no-banner-img .store-content .store-data h2 a:hover {
  color: #333;
  text-shadow: none;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li.no-banner-img .store-content .store-data p {
  color: #748082;
  text-shadow: none;
}
@media (min-width: 1200px) {
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery {
    background-color: rgba(0, 0, 0, 0.65);
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper {
    position: absolute;
    bottom: 0;
    left: 0;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper .store-social {
    padding-left: 15px;
    margin: 0;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper .store-social li {
    line-height: 1;
    margin-right: 5px;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper .store-social li a {
    display: inline-block;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper .store-social li a i {
    font-size: 25px;
    text-shadow: -1px 1px 1px rgba(255, 255, 255, 0.55);
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper .store-social li a i.fa-square-x-twitter {
    color: #000000;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-default img.profile-info-img {
    position: absolute;
    top: 0;
    right: 0;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-default .profile-info-summery-wrapper {
    position: relative;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-default .profile-info-summery-wrapper .profile-info-summery {
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 370px;
    padding: 25px 0 15px;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-default .profile-info-summery-wrapper .profile-info-summery .profile-info {
    padding: 5px 30px;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-default .profile-info-summery-wrapper .profile-info-summery .profile-info ul.dokan-store-info {
    margin-bottom: 10px;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper {
    top: 12px;
    left: 15px;
    bottom: auto;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 {
    position: relative;
    width: 100%;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper {
    position: relative;
    background-color: #fff;
    color: #444;
    border: 1px solid #EDEDED;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery {
    width: 100%;
    margin-top: -75px;
    padding-top: 0;
    background: none;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery .profile-info-head {
    position: relative;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery .profile-info-head .profile-img img {
    background: #fff;
    border: 1px solid #ddd;
    padding: 2px;
    width: 150px;
    height: 150px;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery .profile-info {
    position: relative;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-name {
    color: inherit;
    font-size: 35px;
    text-align: center;
    font-weight: bold;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info {
    padding: 0;
    width: 100%;
    margin: 0;
    display: block;
    text-align: center;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .store-open-close-notice {
    display: inline-block;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .store-open-close-notice:hover span.fa-angle-down:after {
    left: -2.5px;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li {
    text-align: right;
    display: inline-block;
    padding-bottom: 15px;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li a {
    color: inherit;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li i {
    display: none;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li:after {
    content: '·';
    color: #9c9c9c;
    font-weight: bold;
    font-size: 18px;
    position: relative;
    top: 3px;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li:last-child:after {
    content: '';
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper {
    position: relative;
    text-align: center;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper .store-social {
    position: relative;
    padding: 0;
    margin: 0;
    display: inline-block;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper .store-social li a i {
    text-shadow: none;
  }
}
@media (max-width: 767px) {
  .dokan-store-sidebar,
  .dokan-single-store {
    margin-left: 3%;
    width: 94% !important;
    margin-right: 3%;
  }
  .dokan-single-store .profile-frame.profile-frame-no-banner .profile-info-box.profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info-head {
    width: 150px;
  }
}
@media (max-width: 1199px) {
  .dokan-single-store .profile-frame .profile-info-box {
    position: relative;
    width: 100%;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper {
    position: relative;
    background-color: #fff;
    color: #444;
    border: 1px solid #EDEDED;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery {
    width: 100%;
    padding-top: 0;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info-head {
    position: relative;
    top: 0;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info-head .store-name {
    color: inherit;
    font-size: 35px;
    margin-bottom: 11px;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info-head .profile-img img {
    background: #fff;
    border: 1px solid #ddd;
    padding: 2px;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info-head .profile-img.profile-img-circle img {
    width: 100px;
    height: 100px;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info {
    position: relative;
    top: 0;
    padding: 0;
    width: 100%;
    margin: 0;
    display: block;
    overflow: visible;
    line-height: 1.4;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li {
    text-align: center;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li a {
    color: inherit;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li i {
    display: none;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li span.fa-angle-down {
    display: none;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li .dokan-times {
    margin-right: 5px;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li .store-open-close-notice {
    justify-content: center;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li .store-open-close-notice #vendor-store-times {
    right: calc(50% - 155px) !important;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper {
    position: relative;
    top: 0;
    text-align: center;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper .store-social {
    padding: 0;
    margin: 0;
    display: inline-block;
  }
  .dokan-single-store .profile-frame .profile-info-box .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper .store-social li {
    margin-right: 8px;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper {
    padding: 0 8px;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery {
    margin-top: 0px;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info-head {
    top: 0;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info-head .profile-img.profile-img-square {
    background: none;
    border: 0;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info-head .profile-img img {
    width: 100px;
    height: 100px;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-name {
    font-size: 20px;
    margin-bottom: 8px;
    color: #444;
    text-shadow: none;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info {
    top: 0;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li {
    text-align: right;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li i {
    display: inline-block;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .store-open-close-notice {
    justify-content: flex-start;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper {
    position: absolute;
    top: -35px;
    left: 20px;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper .store-social {
    top: 0;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper .store-social li {
    margin-right: 8px;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper .store-social li a i {
    font-size: 25px;
    text-shadow: -1px 1px 1px rgba(255, 255, 255, 0.55);
    color: #0B8379;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper .store-social li a i.fa-square-x-twitter {
    color: #000000;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery .profile-info {
    margin-top: 0;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-name {
    position: relative;
    top: 0px;
    text-align: center;
    font-size: 20px;
    margin-bottom: 10px;
    color: #444;
    text-shadow: none;
    font-weight: bold;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info {
    top: 0;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout2 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper {
    top: 0;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout3 .profile-info-summery-wrapper {
    height: auto;
    border: 0;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info-head {
    top: 0;
    margin-left: 15px;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info {
    padding: 0 15px 0 0;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info {
    top: 0;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li {
    float: none;
    text-align: right;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li i {
    display: inline-block;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info li:before {
    content: "";
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info .store-open-close-notice {
    justify-content: flex-start;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-social-wrapper {
    top: 0;
    text-align: right;
    padding: 0 4px;
  }
}
.dokan-withdraw-content .dokan-withdraw-area h1 {
  margin: 0px 0px 20px;
}
.dokan-withdraw-content .dokan-withdraw-area ul li {
  display: inline-block;
  padding: 0px 5px;
}
.dokan-withdraw-content .dokan-withdraw-area .dokan-panel-body .dokan-panel-inner-container {
  position: relative;
  border-bottom: 1px solid #EDEDED;
  padding: 15px 0;
}
.dokan-withdraw-content .dokan-withdraw-area .dokan-panel-body .dokan-panel-inner-container:last-child {
  border-bottom: none;
  margin-bottom: -15px;
}
.dokan-withdraw-content .dokan-withdraw-area .dokan-panel-body .dokan-panel-inner-container:first-child {
  margin-top: -15px;
}
.dokan-withdraw-content .dokan-withdraw-area .dokan-panel-body .dokan-panel-inner-container p {
  margin-bottom: 5px;
}
.dokan-withdraw-content .dokan-withdraw-area .dokan-panel-body .dokan-panel-inner-container .dokan-w8 .dokan-withdraw-method-logo {
  display: inline-block;
  vertical-align: middle;
  margin-left: 10px;
}
.dokan-withdraw-content .dokan-withdraw-area .dokan-panel-body .dokan-panel-inner-container .dokan-w8 strong a {
  color: var(--dokan-button-background-color, #7047EB);
}
.dokan-withdraw-content .dokan-withdraw-area .dokan-panel-body .dokan-panel-inner-container .dokan-w5 {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translate(-10px, -50%);
}
.dokan-withdraw-content .dokan-withdraw-area .dokan-panel-body .dokan-panel-inner-container .dokan-w5 button,
.dokan-withdraw-content .dokan-withdraw-area .dokan-panel-body .dokan-panel-inner-container .dokan-w5 a.dokan-btn {
  float: left;
}
.dokan-withdraw-content .dokan-withdraw-area .dokan-panel-body .dokan-panel-inner-container .dokan-w5 #dokan-withdraw-display-requests-button {
  float: left;
  text-decoration: none;
}
.dokan-withdraw-content .dokan-withdraw-area .dokan-withdraw-status-filter-container {
  margin-bottom: 15px;
}
.dokan-withdraw-popup h2 {
  font-size: 17px;
  padding: 15px 20px 10px;
  margin: 0px;
  border-bottom: 1px solid #eee;
}
.dokan-withdraw-popup .withdraw-schedule-select-container,
.dokan-withdraw-popup .dokan-form-horizontal .withdraw {
  padding: 15px 30px 0;
}
.dokan-withdraw-popup .withdraw {
  margin-top: 15px;
}
.dokan-withdraw-popup .footer {
  width: 100%;
  padding: 10px 20px;
  text-align: left;
  border-top: 1px solid #eee;
}
@media only screen and (max-width: 500px) {
  .dokan-withdraw-content .dokan-withdraw-area .entry-content .dokan-panel .dokan-panel-body .dokan-panel-inner-container {
    display: flex;
    flex-direction: column;
  }
  .dokan-withdraw-content .dokan-withdraw-area .entry-content .dokan-panel .dokan-panel-body .dokan-panel-inner-container .dokan-w5 {
    position: relative;
    right: 0;
    left: 100%;
    top: 100%;
    transform: none;
    margin-top: 20px;
  }
  .dokan-withdraw-content .dokan-withdraw-area .entry-content .dokan-withdraw-status-filter-container .dokan-add-product-link a {
    margin: 15px 0;
  }
  .dokan-withdraw-content .dokan-withdraw-area table.dokan-table-striped {
    overflow: auto;
    display: block;
  }
  .dokan-withdraw-content .dokan-withdraw-area table.dokan-table-striped tbody tr th {
    width: 150px;
  }
}
.dokan-login-form-popup-wrapper {
  width: 430px !important;
  padding: 16px !important;
  margin: 0 auto !important;
}
.dokan-login-form-popup-wrapper .dokan-login-form-title {
  border-bottom: 1px solid #ddd;
  padding-bottom: 15px;
  margin-bottom: 15px;
}
.dokan-login-form-popup-wrapper fieldset {
  padding: 0;
  border: 0;
  margin: 0;
}
.dokan-login-form-popup-wrapper .dokan-login-form-error {
  color: #F23030;
  background-color: #f2dede;
  font-size: 0.9em;
}
.dokan-login-form-popup-wrapper .dokan-login-form-error.has-error {
  padding: 5px 8px;
  margin-bottom: 5px;
}
/*
* template/global/seller-registration-form.php
*
*/
p.vendor-customer-registration .radio {
  display: inline-block;
}
p.vendor-customer-registration .radio:hover {
  cursor: pointer;
  color: #526b6f;
}
.woocommerce-form-register:has(.vendor-customer-registration) {
  height: fit-content !important;
}
.dokan-popup-content {
  padding: 18px;
}
.dokan-popup-content .dokan-popup-title {
  border-bottom: 1px solid #eeeeee;
  padding-bottom: 10px;
  margin-bottom: 15px;
  line-height: 1;
}
.dokan-popup-content fieldset {
  padding: 0;
  margin: 0;
  border: 0;
  background: none;
}
.dokan-popup-content .mfp-close {
  top: 4px;
  left: 3px;
}
.dokan-popup-content .dokan-popup-option-list {
  list-style-type: none;
  padding: 0;
  margin: 10px 0;
}
.dokan-popup-content .dokan-popup-option-list li {
  margin: 0 0 3px;
}
.dokan-popup-content .dokan-popup-option-list li label.dokan-popup-block-label {
  display: block;
  margin: 0;
  cursor: pointer;
}
.dokan-popup-content .dokan-popup-option-list li label.dokan-popup-block-label input {
  margin: 0 0 0 4px;
}
.dokan-popup-content .dokan-popup-error {
  display: none;
  color: #F23030;
}
.dokan-popup-content .dokan-popup-error.has-error {
  display: block;
}
.iziModal .iziModal-header .iziModal-noSubtitle .iziModal-header-title {
  font-size: revert;
}
.iziModal .iziModal-header-title {
  font-family: revert;
}
#dokan-seller-listing-wrap button {
  margin: unset;
  padding: 6px 12px;
}
#dokan-store-listing-filter-wrap .left,
.store-lists-other-filter-wrap .left,
#dokan-store-listing-filter-wrap .right,
.store-lists-other-filter-wrap .right,
#dokan-store-listing-filter-wrap .item,
.store-lists-other-filter-wrap .item {
  display: unset;
  overflow: unset;
  z-index: unset;
  position: unset;
  height: unset;
  width: unset;
  margin: unset;
  padding: unset;
  border: unset;
}
#dokan-store-listing-filter-wrap,
#dokan-store-listing-filter-wrap form,
#dokan-store-listing-filter-form-wrap * {
  padding: unset;
  margin: unset;
  box-sizing: border-box;
}
#dokan-store-listing-filter-wrap ul,
#dokan-store-listing-filter-wrap form ul,
#dokan-store-listing-filter-form-wrap * ul,
#dokan-store-listing-filter-wrap ul li,
#dokan-store-listing-filter-wrap form ul li,
#dokan-store-listing-filter-form-wrap * ul li {
  margin: unset;
  padding: unset;
}
#dokan-store-listing-filter-wrap div p,
#dokan-store-listing-filter-wrap form div p,
#dokan-store-listing-filter-form-wrap * div p {
  margin: unset;
  padding: unset;
  line-height: unset;
}
#dokan-store-listing-filter-wrap label,
#dokan-store-listing-filter-wrap form label,
#dokan-store-listing-filter-form-wrap * label,
#dokan-store-listing-filter-wrap input,
#dokan-store-listing-filter-wrap form input,
#dokan-store-listing-filter-form-wrap * input,
#dokan-store-listing-filter-wrap select,
#dokan-store-listing-filter-wrap form select,
#dokan-store-listing-filter-form-wrap * select,
#dokan-store-listing-filter-wrap button,
#dokan-store-listing-filter-wrap form button,
#dokan-store-listing-filter-form-wrap * button,
#dokan-store-listing-filter-wrap a,
#dokan-store-listing-filter-wrap form a,
#dokan-store-listing-filter-form-wrap * a,
#dokan-store-listing-filter-wrap i,
#dokan-store-listing-filter-wrap form i,
#dokan-store-listing-filter-form-wrap * i,
#dokan-store-listing-filter-wrap span,
#dokan-store-listing-filter-wrap form span,
#dokan-store-listing-filter-form-wrap * span,
#dokan-store-listing-filter-wrap textarea,
#dokan-store-listing-filter-wrap form textarea,
#dokan-store-listing-filter-form-wrap * textarea {
  width: unset;
  border: unset;
  padding: unset;
  margin: unset;
  display: unset;
  text-transform: unset;
  font-weight: unset;
  line-height: unset;
  font-size: unset;
  letter-spacing: unset;
  box-shadow: unset;
  box-sizing: border-box;
}
#dokan-store-listing-filter-wrap {
  background-color: #ffffff;
  padding: 20px;
  box-shadow: -1px 1px 20px 0px #E9E9E9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}
#dokan-store-listing-filter-wrap .right {
  display: flex;
  align-items: center;
}
#dokan-store-listing-filter-wrap .right .item {
  position: relative;
  white-space: nowrap;
}
#dokan-store-listing-filter-wrap .right .item.sort-by {
  margin: 0px 30px;
}
#dokan-store-listing-filter-wrap .right .item .dokan-store-list-filter-button {
  border-radius: 3px;
}
#dokan-store-listing-filter-wrap .right .item .dokan-icons {
  position: absolute;
  right: 14%;
  top: 50%;
  cursor: pointer;
  margin: 0;
  padding: 0;
  transform: translate(0, -50%);
  z-index: 999;
}
#dokan-store-listing-filter-wrap .right .item .dokan-icons .dokan-icon-div {
  background: #fff;
  height: 2px;
  margin: 2px auto;
}
#dokan-store-listing-filter-wrap .right .item .dokan-icons .dokan-icon-div:nth-child(1) {
  width: 15px;
}
#dokan-store-listing-filter-wrap .right .item .dokan-icons .dokan-icon-div:nth-child(2) {
  width: 10px;
}
#dokan-store-listing-filter-wrap .right .item .dokan-icons .dokan-icon-div:nth-child(3) {
  width: 4px;
}
#dokan-store-listing-filter-wrap .right .item button {
  padding-top: 8px;
  padding-bottom: 8px;
  padding-right: 35px;
  padding-left: 25px;
}
#dokan-store-listing-filter-wrap .right .item button:focus {
  outline: none;
}
#dokan-store-listing-filter-wrap .right .item select {
  background: #ffffff;
  border: 1px solid #edecec;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding: 8px 20px;
}
#dokan-store-listing-filter-wrap .right .item select:before {
  content: "\f140";
}
#dokan-store-listing-filter-wrap .right .item select:focus {
  outline: none;
}
#dokan-store-listing-filter-wrap .right .toggle-view {
  margin: 0;
  padding: 0;
  line-height: 0;
  height: 0;
}
#dokan-store-listing-filter-wrap .right .toggle-view .dashicons {
  font-size: 22px;
  cursor: pointer;
}
#dokan-store-listing-filter-wrap .right .toggle-view .active {
  color: #ee5035;
}
#dokan-store-listing-filter-form-wrap {
  background: #ffffff;
  margin-top: 32px;
  padding: 32px 20px 20px 20px;
  box-shadow: -1px 1px 20px 0px #E9E9E9;
  position: relative;
}
#dokan-store-listing-filter-form-wrap div {
  margin: 0;
  padding: 0;
}
#dokan-store-listing-filter-form-wrap:before {
  content: "\f142";
  height: 0 !important;
  top: -8px;
  right: 50%;
  position: absolute;
  transform: translate(0, -50%);
  font-family: dashicons;
  font-size: 60px;
  color: #fff;
  font-weight: bold;
  line-height: 0 !important;
}
#dokan-store-listing-filter-form-wrap .store-search .store-search-input {
  width: 100%;
  padding: 10px 20px;
  border-radius: 3px;
  border: 1px solid #cccccc;
}
#dokan-store-listing-filter-form-wrap .store-search .store-search-input:placeholder {
  color: #7e7e7ec9;
}
#dokan-store-listing-filter-form-wrap .store-search .store-search-input:focus {
  outline: none;
  border-color: #8080809e;
}
#dokan-store-listing-filter-form-wrap .apply-filter {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
#dokan-store-listing-filter-form-wrap .apply-filter #cancel-filter-btn {
  padding: 8px 25px;
  border-radius: 3px;
  background: #fff !important;
  color: #000 !important;
  border: 1px solid #edecec !important;
  margin-left: 20px;
  display: none;
}
#dokan-store-listing-filter-form-wrap .apply-filter #cancel-filter-btn:focus {
  outline: none;
}
#dokan-store-listing-filter-form-wrap .apply-filter #apply-filter-btn {
  padding: 8px 25px;
  border-radius: 3px;
}
#dokan-store-listing-filter-form-wrap .apply-filter #apply-filter-btn:focus {
  outline: none;
}
.site-content .entry-header .entry-title[class] {
  border: none;
}
#dokan-seller-listing-wrap .seller-listing-content .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-data h2 {
  padding: 0 !important;
}
#dokan-seller-listing-wrap .seller-listing-content .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-data .dokan-seller-rating i.dashicons {
  width: 15px;
  font-size: 17px;
}
#dokan-seller-listing-wrap .seller-listing-content .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-data .dokan-seller-rating i.dashicons.dashicons-star-filled,
#dokan-seller-listing-wrap .seller-listing-content .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-data .dokan-seller-rating i.dashicons.dashicons-star-half {
  color: #fa9a00;
}
#dokan-seller-listing-wrap.grid-view .store-wrapper {
  position: relative;
}
#dokan-seller-listing-wrap.grid-view .store-content {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.45);
  color: #fff;
}
#dokan-seller-listing-wrap.grid-view .store-content.default-store-banner {
  background: transparent;
}
#dokan-seller-listing-wrap.grid-view .store-content .store-data-container {
  padding: 5px 20px;
}
#dokan-seller-listing-wrap.grid-view .store-content .store-data-container .featured-favourite {
  position: relative;
  top: 10px;
}
#dokan-seller-listing-wrap.grid-view .store-content .store-data-container .featured-favourite .featured-label {
  width: max-content;
  width: -moz-max-content;
  width: -webkit-max-content;
  padding: 2px 10px;
  background: #2d54a3;
  color: #fff;
  border-radius: 3px;
  box-shadow: 0px 0px 25px -5px #afafaf;
}
#dokan-seller-listing-wrap.grid-view .store-content .store-data-container .store-data h2 a {
  color: #fff;
  text-shadow: rgba(0, 0, 0, 0.8) 0 1px 0;
  font-size: 25px;
  padding: 0;
  margin: 0;
  text-decoration: none;
}
#dokan-seller-listing-wrap.grid-view .store-content .store-data-container .store-data h2 a:active,
#dokan-seller-listing-wrap.grid-view .store-content .store-data-container .store-data h2 a:focus {
  text-decoration: none;
  outline: none;
}
#dokan-seller-listing-wrap.grid-view .store-content .store-data-container .store-data .dokan-seller-rating p.rating {
  display: none !important;
}
#dokan-seller-listing-wrap.grid-view .store-content .store-data-container .store-data .store-address {
  margin: 0 0 5px 0;
  line-height: 23px;
}
#dokan-seller-listing-wrap.grid-view .store-content .store-data-container .store-data .store-address br {
  display: none;
}
#dokan-seller-listing-wrap.grid-view .store-content .store-data-container .store-data .store-phone {
  margin: 0 0 5px 0;
}
#dokan-seller-listing-wrap.grid-view .store-footer {
  background: #fff;
  position: relative;
}
#dokan-seller-listing-wrap.grid-view .store-footer a:active,
#dokan-seller-listing-wrap.grid-view .store-footer a:focus {
  outline: none;
  text-decoration: none;
}
#dokan-seller-listing-wrap.grid-view .store-footer .dokan-btn-theme.dokan-btn-round {
  margin-left: 10px;
}
#dokan-seller-listing-wrap .seller-listing-content .dokan-error {
  margin: 15px;
}
#dokan-seller-listing-wrap .store_open_is_on {
  margin-top: 35px;
}
#dokan-seller-listing-wrap .dokan-store-is-open-close-status {
  padding: 0px 10px;
  display: block;
  border-radius: 30px;
  position: absolute;
  left: 15px;
  top: 15px;
  font-size: 14px;
  box-shadow: 0px 0px 25px -5px #afafaf;
}
#dokan-seller-listing-wrap .dokan-store-is-open-status {
  background-color: #1dbf73;
}
#dokan-seller-listing-wrap .dokan-store-is-closed-status {
  background-color: #999;
}
#dokan-seller-listing-wrap .dokan-single-seller .store-wrapper .store-header .store-banner {
  min-height: 220px;
  position: relative;
}
#dokan-seller-listing-wrap .dokan-single-seller .store-wrapper .store-header .store-banner img {
  position: absolute;
  right: 0;
  top: 0;
  max-width: 100%;
  height: 100%;
  width: -moz-available;
  width: -webkit-fill-available;
  width: fill-available;
  object-fit: cover;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller {
  width: 100%;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  background: #fff;
  border-radius: 3px;
  position: relative;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-header .store-banner {
  min-height: 140px;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-header .store-banner img {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-content .store-data-container .featured-favourite .featured-label {
  padding: 2px 10px;
  background: #2d54a3;
  color: #fff;
  border-radius: 3px;
  box-shadow: 0px 0px 25px -5px #afafaf;
  position: absolute;
  right: 8px;
  top: 10px;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-content .store-data-container .store-data h2 {
  margin-top: 0;
  margin-bottom: 15px;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-content .store-data-container .store-data h2 a {
  color: #000;
  text-decoration: none;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-content .store-data-container .store-data h2 a:active,
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-content .store-data-container .store-data h2 a:focus {
  text-decoration: none;
  outline: none;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-content .store-data-container .store-data .dokan-seller-rating[class] {
  overflow: visible;
  z-index: 1;
  min-width: 85px;
  font-family: 'Open Sans', sans-serif;
  color: gray;
  float: left;
  position: relative;
  top: -15px;
  right: 25%;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-content .store-data-container .store-data .dokan-seller-rating[class]:before {
  font-family: star;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-content .store-data-container .store-data .dokan-seller-rating[class] span:before {
  font-family: star;
  color: #fa9a00;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-content .store-data-container .store-data .store-address {
  margin-top: 5px;
  display: inline;
  font-size: 15px;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-content .store-data-container .store-data .store-address:before {
  content: "\f082";
  font-family: dashicons;
  color: #0bb90b;
  font-size: 22px;
  position: relative;
  top: 5px;
  right: -5px;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-content .store-data-container .store-data .store-address br {
  display: none;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-content .store-data-container .store-data .store-phone {
  display: none;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper > .store-content {
  flex-basis: 43%;
  padding-right: 4%;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-header {
  flex-basis: 20%;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .dokan-store-is-open-close-status {
  color: #fff;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-footer[class] {
  display: flex;
  flex-direction: row-reverse;
  flex-basis: 33%;
  border: none;
  text-align: left;
  background: transparent;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-footer[class] a:active,
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-footer[class] a:focus {
  outline: none;
  text-decoration: none;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-footer[class] .seller-avatar {
  display: none;
}
#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-footer[class] button {
  margin-left: 20px;
}
.woocommerce-MyAccount-content ul.dokan-account-migration-lists {
  margin: 0;
  padding: 0;
  list-style: none;
  border: 1px solid #eee;
}
.woocommerce-MyAccount-content ul.dokan-account-migration-lists li {
  padding: 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-bottom: 1px solid #eee;
}
.woocommerce-MyAccount-content ul.dokan-account-migration-lists li:last-child {
  border-bottom: none;
}
.woocommerce-MyAccount-content ul.dokan-account-migration-lists li .left-content {
  text-align: right;
  flex: 2;
}
.woocommerce-MyAccount-content ul.dokan-account-migration-lists li .left-content p {
  margin: 0px;
}
.woocommerce-MyAccount-content ul.dokan-account-migration-lists li .right-content {
  text-align: left;
  flex: 1;
}
.store-cat-stack-dokan.cat-drop-stack ul {
  max-height: 800px;
  overflow-y: scroll;
}
/*--------------------------------------------------
:: Responsive Styles
-------------------------------------------------- */
@media (max-width: 1366px) {
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info {
    max-width: 650px;
  }
}
@media (max-width: 1199px) {
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info {
    max-width: 590px;
  }
  .dokan-dashboard .dokan-dashboard-content .dokan-table {
    display: block;
    overflow: scroll;
  }
}
@media (max-width: 992px) {
  #dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper {
    padding: 0px 0px 0px 10px;
  }
  #dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-header .store-banner {
    min-height: 120px;
  }
  #dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-content .store-data-container .store-data h2 {
    font-size: 20px;
    margin-bottom: 5px;
  }
  #dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-content .store-data-container .store-data .store-address {
    font-size: 14px !important;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info {
    max-width: 430px;
  }
}
@media (max-width: 767px) {
  #dokan-store-listing-filter-wrap {
    display: block;
  }
  #dokan-store-listing-filter-wrap .right {
    justify-content: space-between;
    margin-top: 15px;
  }
  #dokan-store-listing-filter-wrap .right .item.sort-by {
    margin: 0;
  }
  #dokan-store-listing-filter-wrap .toggle-view {
    display: none;
  }
  .dokan-table {
    border: none;
    text-align: right;
    width: 100%;
    max-width: 100%;
  }
  .dokan-table tbody {
    display: table-row-group;
    vertical-align: middle;
    border-color: inherit;
  }
  .dokan-table tbody tr {
    vertical-align: inherit;
    width: 100%;
  }
  .dokan-table tbody tr td {
    padding: 6px;
    text-align: right;
    border-color: #ededed;
    border-style: solid;
    border-width: 0px 1px 1px 1px;
    background: #fff;
  }
  .dokan-table tbody tr td:before {
    padding-left: 20px !important;
    float: right;
    font-weight: bold;
  }
  .dokan-table tbody tr .post-date .status {
    float: left;
  }
  .dokan-table tbody tr .diviader {
    background: transparent !important;
    border: none !important;
    height: 15px;
    background: none;
  }
  .dokan-table tbody tr .diviader:before {
    content: ' ';
  }
  .dokan-table tbody tr td:first-child {
    border-top-width: 1px;
  }
  .dokan-table tbody tr .dokan-order-action {
    width: 100%;
  }
  .dokan-table tbody .row-actions {
    visibility: visible !important;
  }
  .dokan-orders-area .dokan-w8 {
    width: 100% !important;
  }
  .dokan-orders-area .dokan-w4 {
    width: 100% !important;
  }
  .dokan-primary {
    width: 100% !important;
    padding-right: 15px;
    padding-left: 15px;
  }
  .dokan-primary li.product {
    width: 100% !important;
  }
  .dokan-secondary {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
  }
  .dokan-dashboard .dokan-dash-sidebar,
  .dokan-dashboard .dokan-dashboard-content {
    float: none;
    width: 100%;
  }
  .dokan-dashboard .dokan-dash-sidebar ul.dokan_tabs,
  .dokan-dashboard .dokan-dashboard-content ul.dokan_tabs {
    border: 0;
    gap: 0.5rem;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
  }
  .dokan-dashboard .dokan-dash-sidebar ul.dokan_tabs li,
  .dokan-dashboard .dokan-dashboard-content ul.dokan_tabs li {
    margin: auto 0 !important;
    border-bottom: 1px solid #EDEDED;
  }
  .dokan-dashboard .dokan-dash-sidebar ul.dokan_tabs li.active a,
  .dokan-dashboard .dokan-dashboard-content ul.dokan_tabs li.active a {
    border-bottom: 1px solid #EDEDED;
  }
  .dokan-dashboard .product-edit-new-container .dokan-edit-row .dokan-side-left,
  .dokan-dashboard .product-edit-new-container .dokan-edit-row .dokan-side-right {
    float: none;
    width: auto;
  }
  .dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li {
    display: inline-block;
    border-left: 1px solid #454545;
  }
  .dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li.active:after {
    content: '';
    display: none;
  }
  .dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li a {
    padding: 12px 24px;
  }
  .dokan-dashboard .dokan-dashboard-content {
    padding-right: 0;
  }
  ul.dokan-seller-wrap {
    text-align: center;
  }
  ul.dokan-seller-wrap li.dokan-single-seller {
    width: 90% !important;
    margin-bottom: 50px;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info {
    max-width: 500px;
  }
}
@media (max-width: 480px) {
  .dokan-single-store .dokan-store-tabs ul.dokan-modules-button li {
    display: inline-block;
    margin: 0 0 3px 5px !important;
  }
  .dokan-single-store .dokan-store-tabs ul.dokan-modules-button li:last-child {
    margin-top: 0 !important;
  }
  .dokan-single-store .dokan-store-tabs ul.dokan-modules-button li button {
    font-size: 12px;
    margin: 0 !important;
    top: 0 !important;
  }
  .dokan-single-store .dokan-store-tabs ul.dokan-list-inline {
    flex-wrap: wrap;
  }
  .dokan-single-store .dokan-store-tabs ul.dokan-list-inline li {
    margin-left: unset;
    border-bottom: 1px solid #ededed;
  }
  .dokan-store-products-filter-area .dokan-store-products-ordeby {
    display: flex;
    flex-wrap: wrap;
  }
  .dokan-store-products-filter-area .dokan-store-products-ordeby input.product-name-search {
    width: 70%;
  }
  .dokan-store-products-filter-area .dokan-store-products-ordeby input.search-store-products {
    width: 30%;
  }
  .dokan-store-products-filter-area select.orderby.orderby-search {
    flex-basis: 100%;
    margin-top: 5px;
  }
}
@media (max-width: 430px) {
  #dokan-store-listing-filter-form-wrap:before {
    right: 10%;
  }
  .apply-filter {
    margin-top: 20px;
  }
  .apply-filter #cancel-filter-btn {
    display: block !important;
  }
  .dokan-dashboard .dokan-dash-sidebar,
  .dokan-dashboard .dokan-dashboard-content {
    float: none;
    width: 100%;
    padding: 20px 0;
  }
  .dokan-dashboard .dokan-dash-sidebar .dokan-table,
  .dokan-dashboard .dokan-dashboard-content .dokan-table {
    display: table;
  }
  .dokan-dashboard .dokan-dash-sidebar article,
  .dokan-dashboard .dokan-dashboard-content article {
    overflow: scroll;
  }
  .dokan-dashboard .dokan-dash-sidebar .dokan-settings-area .dokan-banner,
  .dokan-dashboard .dokan-dashboard-content .dokan-settings-area .dokan-banner {
    width: auto;
    height: auto;
    margin-bottom: 15px;
    min-height: unset;
  }
  .dokan-dashboard .dashboard-content-area .dokan-announcement-widget .dokan-right {
    float: left !important;
  }
  .dokan-dashboard .dashboard-content-area .dokan-announcement-widget .dokan-left {
    float: right !important;
  }
  .dokan-dashboard .dashboard-content-area .dokan-dash-left {
    padding-left: 0 !important;
  }
  .dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu:before,
  .dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu:after {
    content: " ";
    display: table;
  }
  .dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu:after {
    clear: both;
  }
  .dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li {
    float: right;
    width: 100%;
  }
  .dokan-dashboard .dokan-dash-sidebar ul.dokan-dashboard-menu li a {
    padding: 12px 18px;
  }
  .dokan-dashboard .dokan-orders-area .dokan-order-filter-serach {
    padding: 10px 0 10px 0;
  }
  .dokan-dashboard .dokan-orders-area .dokan-order-filter-serach .dokan-btn {
    padding: 6px 13px;
  }
  .dokan-dashboard .dokan-product-listing .dokan-product-listing-area .product-listing-top {
    border-bottom: 0;
  }
  .dokan-dashboard .dokan-product-listing .dokan-product-listing-area .product-listing-top ul.dokan-listing-filter {
    width: 100%;
  }
  .dokan-dashboard .dokan-product-listing .dokan-product-listing-area .product-listing-top .dokan-add-product-link {
    display: flex;
    justify-content: space-between;
    width: auto;
    float: none;
  }
  .dokan-dashboard .dokan-product-listing .dokan-product-listing-area .dokan-product-date-filter {
    display: flex;
    flex-flow: column wrap;
    justify-content: space-between;
  }
  .dokan-dashboard .dokan-product-listing .dokan-product-listing-area .dokan-product-date-filter .dokan-form-group {
    margin-bottom: 10px;
  }
  .dokan-dashboard .dokan-product-listing .dokan-product-listing-area .dokan-product-search-form {
    display: flex;
    justify-content: space-between;
    flex-flow: row-reverse nowrap;
  }
  .dokan-dashboard .dokan-product-listing .dokan-product-listing-area .dokan-product-search-form .dokan-form-group {
    margin-bottom: 0;
    margin-left: 0;
    width: 73%;
  }
  .dokan-dashboard .dokan-product-listing .dokan-product-listing-area .dokan-product-search-form .dokan-btn {
    width: 25%;
  }
  .dokan-dashboard .dokan-product-listing .dokan-product-listing-area #dokan-bulk-action-selector {
    width: 73%;
    margin-left: 2%;
  }
  .dokan-dashboard .dokan-product-listing .dokan-product-listing-area #dokan-bulk-action-submit {
    width: 25%;
    margin-left: 0;
  }
  .dokan-dashboard .dokan-product-listing .dokan-product-listing-area #product-filter .dokan-btn {
    width: 100%;
  }
  .dokan-dashboard .dokan-right,
  .dokan-dashboard .dokan-left {
    float: none !important;
  }
  .dokan-dashboard .dokan-w6,
  .dokan-dashboard .dokan-w8,
  .dokan-dashboard .dokan-w4 {
    float: none;
    width: 100%;
  }
  .dokan-dashboard .dokan-reports-content .dokan-reports-area .dokan-reports-wrap .report-left,
  .dokan-dashboard .dokan-reports-content .dokan-reports-area .dokan-reports-wrap .report-right {
    width: 100%;
  }
  .dokan-dashboard .dokan-product-edit-area .dokan-product-edit-left,
  .dokan-dashboard .dokan-product-edit-area .dokan-product-edit-right {
    float: none;
    width: 100%;
  }
  .dokan-dashboard .content-half-part {
    float: none;
    width: 100%;
  }
  .dokan-dashboard .content-half-part {
    margin-bottom: 8px;
  }
  .dokan-dashboard .content-half-part:last-child {
    padding-right: 0px;
  }
  .dokan-dashboard .content-half-part:first-child {
    padding-left: 0px;
  }
  .dokan-dashboard ul.dokan_tabs {
    padding-right: 0;
    margin-left: 0;
  }
  .dokan-dashboard ul.dokan_tabs li {
    margin-bottom: 10px;
  }
  .dokan-single-store .profile-frame .profile-info-box {
    width: 100%;
  }
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info {
    max-width: 280px;
  }
  .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-footer {
    display: flex;
  }
}
@media (max-width: 375px) {
  .dokan-order-filter-serach form:first-child .dokan-form-group {
    display: flex;
  }
  #dokan-store-listing-filter-wrap .right .item #stores_orderby {
    padding: 8px 16px 8px 0px;
  }
}
@media (max-width: 360px) {
  .dokan-single-store .profile-frame .profile-info-box.profile-layout-layout1 .profile-info-summery-wrapper .profile-info-summery .profile-info .dokan-store-info {
    max-width: 200px;
  }
}

