<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="1240" height="822" fill="none" xmlns:v="https://vecta.io/nano"><style><![CDATA[.B{fill:#fff}.C{color-interpolation-filters:sRGB}.D{flood-opacity:0}.E{fill-opacity:.4}.F{fill:#15c3c3}.G{fill-rule:evenodd}.H{fill-opacity:.566}]]></style><g clip-path="url(#P)"><path d="M0 0h1240v822H0z" class="B"/><path fill="url(#E)" d="M0 0h1240v822H0z"/><path opacity=".27" d="M118.762 546.714c72.294 190.294-73.538 211.533-155.491 198.367L-124-45.121l642.199-70.926c286.907 376-70.221 507.332-193.281 471.742S28.395 308.848 118.762 546.714z" class="B E"/><g opacity=".27" class="B E"><circle opacity=".78" cx="899" cy="812" r="143"/><circle opacity=".78" cx="1185" cy="812" r="87.389"/></g><path d="M605.6 310.465c0 18.831 15.283 34.135 34.135 34.135H759.2v221.896c0 18.841-15.299 34.14-34.135 34.14l-238.93-.041c-18.852 0-34.135-15.258-34.135-34.13v-341.35C452 206.263 467.283 191 486.135 191H605.6v119.465zm34.134 0V191l119.465 119.465H639.734z" fill="url(#F)" class="G"/><g stroke-width="7" stroke-linecap="round"><path stroke="url(#G)" d="M575.5 401.5h110"/><path stroke="url(#H)" d="M575.5 447.5h110"/><path stroke="url(#I)" d="M575.5 493.5h110"/></g><circle cx="540" cy="403" r="17" class="B"/><g filter="url(#A)" class="F"><use xlink:href="#Q"/></g><circle cx="540" cy="449" r="17" class="B"/><g filter="url(#B)" class="F"><path d="M547.982 444.452v-.038c-.017-.038-.017-.076-.035-.114v-.019a.41.41 0 0 0-.071-.095l-.017-.019c-.018-.019-.053-.038-.071-.057l-.018-.019h-.017l-.018-.019-7.505-4.015c-.142-.076-.318-.076-.477 0l-2.526 1.351 7.541 4.167.018.019c.017 0 .017.019.035.019.018.019.018.038.035.057v.038.019 4.414c0 .076-.035.133-.088.171l-1.519.857c-.088.057-.194.019-.247-.076-.018-.019-.018-.058-.018-.096v-4.319l-7.629-4.243-.017-.019-3.056 1.637-.017.019h-.018l-.017.019c-.018.019-.053.038-.071.057l-.018.019c-.035.038-.053.076-.088.114v.019c-.018.038-.035.076-.035.114v.038c0 .038-.018.057-.018.095v.019 8.867c0 .209.106.418.3.514l7.435 3.995c.106.057.23.076.353.038l.036-.019c.035 0 .053-.019.088-.038l7.488-3.995a.58.58 0 0 0 .3-.514v-8.848-.019c-.018-.038-.018-.057-.018-.095z"/></g><circle cx="540" cy="495" r="17" class="B"/><g filter="url(#C)" class="F"><path d="M547.982 490.452v-.038c-.017-.038-.017-.076-.035-.114v-.019a.41.41 0 0 0-.071-.095l-.017-.019c-.018-.019-.053-.038-.071-.057l-.018-.019h-.017l-.018-.019-7.505-4.015c-.142-.076-.318-.076-.477 0l-2.526 1.351 7.541 4.167.018.019c.017 0 .017.019.035.019.018.019.018.038.035.057v.038.019 4.414c0 .076-.035.133-.088.171l-1.519.857c-.088.057-.194.019-.247-.076-.018-.019-.018-.058-.018-.096v-4.319l-7.629-4.243-.017-.019-3.056 1.637-.017.019h-.018l-.017.019c-.018.019-.053.038-.071.057l-.018.019c-.035.038-.053.076-.088.114v.019c-.018.038-.035.076-.035.114v.038c0 .038-.018.057-.018.095v.019 8.867c0 .209.106.418.3.514l7.435 3.995c.106.057.23.076.353.038l.036-.019c.035 0 .053-.019.088-.038l7.488-3.995a.58.58 0 0 0 .3-.514v-8.848-.019c-.018-.038-.018-.057-.018-.095z"/></g><g filter="url(#D)"><path d="M848 440c0 44.736-36.264 81-81 81s-81-36.264-81-81 36.264-81 81-81 81 36.264 81 81z" fill="url(#J)"/></g><path d="M766.359 407L729.5 420.79l36.859 13.79 13.796-5.161-13.796-5.672v-.013l9.212-3.915v-.017l13.796 6.162 13.85-5.174L766.359 407zM729.5 466.743l34.554 12.924v-41.362L729.5 425.379v41.364zm9.214-3.3v-4.906l9.215 3.451v4.899l-9.215-3.444zm30.038 16.224l34.554-12.924v-41.364l-34.554 12.926v41.362zm25.34-21.13v4.906l-9.215 3.444v-4.899l9.215-3.451z" class="B G"/><circle cx="629" cy="128" transform="rotate(180 629 128)" fill="url(#K)" r="8"/><g class="B H"><circle cx="848" cy="345" transform="rotate(180 848 345)" r="14"/><circle cx="360" cy="507" transform="rotate(180 360 507)" r="14"/></g><path d="M638 671.188L615 676l4.452-22L638 671.188z" fill="url(#L)"/><path opacity=".994" d="M404.785 315.341l16.694-16.339 8.892 23.379-25.586-7.04z" fill="url(#M)"/><g fill="#cbdff1"><path opacity=".4" d="M1121.83 678.766c1.32.206 1.67 2.503-.14 2.598h-35.85c-2.67-.143-1.3-2.543 0-2.606h35.85c.04.008.09.008.14.008zM873.592 504.352c1.322.206 1.671 2.503-.137 2.598h-35.853c-2.663-.143-1.297-2.543 0-2.606h35.853c.044.008.093.008.137.008z"/></g><path d="M958.15 452.583c-5.846 0-10.583 4.737-10.583 10.584 0 5.83 4.737 10.575 10.583 10.575 5.831 0 10.576-4.737 10.576-10.575h10.583a21.16 21.16 0 0 1-21.151 21.15c-11.676 0-21.158-9.474-21.158-21.15S946.474 442 958.158 442v10.583h-.008z" fill="url(#N)" fill-opacity=".6"/><path d="M233.615 420.06c5.063 2.923 11.534 1.189 14.457-3.874a10.59 10.59 0 0 0-3.877-14.45c-5.05-2.915-11.527-1.185-14.447 3.871l-9.165-5.291c5.838-10.112 18.78-13.58 28.892-7.742s13.587 18.784 7.749 28.896-18.788 13.594-28.907 7.752l5.292-9.166.006.004z" fill="url(#O)" fill-opacity=".6"/><path d="M337.104 273v-8.029c-5.56-.255-10.949-1.776-14.104-3.636l2.492-9.894c3.487 1.941 8.377 3.719 13.773 3.719 4.728 0 7.963-1.859 7.963-5.24 0-3.216-2.654-5.248-8.795-7.362-8.875-3.043-14.936-7.27-14.936-15.472 0-7.443 5.147-13.275 14.024-15.051V204h8.125v7.443c5.56.254 9.295 1.44 12.031 2.789l-2.405 9.556c-2.156-.929-5.972-2.878-11.95-2.878-5.391 0-7.134 2.368-7.134 4.738 0 2.789 2.904 4.565 9.957 7.27 9.87 3.554 13.855 8.2 13.855 15.816 0 7.526-5.229 13.951-14.767 15.645V273h-8.129z" fill="#36ffd6"/><path d="M783.104 238v-8.029c-5.56-.255-10.949-1.776-14.104-3.636l2.492-9.894c3.487 1.941 8.377 3.719 13.773 3.719 4.728 0 7.963-1.859 7.963-5.24 0-3.216-2.654-5.248-8.795-7.362-8.875-3.043-14.936-7.27-14.936-15.472 0-7.443 5.147-13.275 14.024-15.051V169h8.125v7.443c5.56.254 9.294 1.44 12.031 2.789l-2.405 9.557c-2.156-.93-5.972-2.879-11.95-2.879-5.391 0-7.134 2.368-7.134 4.738 0 2.789 2.904 4.565 9.957 7.27 9.87 3.554 13.855 8.2 13.855 15.816 0 7.526-5.229 13.951-14.767 15.645V238h-8.129z" fill="#27ffd9"/><path d="M525.104 702v-8.029c-5.56-.255-10.949-1.776-14.104-3.636l2.492-9.894c3.487 1.941 8.377 3.719 13.773 3.719 4.728 0 7.963-1.859 7.963-5.24 0-3.216-2.654-5.248-8.795-7.362-8.875-3.043-14.936-7.27-14.936-15.472 0-7.443 5.147-13.275 14.024-15.051V633h8.125v7.443c5.56.254 9.294 1.44 12.031 2.789l-2.405 9.557c-2.156-.93-5.972-2.878-11.95-2.878-5.391 0-7.134 2.367-7.134 4.737 0 2.789 2.904 4.565 9.957 7.27 9.87 3.554 13.855 8.2 13.855 15.816 0 7.526-5.229 13.951-14.767 15.645V702h-8.129z" fill="#069cbe"/></g><defs><filter id="A" x="528" y="394" width="24" height="26" filterUnits="userSpaceOnUse" class="C"><feFlood result="A" class="D"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="B"/><feOffset dy="4"/><feGaussianBlur stdDeviation="2"/><feComposite in2="B" operator="out"/><feColorMatrix values="0 0 0 0 0.0823529 0 0 0 0 0.764706 0 0 0 0 0.764706 0 0 0 0.26 0"/><feBlend in2="A"/><feBlend in="SourceGraphic"/></filter><filter id="B" x="528" y="440" width="24" height="26" filterUnits="userSpaceOnUse" class="C"><feFlood result="A" class="D"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="B"/><feOffset dy="4"/><feGaussianBlur stdDeviation="2"/><feComposite in2="B" operator="out"/><feColorMatrix values="0 0 0 0 0.0823529 0 0 0 0 0.764706 0 0 0 0 0.764706 0 0 0 0.26 0"/><feBlend in2="A"/><feBlend in="SourceGraphic"/></filter><filter id="C" x="528" y="486" width="24" height="26" filterUnits="userSpaceOnUse" class="C"><feFlood result="A" class="D"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="B"/><feOffset dy="4"/><feGaussianBlur stdDeviation="2"/><feComposite in2="B" operator="out"/><feColorMatrix values="0 0 0 0 0.0823529 0 0 0 0 0.764706 0 0 0 0 0.764706 0 0 0 0.26 0"/><feBlend in2="A"/><feBlend in="SourceGraphic"/></filter><filter id="D" x="584" y="245" width="342" height="342" filterUnits="userSpaceOnUse" class="C"><feFlood result="A" class="D"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="B"/><feOffset dx="-12" dy="-24"/><feGaussianBlur stdDeviation="45"/><feComposite in2="B" operator="out"/><feColorMatrix values="0 0 0 0 0.0132465 0 0 0 0 0.454167 0 0 0 0 0.449268 0 0 0 0.69 0"/><feBlend in2="A"/><feBlend in="SourceGraphic"/></filter><linearGradient id="E" x1="716" y1="-115" x2="799.531" y2="814.493" xlink:href="#R"><stop stop-color="#29f0a1"/><stop offset="1" stop-color="#0ab2e2"/></linearGradient><linearGradient id="F" x1="605.6" y1="191" x2="605.6" y2="600.636" xlink:href="#R"><stop stop-color="#fff"/><stop offset="1" stop-color="#76ecff"/></linearGradient><linearGradient id="G" x1="558.5" y1="405" x2="749.5" y2="405" xlink:href="#R"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="H" x1="558.5" y1="451" x2="749.5" y2="451" xlink:href="#R"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="I" x1="558.5" y1="497" x2="749.5" y2="497" xlink:href="#R"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="J" x1="702.76" y1="332.55" x2="702.76" y2="461.031" xlink:href="#R"><stop stop-color="#f4ad00"/><stop offset="1" stop-color="#ed7272"/></linearGradient><linearGradient id="K" x1="629" y1="120" x2="629" y2="136" xlink:href="#R"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="L" x1="638" y1="665" x2="615" y2="665" xlink:href="#R"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="M" x1="395.302" y1="291.885" x2="429.402" y2="305.624" xlink:href="#R"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="N" x1="958.154" y1="442" x2="958.154" y2="484.317" xlink:href="#R"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><linearGradient id="O" x1="228.32" y1="429.224" x2="249.479" y2="392.576" xlink:href="#R"><stop stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></linearGradient><clipPath id="P"><path d="M0 0h1240v822H0z" class="B"/></clipPath><path id="Q" d="M547.982 398.452v-.038c-.017-.038-.017-.076-.035-.114v-.019a.41.41 0 0 0-.071-.095l-.017-.019c-.018-.019-.053-.038-.071-.057l-.018-.019h-.017l-.018-.019-7.505-4.015c-.142-.076-.318-.076-.477 0l-2.526 1.351 7.541 4.167.018.019c.017 0 .017.019.035.019.018.019.018.038.035.057v.038.019 4.414c0 .076-.035.133-.088.171l-1.519.857c-.088.057-.194.019-.247-.076-.018-.019-.018-.058-.018-.096v-4.319l-7.629-4.243-.017-.019-3.056 1.637-.017.019h-.018l-.017.019c-.018.019-.053.038-.071.057l-.018.019c-.035.038-.053.076-.088.114v.019c-.018.038-.035.076-.035.114v.038c0 .038-.018.057-.018.095v.019 8.867c0 .209.106.418.3.514l7.435 3.995c.106.057.23.076.353.038l.036-.019c.035 0 .053-.019.088-.038l7.488-3.995a.58.58 0 0 0 .3-.514v-8.848-.019c-.018-.038-.018-.057-.018-.095z"/><linearGradient id="R" gradientUnits="userSpaceOnUse"/></defs></svg>