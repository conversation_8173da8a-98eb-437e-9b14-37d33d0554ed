*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*:where(.dokan-layout,.dokan-layout *),
:where(.dokan-layout,.dokan-layout *)::before,
:where(.dokan-layout,.dokan-layout *)::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

:where(.dokan-layout,.dokan-layout *)::before,
:where(.dokan-layout,.dokan-layout *)::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

.dokan-layout {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

.dokan-layout {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr:where(.dokan-layout,.dokan-layout *) {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]):where(.dokan-layout,.dokan-layout *) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1:where(.dokan-layout,.dokan-layout *),
h2:where(.dokan-layout,.dokan-layout *),
h3:where(.dokan-layout,.dokan-layout *),
h4:where(.dokan-layout,.dokan-layout *),
h5:where(.dokan-layout,.dokan-layout *),
h6:where(.dokan-layout,.dokan-layout *) {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a:where(.dokan-layout,.dokan-layout *) {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b:where(.dokan-layout,.dokan-layout *),
strong:where(.dokan-layout,.dokan-layout *) {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code:where(.dokan-layout,.dokan-layout *),
kbd:where(.dokan-layout,.dokan-layout *),
samp:where(.dokan-layout,.dokan-layout *),
pre:where(.dokan-layout,.dokan-layout *) {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small:where(.dokan-layout,.dokan-layout *) {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub:where(.dokan-layout,.dokan-layout *),
sup:where(.dokan-layout,.dokan-layout *) {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub:where(.dokan-layout,.dokan-layout *) {
  bottom: -0.25em;
}

sup:where(.dokan-layout,.dokan-layout *) {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table:where(.dokan-layout,.dokan-layout *) {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button:where(.dokan-layout,.dokan-layout *),
input:where(.dokan-layout,.dokan-layout *),
optgroup:where(.dokan-layout,.dokan-layout *),
select:where(.dokan-layout,.dokan-layout *),
textarea:where(.dokan-layout,.dokan-layout *) {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button:where(.dokan-layout,.dokan-layout *),
select:where(.dokan-layout,.dokan-layout *) {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button:where(.dokan-layout,.dokan-layout *),
input:where([type='button']):where(.dokan-layout,.dokan-layout *),
input:where([type='reset']):where(.dokan-layout,.dokan-layout *),
input:where([type='submit']):where(.dokan-layout,.dokan-layout *) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring:where(.dokan-layout,.dokan-layout *) {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid:where(.dokan-layout,.dokan-layout *) {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress:where(.dokan-layout,.dokan-layout *) {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

:where(.dokan-layout,.dokan-layout *) ::-webkit-inner-spin-button,
:where(.dokan-layout,.dokan-layout *) ::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search']:where(.dokan-layout,.dokan-layout *) {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

:where(.dokan-layout,.dokan-layout *) ::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

:where(.dokan-layout,.dokan-layout *) ::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary:where(.dokan-layout,.dokan-layout *) {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote:where(.dokan-layout,.dokan-layout *),
dl:where(.dokan-layout,.dokan-layout *),
dd:where(.dokan-layout,.dokan-layout *),
h1:where(.dokan-layout,.dokan-layout *),
h2:where(.dokan-layout,.dokan-layout *),
h3:where(.dokan-layout,.dokan-layout *),
h4:where(.dokan-layout,.dokan-layout *),
h5:where(.dokan-layout,.dokan-layout *),
h6:where(.dokan-layout,.dokan-layout *),
hr:where(.dokan-layout,.dokan-layout *),
figure:where(.dokan-layout,.dokan-layout *),
p:where(.dokan-layout,.dokan-layout *),
pre:where(.dokan-layout,.dokan-layout *) {
  margin: 0;
}

fieldset:where(.dokan-layout,.dokan-layout *) {
  margin: 0;
  padding: 0;
}

legend:where(.dokan-layout,.dokan-layout *) {
  padding: 0;
}

ol:where(.dokan-layout,.dokan-layout *),
ul:where(.dokan-layout,.dokan-layout *),
menu:where(.dokan-layout,.dokan-layout *) {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog:where(.dokan-layout,.dokan-layout *) {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea:where(.dokan-layout,.dokan-layout *) {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

:where(.dokan-layout,.dokan-layout *) input::-moz-placeholder, :where(.dokan-layout,.dokan-layout *) textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

:where(.dokan-layout,.dokan-layout *) input::placeholder,
:where(.dokan-layout,.dokan-layout *) textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button:where(.dokan-layout,.dokan-layout *),
[role="button"]:where(.dokan-layout,.dokan-layout *) {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled:where(.dokan-layout,.dokan-layout *) {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img:where(.dokan-layout,.dokan-layout *),
svg:where(.dokan-layout,.dokan-layout *),
video:where(.dokan-layout,.dokan-layout *),
canvas:where(.dokan-layout,.dokan-layout *),
audio:where(.dokan-layout,.dokan-layout *),
iframe:where(.dokan-layout,.dokan-layout *),
embed:where(.dokan-layout,.dokan-layout *),
object:where(.dokan-layout,.dokan-layout *) {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img:where(.dokan-layout,.dokan-layout *),
video:where(.dokan-layout,.dokan-layout *) {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])):where(.dokan-layout,.dokan-layout *) {
  display: none;
}

[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #2563eb;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  color: #6b7280;
  opacity: 1;
}

input::placeholder,textarea::placeholder {
  color: #6b7280;
  opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

::-webkit-date-and-time-value {
  min-height: 1.5em;
  text-align: inherit;
}

::-webkit-datetime-edit {
  display: inline-flex;
}

::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {
  padding-top: 0;
  padding-bottom: 0;
}

select {
  background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 20 20%27%3e%3cpath stroke=%27%236b7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%271.5%27 d=%27M6 8l4 4 4-4%27/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[multiple],[size]:where(select:not([size="1"])) {
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: unset;
          print-color-adjust: unset;
}

[type='checkbox'],[type='radio'] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #2563eb;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

[type='checkbox'] {
  border-radius: 0px;
}

[type='radio'] {
  border-radius: 100%;
}

[type='checkbox']:focus,[type='radio']:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

[type='checkbox']:checked,[type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {

  [type='checkbox']:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {

  [type='radio']:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='checkbox']:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 16%27%3e%3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27M4 8h8%27/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@media (forced-colors: active)  {

  [type='checkbox']:indeterminate {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='file'] {
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}

[type='file']:focus {
  outline: 1px solid ButtonText;
  outline: 1px auto -webkit-focus-ring-color;
}:root{--colors-primary-500: var(--dokan-button-border-color, #7047EB);--wp-components-color-accent: var(--dokan-button-background-color, #7047EB);--wp-components-color-accent-darker-20: var(--dokan-button-hover-background-color, #502BBF)}#headlessui-portal-root{display:none}#headlessui-portal-root.dokan-layout button[type=button]:is(.absolute.right-2.top-2) {
  border-radius: 9999px;
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}#headlessui-portal-root.dokan-layout button[type=button]:is(.absolute.right-2.top-2):hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
  --tw-ring-offset-width: 2px;
}.dokan-layout table:not(.dataviews-view-table),.dokan-layout table:not(.dataviews-view-table) th,.dokan-layout table:not(.dataviews-view-table) td{margin:0;padding:0;border:0;border-spacing:0;border-collapse:collapse;font-size:inherit;font-weight:inherit;text-align:inherit;vertical-align:inherit;box-sizing:border-box}.dokan-layout a:focus:not([role=switch],[role=combobox]),.dokan-layout button:focus:not([role=switch],[role=combobox]),.dokan-layout .button.alt:focus:not([role=switch],[role=combobox]),.dokan-layout textarea:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=button]:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=reset]:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=submit]:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=tel]:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=url]:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=password]:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=search]:focus:not([role=switch],[role=combobox]){outline-color:var(--dokan-button-border-color, #7047EB)}.dokan-layout  a:not(.dokan-btn):not([class*=dokan-btn-],.skip-color-module) {
  color: var(--dokan-link-color, #7047EB);
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}.dokan-layout  a:not(.dokan-btn):not([class*=dokan-btn-],.skip-color-module):hover,:is(.dokan-layout a:not(.dokan-btn):not([class*=dokan-btn-],.skip-color-module):focus) {
  color: var(--dokan-link-hover-color, var(--dokan-sidebar-background-color, #322067));
}.dokan-layout  input[type=checkbox]::before {
  --tw-content: "" !important;
  content: var(--tw-content) !important;
}.dokan-layout textarea:focus,.dokan-layout input[type=text]:focus,.dokan-layout input[type=number]:focus{outline-offset:0}button[data-headlessui-state=checked]:hover,button[data-headlessui-state=checked]:focus{background-color:var(--dokan-button-background-color, #7047EB) !important}div[data-radix-popper-content-wrapper],div[data-headlessui-state=open][role=dialog]{z-index:999 !important}.container {
  width: 100%;
}@media (min-width: 640px) {

  .container {
    max-width: 640px;
  }
}@media (min-width: 768px) {

  .container {
    max-width: 768px;
  }
}@media (min-width: 1024px) {

  .container {
    max-width: 1024px;
  }
}@media (min-width: 1280px) {

  .container {
    max-width: 1280px;
  }
}@media (min-width: 1536px) {

  .container {
    max-width: 1536px;
  }
}.dokan-layout .sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}.dokan-layout .visible {
  visibility: visible;
}.dokan-layout .static {
  position: static;
}.dokan-layout .fixed {
  position: fixed;
}.dokan-layout .absolute {
  position: absolute;
}.dokan-layout .relative {
  position: relative;
}.dokan-layout .sticky {
  position: sticky;
}.dokan-layout .-start-6 {
  inset-inline-start: -1.5rem;
}.dokan-layout .-top-2 {
  top: -0.5rem;
}.dokan-layout .right-0 {
  right: 0px;
}.dokan-layout .right-0\.5 {
  right: 0.125rem;
}.dokan-layout .right-1 {
  right: 0.25rem;
}.dokan-layout .right-3 {
  right: 0.75rem;
}.dokan-layout .right-6 {
  right: 1.5rem;
}.dokan-layout .top-0 {
  top: 0px;
}.dokan-layout .top-0\.5 {
  top: 0.125rem;
}.dokan-layout .top-1 {
  top: 0.25rem;
}.dokan-layout .top-6 {
  top: 1.5rem;
}.dokan-layout .top-full {
  top: 100%;
}.dokan-layout .z-0 {
  z-index: 0;
}.dokan-layout .z-10 {
  z-index: 10;
}.dokan-layout .z-40 {
  z-index: 40;
}.dokan-layout .z-50 {
  z-index: 50;
}.dokan-layout .col-span-12 {
  grid-column: span 12 / span 12;
}.dokan-layout .col-span-4 {
  grid-column: span 4 / span 4;
}.dokan-layout .-m-4 {
  margin: -1rem;
}.dokan-layout .m-0 {
  margin: 0px;
}.dokan-layout .mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}.dokan-layout .mx-auto {
  margin-left: auto;
  margin-right: auto;
}.dokan-layout .my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}.dokan-layout .my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}.dokan-layout .-mb-px {
  margin-bottom: -1px;
}.dokan-layout .-mt-10 {
  margin-top: -2.5rem;
}.dokan-layout .mb-0 {
  margin-bottom: 0px;
}.dokan-layout .mb-10 {
  margin-bottom: 2.5rem;
}.dokan-layout .mb-12 {
  margin-bottom: 3rem;
}.dokan-layout .mb-14 {
  margin-bottom: 3.5rem;
}.dokan-layout .mb-2 {
  margin-bottom: 0.5rem;
}.dokan-layout .mb-3 {
  margin-bottom: 0.75rem;
}.dokan-layout .mb-4 {
  margin-bottom: 1rem;
}.dokan-layout .mb-5 {
  margin-bottom: 1.25rem;
}.dokan-layout .mb-6 {
  margin-bottom: 1.5rem;
}.dokan-layout .mb-7 {
  margin-bottom: 1.75rem;
}.dokan-layout .mb-8 {
  margin-bottom: 2rem;
}.dokan-layout .ml-1 {
  margin-left: 0.25rem;
}.dokan-layout .ml-2 {
  margin-left: 0.5rem;
}.dokan-layout .ml-5 {
  margin-left: 1.25rem;
}.dokan-layout .ml-auto {
  margin-left: auto;
}.dokan-layout .mr-2 {
  margin-right: 0.5rem;
}.dokan-layout .mr-20 {
  margin-right: 5rem;
}.dokan-layout .mr-4 {
  margin-right: 1rem;
}.dokan-layout .mr-5 {
  margin-right: 1.25rem;
}.dokan-layout .mr-\[20px\] {
  margin-right: 20px;
}.dokan-layout .mr-auto {
  margin-right: auto;
}.dokan-layout .ms-10 {
  margin-inline-start: 2.5rem;
}.dokan-layout .mt-0 {
  margin-top: 0px;
}.dokan-layout .mt-0\.5 {
  margin-top: 0.125rem;
}.dokan-layout .mt-1 {
  margin-top: 0.25rem;
}.dokan-layout .mt-1\.5 {
  margin-top: 0.375rem;
}.dokan-layout .mt-10 {
  margin-top: 2.5rem;
}.dokan-layout .mt-12 {
  margin-top: 3rem;
}.dokan-layout .mt-16 {
  margin-top: 4rem;
}.dokan-layout .mt-2 {
  margin-top: 0.5rem;
}.dokan-layout .mt-3 {
  margin-top: 0.75rem;
}.dokan-layout .mt-4 {
  margin-top: 1rem;
}.dokan-layout .mt-5 {
  margin-top: 1.25rem;
}.dokan-layout .mt-6 {
  margin-top: 1.5rem;
}.dokan-layout .box-border {
  box-sizing: border-box;
}.dokan-layout .block {
  display: block;
}.dokan-layout .inline-block {
  display: inline-block;
}.dokan-layout .inline {
  display: inline;
}.dokan-layout .flex {
  display: flex;
}.dokan-layout .inline-flex {
  display: inline-flex;
}.dokan-layout .table {
  display: table;
}.dokan-layout .table-column {
  display: table-column;
}.dokan-layout .table-row {
  display: table-row;
}.dokan-layout .grid {
  display: grid;
}.dokan-layout .hidden {
  display: none;
}.dokan-layout .aspect-video {
  aspect-ratio: 16 / 9;
}.dokan-layout .size-5 {
  width: 1.25rem;
  height: 1.25rem;
}.dokan-layout .h-1\.5 {
  height: 0.375rem;
}.dokan-layout .h-1\/2 {
  height: 50%;
}.dokan-layout .h-10 {
  height: 2.5rem;
}.dokan-layout .h-12 {
  height: 3rem;
}.dokan-layout .h-14 {
  height: 3.5rem;
}.dokan-layout .h-16 {
  height: 4rem;
}.dokan-layout .h-3 {
  height: 0.75rem;
}.dokan-layout .h-3\.5 {
  height: 0.875rem;
}.dokan-layout .h-36 {
  height: 9rem;
}.dokan-layout .h-4 {
  height: 1rem;
}.dokan-layout .h-48 {
  height: 12rem;
}.dokan-layout .h-5 {
  height: 1.25rem;
}.dokan-layout .h-6 {
  height: 1.5rem;
}.dokan-layout .h-7 {
  height: 1.75rem;
}.dokan-layout .h-8 {
  height: 2rem;
}.dokan-layout .h-\[21rem\] {
  height: 21rem;
}.dokan-layout .h-\[25px\] {
  height: 25px;
}.dokan-layout .h-\[3rem\] {
  height: 3rem;
}.dokan-layout .h-\[48px\] {
  height: 48px;
}.dokan-layout .h-\[85px\] {
  height: 85px;
}.dokan-layout .h-full {
  height: 100%;
}.dokan-layout .max-h-\[280px\] {
  max-height: 280px;
}.dokan-layout .max-h-\[500px\] {
  max-height: 500px;
}.dokan-layout .\!min-h-\[max-content\] {
  min-height: -moz-max-content !important;
  min-height: max-content !important;
}.dokan-layout .min-h-32 {
  min-height: 8rem;
}.dokan-layout .min-h-48 {
  min-height: 12rem;
}.dokan-layout .min-h-\[194px\] {
  min-height: 194px;
}.dokan-layout .min-h-\[3rem\] {
  min-height: 3rem;
}.dokan-layout .min-h-screen {
  min-height: 100vh;
}.dokan-layout .w-1\.5 {
  width: 0.375rem;
}.dokan-layout .w-1\/2 {
  width: 50%;
}.dokan-layout .w-1\/3 {
  width: 33.333333%;
}.dokan-layout .w-1\/4 {
  width: 25%;
}.dokan-layout .w-10 {
  width: 2.5rem;
}.dokan-layout .w-12 {
  width: 3rem;
}.dokan-layout .w-14 {
  width: 3.5rem;
}.dokan-layout .w-16 {
  width: 4rem;
}.dokan-layout .w-2\/3 {
  width: 66.666667%;
}.dokan-layout .w-20 {
  width: 5rem;
}.dokan-layout .w-24 {
  width: 6rem;
}.dokan-layout .w-28 {
  width: 7rem;
}.dokan-layout .w-3\.5 {
  width: 0.875rem;
}.dokan-layout .w-3\/4 {
  width: 75%;
}.dokan-layout .w-3\/6 {
  width: 50%;
}.dokan-layout .w-32 {
  width: 8rem;
}.dokan-layout .w-36 {
  width: 9rem;
}.dokan-layout .w-4 {
  width: 1rem;
}.dokan-layout .w-4\/6 {
  width: 66.666667%;
}.dokan-layout .w-44 {
  width: 11rem;
}.dokan-layout .w-48 {
  width: 12rem;
}.dokan-layout .w-5 {
  width: 1.25rem;
}.dokan-layout .w-5\/6 {
  width: 83.333333%;
}.dokan-layout .w-6 {
  width: 1.5rem;
}.dokan-layout .w-64 {
  width: 16rem;
}.dokan-layout .w-7 {
  width: 1.75rem;
}.dokan-layout .w-8 {
  width: 2rem;
}.dokan-layout .w-\[10\.5rem\] {
  width: 10.5rem;
}.dokan-layout .w-\[11rem\] {
  width: 11rem;
}.dokan-layout .w-\[137px\] {
  width: 137px;
}.dokan-layout .w-\[157px\] {
  width: 157px;
}.dokan-layout .w-\[169px\] {
  width: 169px;
}.dokan-layout .w-\[5px\] {
  width: 5px;
}.dokan-layout .w-auto {
  width: auto;
}.dokan-layout .w-fit {
  width: -moz-fit-content;
  width: fit-content;
}.dokan-layout .w-full {
  width: 100%;
}.dokan-layout .min-w-0 {
  min-width: 0px;
}.dokan-layout .min-w-72 {
  min-width: 18rem;
}.dokan-layout .min-w-\[25px\] {
  min-width: 25px;
}.dokan-layout .min-w-full {
  min-width: 100%;
}.dokan-layout .max-w-2xl {
  max-width: 42rem;
}.dokan-layout .max-w-3xl {
  max-width: 48rem;
}.dokan-layout .max-w-7xl {
  max-width: 80rem;
}.dokan-layout .max-w-\[20rem\] {
  max-width: 20rem;
}.dokan-layout .max-w-\[23rem\] {
  max-width: 23rem;
}.dokan-layout .max-w-\[24rem\] {
  max-width: 24rem;
}.dokan-layout .max-w-\[450px\] {
  max-width: 450px;
}.dokan-layout .max-w-\[46rem\] {
  max-width: 46rem;
}.dokan-layout .max-w-\[530px\] {
  max-width: 530px;
}.dokan-layout .max-w-\[720px\] {
  max-width: 720px;
}.dokan-layout .max-w-full {
  max-width: 100%;
}.dokan-layout .max-w-md {
  max-width: 28rem;
}.dokan-layout .max-w-sm {
  max-width: 24rem;
}.dokan-layout .max-w-xl {
  max-width: 36rem;
}.dokan-layout .flex-1 {
  flex: 1 1 0%;
}.dokan-layout .flex-auto {
  flex: 1 1 auto;
}.dokan-layout .flex-shrink-0 {
  flex-shrink: 0;
}.dokan-layout .shrink {
  flex-shrink: 1;
}.dokan-layout .rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.dokan-layout .scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.dokan-layout .scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.dokan-layout .transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}@keyframes pulse {

  50% {
    opacity: .5;
  }
}.dokan-layout .animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}.dokan-layout .animate-spin {
  animation: spin 1s linear infinite;
}.dokan-layout .cursor-pointer {
  cursor: pointer;
}.dokan-layout .resize {
  resize: both;
}.dokan-layout .grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}.dokan-layout .grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}.dokan-layout .grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}.dokan-layout .grid-cols-8 {
  grid-template-columns: repeat(8, minmax(0, 1fr));
}.dokan-layout .flex-row {
  flex-direction: row;
}.dokan-layout .flex-col {
  flex-direction: column;
}.dokan-layout .flex-wrap {
  flex-wrap: wrap;
}.dokan-layout .items-start {
  align-items: flex-start;
}.dokan-layout .items-end {
  align-items: flex-end;
}.dokan-layout .items-center {
  align-items: center;
}.dokan-layout .justify-start {
  justify-content: flex-start;
}.dokan-layout .justify-end {
  justify-content: flex-end;
}.dokan-layout .justify-center {
  justify-content: center;
}.dokan-layout .justify-between {
  justify-content: space-between;
}.dokan-layout .gap-1 {
  gap: 0.25rem;
}.dokan-layout .gap-1\.5 {
  gap: 0.375rem;
}.dokan-layout .gap-2 {
  gap: 0.5rem;
}.dokan-layout .gap-2\.5 {
  gap: 0.625rem;
}.dokan-layout .gap-3 {
  gap: 0.75rem;
}.dokan-layout .gap-4 {
  gap: 1rem;
}.dokan-layout .gap-5 {
  gap: 1.25rem;
}.dokan-layout .gap-6 {
  gap: 1.5rem;
}.dokan-layout .gap-7 {
  gap: 1.75rem;
}.dokan-layout .gap-x-1 {
  -moz-column-gap: 0.25rem;
       column-gap: 0.25rem;
}.dokan-layout .gap-x-2 {
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}.dokan-layout .gap-y-4 {
  row-gap: 1rem;
}.dokan-layout :is(.space-x-1 > :not([hidden]) ~ :not([hidden])) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}.dokan-layout :is(.space-x-2 > :not([hidden]) ~ :not([hidden])) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}.dokan-layout :is(.space-x-3 > :not([hidden]) ~ :not([hidden])) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}.dokan-layout :is(.space-x-4 > :not([hidden]) ~ :not([hidden])) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}.dokan-layout :is(.space-x-8 > :not([hidden]) ~ :not([hidden])) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}.dokan-layout :is(.space-y-2 > :not([hidden]) ~ :not([hidden])) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}.dokan-layout :is(.space-y-2\.5 > :not([hidden]) ~ :not([hidden])) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.625rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.625rem * var(--tw-space-y-reverse));
}.dokan-layout :is(.space-y-3 > :not([hidden]) ~ :not([hidden])) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}.dokan-layout :is(.space-y-4 > :not([hidden]) ~ :not([hidden])) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}.dokan-layout :is(.space-y-6 > :not([hidden]) ~ :not([hidden])) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}.dokan-layout :is(.space-y-8 > :not([hidden]) ~ :not([hidden])) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}.dokan-layout :is(.divide-y > :not([hidden]) ~ :not([hidden])) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}.dokan-layout :is(.divide-gray-200 > :not([hidden]) ~ :not([hidden])) {
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}.dokan-layout :is(.divide-gray-300 > :not([hidden]) ~ :not([hidden])) {
  --tw-divide-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-divide-opacity, 1));
}.dokan-layout .self-center {
  align-self: center;
}.dokan-layout .overflow-auto {
  overflow: auto;
}.dokan-layout .overflow-hidden {
  overflow: hidden;
}.dokan-layout .overflow-y-auto {
  overflow-y: auto;
}.dokan-layout .truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}.dokan-layout .whitespace-nowrap {
  white-space: nowrap;
}.dokan-layout .rounded {
  border-radius: 0.25rem;
}.dokan-layout .rounded-2xl {
  border-radius: 1rem;
}.dokan-layout .rounded-\[20px\] {
  border-radius: 20px;
}.dokan-layout .rounded-full {
  border-radius: 9999px;
}.dokan-layout .rounded-lg {
  border-radius: 0.5rem;
}.dokan-layout .rounded-md {
  border-radius: 0.375rem;
}.dokan-layout .rounded-none {
  border-radius: 0px;
}.dokan-layout .rounded-sm {
  border-radius: 0.125rem;
}.dokan-layout .rounded-l-md {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}.dokan-layout .rounded-l-none {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}.dokan-layout .rounded-r-md {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}.dokan-layout .rounded-r-none {
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}.dokan-layout .border {
  border-width: 1px;
}.dokan-layout .border-0 {
  border-width: 0px;
}.dokan-layout .border-2 {
  border-width: 2px;
}.dokan-layout .border-\[0\.957434px\] {
  border-width: 0.957434px;
}.dokan-layout .border-\[1px\] {
  border-width: 1px;
}.dokan-layout .\!border-b-2 {
  border-bottom-width: 2px !important;
}.dokan-layout .\!border-b-\[1px\] {
  border-bottom-width: 1px !important;
}.dokan-layout .\!border-r-\[1px\] {
  border-right-width: 1px !important;
}.dokan-layout .border-b {
  border-bottom-width: 1px;
}.dokan-layout .border-b-0 {
  border-bottom-width: 0px;
}.dokan-layout .border-b-2 {
  border-bottom-width: 2px;
}.dokan-layout .border-b-\[1px\] {
  border-bottom-width: 1px;
}.dokan-layout .border-l {
  border-left-width: 1px;
}.dokan-layout .border-l-0 {
  border-left-width: 0px;
}.dokan-layout .border-l-\[1px\] {
  border-left-width: 1px;
}.dokan-layout .border-r {
  border-right-width: 1px;
}.dokan-layout .border-r-\[0\.957434px\] {
  border-right-width: 0.957434px;
}.dokan-layout .border-r-\[1px\] {
  border-right-width: 1px;
}.dokan-layout .border-s-2 {
  border-inline-start-width: 2px;
}.dokan-layout .border-t {
  border-top-width: 1px;
}.dokan-layout .border-solid {
  border-style: solid;
}.dokan-layout .border-dashed {
  border-style: dashed;
}.dokan-layout .border-none {
  border-style: none;
}.dokan-layout .\!border-dokan-btn {
  border-color: var(--dokan-button-border-color, #7047EB) !important;
}.dokan-layout .border-\[\#1a9ed4\] {
  --tw-border-opacity: 1;
  border-color: rgb(26 158 212 / var(--tw-border-opacity, 1));
}.dokan-layout .border-\[\#5341C20F\] {
  border-color: #5341C20F;
}.dokan-layout .border-\[\#7047EB\] {
  --tw-border-opacity: 1;
  border-color: rgb(112 71 235 / var(--tw-border-opacity, 1));
}.dokan-layout .border-\[\#E5E0F2\] {
  --tw-border-opacity: 1;
  border-color: rgb(229 224 242 / var(--tw-border-opacity, 1));
}.dokan-layout .border-\[\#E9E9E9\] {
  --tw-border-opacity: 1;
  border-color: rgb(233 233 233 / var(--tw-border-opacity, 1));
}.dokan-layout .border-\[\#e9e9ea\] {
  --tw-border-opacity: 1;
  border-color: rgb(233 233 234 / var(--tw-border-opacity, 1));
}.dokan-layout .border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}.dokan-layout .border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}.dokan-layout .border-indigo-600 {
  --tw-border-opacity: 1;
  border-color: rgb(79 70 229 / var(--tw-border-opacity, 1));
}.dokan-layout .border-orange-500 {
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}.dokan-layout .border-red-300 {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}.dokan-layout .border-red-50 {
  --tw-border-opacity: 1;
  border-color: rgb(254 242 242 / var(--tw-border-opacity, 1));
}.dokan-layout .border-transparent {
  border-color: transparent;
}.dokan-layout .\!bg-transparent {
  background-color: transparent !important;
}.dokan-layout .bg-\[\#0C5F9A\] {
  --tw-bg-opacity: 1;
  background-color: rgb(12 95 154 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#6F4CEB\] {
  --tw-bg-opacity: 1;
  background-color: rgb(111 76 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#7047EB\] {
  --tw-bg-opacity: 1;
  background-color: rgb(112 71 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#D8D8FE\] {
  --tw-bg-opacity: 1;
  background-color: rgb(216 216 254 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#E4E6EB\] {
  --tw-bg-opacity: 1;
  background-color: rgb(228 230 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#EFEAFF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(239 234 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#F1EDFD\] {
  --tw-bg-opacity: 1;
  background-color: rgb(241 237 253 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#F8F9F8\] {
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 248 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#FF9B5366\] {
  background-color: #FF9B5366;
}.dokan-layout .bg-\[\#e4e6eb\] {
  --tw-bg-opacity: 1;
  background-color: rgb(228 230 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-amber-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-indigo-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-indigo-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-indigo-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-orange-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-pink-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 242 248 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-teal-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 250 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-transparent {
  background-color: transparent;
}.dokan-layout .bg-violet-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(76 29 149 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-yellow-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[url\(\'\/assets\/images\/error-page-bg\.png\'\)\] {
  background-image: url(../images/error-page-bg.png);
}.dokan-layout .bg-auto {
  background-size: auto;
}.dokan-layout .bg-cover {
  background-size: cover;
}.dokan-layout .bg-\[-20px_10px\] {
  background-position: -20px 10px;
}.dokan-layout .bg-\[right_-40px_bottom_-40px\] {
  background-position: right -40px bottom -40px;
}.dokan-layout .bg-center {
  background-position: center;
}.dokan-layout .bg-no-repeat {
  background-repeat: no-repeat;
}.dokan-layout .\!fill-\[\#7047EB\] {
  fill: #7047EB !important;
}.dokan-layout .fill-neutral-400 {
  fill: #a3a3a3;
}.dokan-layout .fill-red-500 {
  fill: #ef4444;
}.dokan-layout .fill-white {
  fill: #fff;
}.dokan-layout .stroke-red-500 {
  stroke: #ef4444;
}.dokan-layout .stroke-\[2\.5\] {
  stroke-width: 2.5;
}.dokan-layout .object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}.dokan-layout .p-0 {
  padding: 0px;
}.dokan-layout .p-1 {
  padding: 0.25rem;
}.dokan-layout .p-2 {
  padding: 0.5rem;
}.dokan-layout .p-2\.5 {
  padding: 0.625rem;
}.dokan-layout .p-3 {
  padding: 0.75rem;
}.dokan-layout .p-4 {
  padding: 1rem;
}.dokan-layout .p-5 {
  padding: 1.25rem;
}.dokan-layout .p-6 {
  padding: 1.5rem;
}.dokan-layout .p-8 {
  padding: 2rem;
}.dokan-layout .px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}.dokan-layout .px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}.dokan-layout .px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}.dokan-layout .px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}.dokan-layout .px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}.dokan-layout .px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}.dokan-layout .px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}.dokan-layout .px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}.dokan-layout .px-\[18px\] {
  padding-left: 18px;
  padding-right: 18px;
}.dokan-layout .px-\[25px\] {
  padding-left: 25px;
  padding-right: 25px;
}.dokan-layout .py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}.dokan-layout .py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}.dokan-layout .py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}.dokan-layout .py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}.dokan-layout .py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}.dokan-layout .py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}.dokan-layout .py-3\.5 {
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}.dokan-layout .py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}.dokan-layout .py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}.dokan-layout .py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}.dokan-layout .py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}.dokan-layout .pb-10 {
  padding-bottom: 2.5rem;
}.dokan-layout .pb-5 {
  padding-bottom: 1.25rem;
}.dokan-layout .pl-1 {
  padding-left: 0.25rem;
}.dokan-layout .pl-2 {
  padding-left: 0.5rem;
}.dokan-layout .pl-3 {
  padding-left: 0.75rem;
}.dokan-layout .pl-4 {
  padding-left: 1rem;
}.dokan-layout .pl-\[5px\] {
  padding-left: 5px;
}.dokan-layout .pr-0 {
  padding-right: 0px;
}.dokan-layout .pr-1\.5 {
  padding-right: 0.375rem;
}.dokan-layout .pr-2 {
  padding-right: 0.5rem;
}.dokan-layout .pt-4 {
  padding-top: 1rem;
}.dokan-layout .text-left {
  text-align: left;
}.dokan-layout .text-center {
  text-align: center;
}.dokan-layout .align-middle {
  vertical-align: middle;
}.dokan-layout .text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}.dokan-layout .text-2xl\/7 {
  font-size: 1.5rem;
  line-height: 1.75rem;
}.dokan-layout .text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}.dokan-layout .text-\[10px\] {
  font-size: 10px;
}.dokan-layout .text-\[12px\] {
  font-size: 12px;
}.dokan-layout .text-\[14px\] {
  font-size: 14px;
}.dokan-layout .text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}.dokan-layout .text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}.dokan-layout .text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}.dokan-layout .text-sm\/6 {
  font-size: 0.875rem;
  line-height: 1.5rem;
}.dokan-layout .text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}.dokan-layout .text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}.dokan-layout .font-bold {
  font-weight: 700;
}.dokan-layout .font-light {
  font-weight: 300;
}.dokan-layout .font-medium {
  font-weight: 500;
}.dokan-layout .font-normal {
  font-weight: 400;
}.dokan-layout .font-semibold {
  font-weight: 600;
}.dokan-layout .capitalize {
  text-transform: capitalize;
}.dokan-layout .leading-3 {
  line-height: .75rem;
}.dokan-layout .leading-4 {
  line-height: 1rem;
}.dokan-layout .leading-5 {
  line-height: 1.25rem;
}.dokan-layout .leading-6 {
  line-height: 1.5rem;
}.dokan-layout .leading-\[48px\] {
  line-height: 48px;
}.dokan-layout .tracking-wide {
  letter-spacing: 0.025em;
}.dokan-layout .\!text-dokan-primary {
  color: var(--dokan-button-background-color, #7047EB) !important;
}.dokan-layout .\!text-gray-800 {
  --tw-text-opacity: 1 !important;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1)) !important;
}.dokan-layout .\!text-gray-900 {
  --tw-text-opacity: 1 !important;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1)) !important;
}.dokan-layout .\!text-white {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}.dokan-layout .text-\[\#1a9ed4\] {
  --tw-text-opacity: 1;
  color: rgb(26 158 212 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#25252D\] {
  --tw-text-opacity: 1;
  color: rgb(37 37 45 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#393939\] {
  --tw-text-opacity: 1;
  color: rgb(57 57 57 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#4C19E6\] {
  --tw-text-opacity: 1;
  color: rgb(76 25 230 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#575757\] {
  --tw-text-opacity: 1;
  color: rgb(87 87 87 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#637381\] {
  --tw-text-opacity: 1;
  color: rgb(99 115 129 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#7047EB\] {
  --tw-text-opacity: 1;
  color: rgb(112 71 235 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#788383\] {
  --tw-text-opacity: 1;
  color: rgb(120 131 131 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#7B4E2E\] {
  --tw-text-opacity: 1;
  color: rgb(123 78 46 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#828282\] {
  --tw-text-opacity: 1;
  color: rgb(130 130 130 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#bcbed5\] {
  --tw-text-opacity: 1;
  color: rgb(188 190 213 / var(--tw-text-opacity, 1));
}.dokan-layout .text-amber-800 {
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity, 1));
}.dokan-layout .text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}.dokan-layout .text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}.dokan-layout .text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}.dokan-layout .text-dokan-danger {
  color: var(--dokan-danger-text-color, #BC1C21);
}.dokan-layout .text-dokan-link {
  color: var(--dokan-link-color, #7047EB);
}.dokan-layout .text-dokan-primary {
  color: var(--dokan-button-background-color, #7047EB);
}.dokan-layout .text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}.dokan-layout .text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}.dokan-layout .text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}.dokan-layout .text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}.dokan-layout .text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}.dokan-layout .text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}.dokan-layout .text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}.dokan-layout .text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}.dokan-layout .text-indigo-600 {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}.dokan-layout .text-indigo-800 {
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}.dokan-layout .text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}.dokan-layout .text-orange-800 {
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}.dokan-layout .text-pink-800 {
  --tw-text-opacity: 1;
  color: rgb(157 23 77 / var(--tw-text-opacity, 1));
}.dokan-layout .text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}.dokan-layout .text-purple-800 {
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}.dokan-layout .text-red-300 {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}.dokan-layout .text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}.dokan-layout .text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}.dokan-layout .text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}.dokan-layout .text-teal-800 {
  --tw-text-opacity: 1;
  color: rgb(17 94 89 / var(--tw-text-opacity, 1));
}.dokan-layout .text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}.dokan-layout .text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}.dokan-layout .text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}.dokan-layout .underline {
  text-decoration-line: underline;
}.dokan-layout .no-underline {
  text-decoration-line: none;
}.dokan-layout .opacity-0 {
  opacity: 0;
}.dokan-layout .opacity-100 {
  opacity: 1;
}.dokan-layout .opacity-25 {
  opacity: 0.25;
}.dokan-layout .opacity-75 {
  opacity: 0.75;
}.dokan-layout .\!shadow-none {
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}.dokan-layout .shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.dokan-layout .shadow-\[0px_2px_14px_1px_rgba\(50\2c 22\2c 56\2c 0\.06\)\] {
  --tw-shadow: 0px 2px 14px 1px rgba(50,22,56,0.06);
  --tw-shadow-colored: 0px 2px 14px 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.dokan-layout .shadow-\[0px_4px_20px_0px_rgba\(0\2c 0\2c 0\2c 0\.05\)\] {
  --tw-shadow: 0px 4px 20px 0px rgba(0,0,0,0.05);
  --tw-shadow-colored: 0px 4px 20px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.dokan-layout .shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.dokan-layout .shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.dokan-layout .shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.dokan-layout .shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.dokan-layout .outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}.dokan-layout .ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.dokan-layout .ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.dokan-layout .ring-inset {
  --tw-ring-inset: inset;
}.dokan-layout .ring-black\/5 {
  --tw-ring-color: rgb(0 0 0 / 0.05);
}.dokan-layout .ring-gray-900\/5 {
  --tw-ring-color: rgb(17 24 39 / 0.05);
}.dokan-layout .ring-white {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));
}.dokan-layout .filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}.dokan-layout .transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.dokan-layout .transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.dokan-layout .transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.dokan-layout .transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.dokan-layout .transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.dokan-layout .duration-150 {
  transition-duration: 150ms;
}.dokan-layout .duration-200 {
  transition-duration: 200ms;
}.dokan-layout .duration-300 {
  transition-duration: 300ms;
}.dokan-layout .duration-500 {
  transition-duration: 500ms;
}.dokan-layout .ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}.dokan-layout .ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}.dokan-layout .\@container\/after-header {
  container-type: inline-size;
  container-name: after-header;
}.dokan-layout .\@container\/before-header {
  container-type: inline-size;
  container-name: before-header;
}.dokan-layout .\@container\/combine {
  container-type: inline-size;
  container-name: combine;
}.dokan-layout .\@container\/currency {
  container-type: inline-size;
  container-name: currency;
}.dokan-layout .\@container\/header {
  container-type: inline-size;
  container-name: header;
}.dokan-layout .\@container\/header-title-section {
  container-type: inline-size;
  container-name: header-title-section;
}.dokan-layout .\@container\/main {
  container-type: inline-size;
  container-name: main;
}.dokan-layout .\@container\/radio {
  container-type: inline-size;
  container-name: radio;
}.dokan-layout .\@container\/step-body {
  container-type: inline-size;
  container-name: step-body;
}.dokan-layout .\@container\/switcher {
  container-type: inline-size;
  container-name: switcher;
}.dokan-layout  .dokan-btn {
  position: relative !important;
  border-color: var(--dokan-button-border-color, #7047EB) !important;
  background-color: var(--dokan-button-background-color, #7047EB) !important;
  color: var(--dokan-button-text-color, #ffffff) !important;
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
  --tw-ring-color: var(--dokan-button-border-color, #7047EB) !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;
  transition-duration: 200ms !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
}.dokan-layout  .dokan-btn:hover {
  border-color: var(--dokan-button-hover-border-color, #370EB1) !important;
  background-color: var(--dokan-button-hover-background-color, #502BBF) !important;
  color: var(--dokan-button-hover-text-color, #ffffff) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn:disabled {
  cursor: not-allowed !important;
  opacity: 0.6 !important;
}.dokan-layout  .dokan-btn-secondary {
  position: relative !important;
  border-style: none !important;
  border-color: var(--dokan-button-secondary-border-color, var(--dokan-button-border-color, #7047EB)) !important;
  background-color: var(--dokan-button-secondary-background-color, #ffffff) !important;
  color: var(--dokan-button-secondary-text-color, #7047EB) !important;
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-color: var(--dokan-button-secondary-border-color, var(--dokan-button-border-color, #7047EB)) !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;
  transition-duration: 200ms !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
}.dokan-layout  .dokan-btn-secondary:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-secondary:hover {
  border-color: var(--dokan-button-secondary-hover-border-color, #7047EB) !important;
  background-color: var(--dokan-button-secondary-hover-background-color, #DACEFF82) !important;
  color: var(--dokan-button-secondary-hover-text-color, #7047EB) !important;
  --tw-ring-color: var(--dokan-button-secondary-hover-border-color, #7047EB) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout .dokan-btn-tertiary{--tw-shadow: 1px 3px 4px 0 rgb(0 0 0 / 0.05);position: relative !important;border-color: var(--dokan-button-tertiary-border-color, #00000000) !important;background-color: var(--dokan-button-tertiary-background-color, #00000000) !important;color: var(--dokan-button-tertiary-text-color, var(--dokan-button-background-color, #7047EB)) !important;--tw-ring-color: var(--dokan-button-tertiary-border-color, #00000000) !important;transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;transition-duration: 200ms !important;transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important}.dokan-layout  .dokan-btn-tertiary:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-tertiary:hover {
  background-color: var(--dokan-button-tertiary-hover-background-color, #DACEFF82) !important;
  color: var(--dokan-button-tertiary-hover-text-color, var(--dokan-button-background-color, #7047EB)) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-info {
  position: relative !important;
  border-color: var(--dokan-button-info-background-color, #0B76B7) !important;
  background-color: var(--dokan-button-info-background-color, #0B76B7) !important;
  color: var(--dokan-button-info-text-color, #ffffff) !important;
  --tw-ring-color: var(--dokan-button-info-background-color, #0B76B7) !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-duration: 200ms !important;
}.dokan-layout  .dokan-btn-info:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-info:hover {
  border-color: var(--dokan-button-info-hover-background-color, #2795d7) !important;
  background-color: var(--dokan-button-info-hover-background-color, #2795d7) !important;
  color: var(--dokan-button-info-hover-text-color, #ffffff) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-color: var(--dokan-button-info-hover-background-color, #2795d7) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-success {
  position: relative !important;
  border-color: var(--dokan-button-success-background-color, #07a67e) !important;
  background-color: var(--dokan-button-success-background-color, #07a67e) !important;
  color: var(--dokan-button-success-text-color, #ffffff) !important;
  --tw-ring-color: var(--dokan-button-success-background-color, #07a67e) !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-duration: 200ms !important;
}.dokan-layout  .dokan-btn-success:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-success:hover {
  border-color: var(--dokan-button-success-hover-background-color, #11b68c) !important;
  background-color: var(--dokan-button-success-hover-background-color, #11b68c) !important;
  color: var(--dokan-button-success-hover-text-color, #ffffff) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-color: var(--dokan-button-success-hover-background-color, #11b68c) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-warning {
  position: relative !important;
  border-color: var(--dokan-button-warning-background-color, #e9a905) !important;
  background-color: var(--dokan-button-warning-background-color, #e9a905) !important;
  color: var(--dokan-button-warning-text-color, #ffffff) !important;
  --tw-ring-color: var(--dokan-button-warning-background-color, #e9a905) !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-duration: 200ms !important;
}.dokan-layout  .dokan-btn-warning:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-warning:hover {
  border-color: var(--dokan-button-warning-hover-background-color, #fbbf24) !important;
  background-color: var(--dokan-button-warning-hover-background-color, #fbbf24) !important;
  color: var(--dokan-button-warning-hover-text-color, #ffffff) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-color: var(--dokan-button-warning-hover-background-color, #fbbf24) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-danger {
  position: relative !important;
  border-color: var(--dokan-button-danger-background-color, #e3050c) !important;
  background-color: var(--dokan-button-danger-background-color, #e3050c) !important;
  color: var(--dokan-button-danger-text-color, #ffffff) !important;
  --tw-ring-color: var(--dokan-button-danger-background-color, #e3050c) !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-duration: 200ms !important;
}.dokan-layout  .dokan-btn-danger:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .dokan-btn-danger:hover {
  border-color: var(--dokan-button-danger-hover-background-color, #f23030) !important;
  background-color: var(--dokan-button-danger-hover-background-color, #f23030) !important;
  color: var(--dokan-button-danger-hover-text-color, #ffffff) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-color: var(--dokan-button-danger-hover-background-color, #f23030) !important;
  --tw-ring-offset-width: 0px !important;
}.dokan-layout  .\!dokan-link {
  position: relative !important;
  color: var(--dokan-link-color, #7047EB) !important;
  text-decoration-line: none !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-duration: 200ms !important;
}.dokan-layout  .dokan-link {
  position: relative !important;
  color: var(--dokan-link-color, #7047EB) !important;
  text-decoration-line: none !important;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-duration: 200ms !important;
}.dokan-layout  .\!dokan-link:hover,:is(.dokan-layout .\!dokan-link:focus) {
  color: var(--dokan-link-hover-color, var(--dokan-sidebar-background-color, #322067)) !important;
}.dokan-layout  .dokan-link:hover,:is(.dokan-layout .dokan-link:focus) {
  color: var(--dokan-link-hover-color, var(--dokan-sidebar-background-color, #322067)) !important;
}.dokan-layout  .\!dokan-link:hover,:is(.dokan-layout .\!dokan-link:focus) {
  color: var(--dokan-link-hover-color, var(--dokan-sidebar-background-color, #322067)) !important;
}.dokan-layout  .dokan-link:hover,:is(.dokan-layout .dokan-link:focus) {
  color: var(--dokan-link-hover-color, var(--dokan-sidebar-background-color, #322067)) !important;
}.dokan-layout  .dokan-badge-primary {
  position: relative !important;
  background-color: var(--dokan-button-background-color, #7047EB) !important;
  color: var(--dokan-button-text-color, #ffffff) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-button-border-color, #7047EB) !important;
}.dokan-layout  .dokan-badge-secondary {
  position: relative !important;
  background-color: var(--dokan-button-secondary-background-color, #ffffff) !important;
  color: var(--dokan-button-secondary-text-color, #7047EB) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-button-secondary-border-color, var(--dokan-button-border-color, #7047EB)) !important;
}.dokan-layout  .dokan-badge-info {
  position: relative !important;
  background-color: var(--dokan-info-background-color, #E9F9FF) !important;
  color: var(--dokan-info-text-color, #0B76B7) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-info-border-color, #E9F9FFFF) !important;
}.dokan-layout  .dokan-badge-success {
  position: relative !important;
  background-color: var(--dokan-success-background-color, #DAF8E6) !important;
  color: var(--dokan-success-text-color, #004434) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-success-border-color, #DAF8E6FF) !important;
}.dokan-layout  .dokan-badge-warning {
  position: relative !important;
  background-color: var(--dokan-warning-background-color, #FFFBEB) !important;
  color: var(--dokan-warning-text-color, #9D5425) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-warning-border-color, #FFFBEBFF) !important;
}.dokan-layout  .dokan-badge-danger {
  position: relative !important;
  background-color: var(--dokan-danger-background-color, #FEF3F3) !important;
  color: var(--dokan-danger-text-color, #BC1C21) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-danger-border-color, #FEF3F3FF) !important;
}.dokan-layout  .dokan-alert-info {
  position: relative !important;
  background-color: var(--dokan-info-background-color, #E9F9FF) !important;
  color: var(--dokan-info-secondary-text-color, #637381) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-info-border-color, #E9F9FFFF) !important;
}.dokan-layout  .dokan-alert-success {
  position: relative !important;
  background-color: var(--dokan-success-background-color, #DAF8E6) !important;
  color: var(--dokan-success-secondary-text-color, #637381) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-success-border-color, #DAF8E6FF) !important;
}.dokan-layout  .dokan-alert-warning {
  position: relative !important;
  background-color: var(--dokan-warning-background-color, #FFFBEB) !important;
  color: var(--dokan-warning-secondary-text-color, #D0915C) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-warning-border-color, #FFFBEBFF) !important;
}.dokan-layout  .dokan-alert-danger {
  position: relative !important;
  background-color: var(--dokan-danger-background-color, #FEF3F3) !important;
  color: var(--dokan-danger-secondary-text-color, #F56060) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-inset: inset !important;
  --tw-ring-color: var(--dokan-danger-border-color, #FEF3F3FF) !important;
}.dokan-withdraw-style-reset button:focus,.dokan-withdraw-style-reset .menu-toggle:hover,.dokan-withdraw-style-reset button:hover,.dokan-withdraw-style-reset .button:hover,.dokan-withdraw-style-reset .ast-custom-button:hover,.dokan-withdraw-style-reset input[type=reset]:hover,.dokan-withdraw-style-reset input[type=reset]:focus,.dokan-withdraw-style-reset input#submit:hover,.dokan-withdraw-style-reset input#submit:focus,.dokan-withdraw-style-reset input[type=button]:hover,.dokan-withdraw-style-reset input[type=button]:focus,.dokan-withdraw-style-reset input[type=submit]:hover,.dokan-withdraw-style-reset input[type=submit]:focus{background-color:var(--dokan-button-hover-color, #7047EB);border-color:var(--dokan-button-hover-background-color, #7047EB)}.dokan-withdraw-style-reset button{box-shadow:none;border:none}.dokan-withdraw-style-reset input{border:none}.dokan-withdraw-style-reset table,.dokan-withdraw-style-reset th,.dokan-withdraw-style-reset td{margin:0;padding:0;border:0;border-spacing:0;border-collapse:collapse;font-size:inherit;font-weight:inherit;text-align:inherit;vertical-align:inherit;box-sizing:border-box}.dokan-withdraw-style-reset input[type=text][role=combobox]{height:-moz-fit-content;height:fit-content}.dokan-withdraw-style-reset .border{border-style:solid;border-width:1px}.dokan-withdraw-style-reset .border-b{border-bottom-width:1px;border-bottom-style:solid}#headlessui-portal-root.dokan-layout button[type=button]:is(.before\:absolute.right-2.top-2)::before{content: var(--tw-content);border-radius: 9999px;padding-top: 0.125rem;padding-bottom: 0.125rem;padding-left: 0.375rem;padding-right: 0.375rem}#headlessui-portal-root.dokan-layout button[type=button]:is(.before\:absolute.right-2.top-2):hover::before{content: var(--tw-content);--tw-bg-opacity: 1;background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color: rgb(239 68 68 / var(--tw-text-opacity, 1));--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);--tw-ring-opacity: 1;--tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));--tw-ring-offset-width: 2px}.dokan-layout .before\:absolute::before {
  content: var(--tw-content);
  position: absolute;
}.dokan-layout .before\:bottom-full::before {
  content: var(--tw-content);
  bottom: 100%;
}.dokan-layout .before\:left-0::before {
  content: var(--tw-content);
  left: 0px;
}.dokan-layout .before\:h-12::before {
  content: var(--tw-content);
  height: 3rem;
}.dokan-layout .before\:w-full::before {
  content: var(--tw-content);
  width: 100%;
}.dokan-layout .before\:content-\[\'\'\]::before {
  --tw-content: '';
  content: var(--tw-content);
}.dokan-layout :is(.\*\:first\:\*\:border-transparent > *:first-child > *) {
  border-color: transparent;
}.dokan-layout :is(.\*\:first\:border-gray-200:first-child > *) {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}.dokan-layout .last\:mb-0:last-child {
  margin-bottom: 0px;
}.dokan-layout .last\:border-b-0:last-child {
  border-bottom-width: 0px;
}.dokan-layout .hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}.dokan-layout .hover\:bg-blue-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}.dokan-layout .hover\:bg-indigo-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));
}.dokan-layout .hover\:bg-indigo-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}.dokan-layout .hover\:bg-transparent:hover {
  background-color: transparent;
}.dokan-layout .hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .hover\:bg-yellow-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}.dokan-layout .hover\:bg-opacity-85:hover {
  --tw-bg-opacity: 0.85;
}.dokan-layout .hover\:\!text-\[\#7047EB\]:hover {
  --tw-text-opacity: 1 !important;
  color: rgb(112 71 235 / var(--tw-text-opacity, 1)) !important;
}.dokan-layout .hover\:text-\[\#7047EB\]:hover {
  --tw-text-opacity: 1;
  color: rgb(112 71 235 / var(--tw-text-opacity, 1));
}.dokan-layout .hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}.dokan-layout .hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}.dokan-layout .hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}.dokan-layout .hover\:underline:hover {
  text-decoration-line: underline;
}.dokan-layout .focus\:border-none:focus {
  border-style: none;
}.dokan-layout .focus\:border-gray-300:focus {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}.dokan-layout .focus\:border-gray-900:focus {
  --tw-border-opacity: 1;
  border-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}.dokan-layout .focus\:border-orange-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}.dokan-layout .focus\:\!outline-none:focus {
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
}.dokan-layout .focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}.dokan-layout .focus\:outline-dokan-btn:focus {
  outline-color: var(--dokan-button-border-color, #7047EB);
}.dokan-layout .focus\:\!ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
}.dokan-layout .focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.dokan-layout .focus\:ring-gray-900:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(17 24 39 / var(--tw-ring-opacity, 1));
}.dokan-layout .focus\:ring-indigo-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));
}.dokan-layout .focus\:ring-orange-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity, 1));
}.dokan-layout .focus-visible\:outline:focus-visible {
  outline-style: solid;
}.dokan-layout .focus-visible\:outline-2:focus-visible {
  outline-width: 2px;
}.dokan-layout .focus-visible\:outline-offset-2:focus-visible {
  outline-offset: 2px;
}.dokan-layout .focus-visible\:outline-indigo-600:focus-visible {
  outline-color: #4f46e5;
}.dokan-layout .disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}.dokan-layout :is(.group:hover .group-hover\:\!bg-\[\#EFEAFF\]) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(239 234 255 / var(--tw-bg-opacity, 1)) !important;
}.dokan-layout :is(.group:hover .group-hover\:fill-\[\#7047EB\]) {
  fill: #7047EB;
}.dokan-layout .data-\[closed\]\:translate-y-1[data-closed] {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.dokan-layout .data-\[closed\]\:opacity-0[data-closed] {
  opacity: 0;
}.dokan-layout .data-\[enter\]\:duration-200[data-enter] {
  transition-duration: 200ms;
}.dokan-layout .data-\[leave\]\:duration-150[data-leave] {
  transition-duration: 150ms;
}.dokan-layout .data-\[enter\]\:ease-out[data-enter] {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}.dokan-layout .data-\[leave\]\:ease-in[data-leave] {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}@container main (min-width: 20rem) {

  .dokan-layout .\@xs\/main\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}@container currency (min-width: 24rem) {

  .dokan-layout .\@sm\/currency\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .dokan-layout .\@sm\/currency\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .dokan-layout .\@sm\/currency\:justify-end {
    justify-content: flex-end;
  }

  .dokan-layout .\@sm\/currency\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .dokan-layout .\@sm\/currency\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}@container (min-width: 24rem) {

  .dokan-layout .\@sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}@container combine (min-width: 28rem) {

  .dokan-layout .\@md\/combine\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .dokan-layout .\@md\/combine\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}@container radio (min-width: 28rem) {

  .dokan-layout .\@md\/radio\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .dokan-layout .\@md\/radio\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .dokan-layout .\@md\/radio\:justify-end {
    justify-content: flex-end;
  }
}@container step-body (min-width: 28rem) {

  .dokan-layout .\@md\/step-body\:p-12 {
    padding: 3rem;
  }
}@container switcher (min-width: 28rem) {

  .dokan-layout .\@md\/switcher\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .dokan-layout .\@md\/switcher\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .dokan-layout .\@md\/switcher\:justify-end {
    justify-content: flex-end;
  }
}@container (min-width: 28rem) {

  .dokan-layout .\@md\:gap-6 {
    gap: 1.5rem;
  }

  .dokan-layout .\@md\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}@container combine (min-width: 36rem) {

  .dokan-layout .\@xl\/combine\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .dokan-layout .\@xl\/combine\:col-span-7 {
    grid-column: span 7 / span 7;
  }
}@container main (min-width: 36rem) {

  .dokan-layout .\@xl\/main\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .dokan-layout .\@xl\/main\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .dokan-layout .\@xl\/main\:my-0 {
    margin-top: 0px;
    margin-bottom: 0px;
  }
}@container main (min-width: 42rem) {

  .dokan-layout .\@2xl\/main\:mb-10 {
    margin-bottom: 2.5rem;
  }
}@container main (min-width: 48rem) {

  .dokan-layout .\@3xl\/main\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .dokan-layout .\@3xl\/main\:col-span-9 {
    grid-column: span 9 / span 9;
  }

  .dokan-layout .\@3xl\/main\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }
}@container main (min-width: 64rem) {

  .dokan-layout .\@5xl\/main\:pl-12 {
    padding-left: 3rem;
  }
}@container step-body (min-width: 64rem) {

  .dokan-layout .\@5xl\/step-body\:px-28 {
    padding-left: 7rem;
    padding-right: 7rem;
  }

  .dokan-layout .\@5xl\/step-body\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}@media (min-width: 640px) {

  .dokan-layout .sm\:mb-0 {
    margin-bottom: 0px;
  }

  .dokan-layout .sm\:ml-4 {
    margin-left: 1rem;
  }

  .dokan-layout .sm\:mt-0 {
    margin-top: 0px;
  }

  .dokan-layout .sm\:block {
    display: block;
  }

  .dokan-layout .sm\:flex {
    display: flex;
  }

  .dokan-layout .sm\:hidden {
    display: none;
  }

  .dokan-layout .sm\:w-\[50rem\] {
    width: 50rem;
  }

  .dokan-layout .sm\:w-\[54rem\] {
    width: 54rem;
  }

  .dokan-layout .sm\:w-\[70\%\] {
    width: 70%;
  }

  .dokan-layout .sm\:w-auto {
    width: auto;
  }

  .dokan-layout .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .dokan-layout .sm\:flex-col {
    flex-direction: column;
  }

  .dokan-layout .sm\:items-start {
    align-items: flex-start;
  }

  .dokan-layout .sm\:\!items-center {
    align-items: center !important;
  }

  .dokan-layout .sm\:truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .dokan-layout .sm\:rounded-lg {
    border-radius: 0.5rem;
  }

  .dokan-layout .sm\:rounded-md {
    border-radius: 0.375rem;
  }

  .dokan-layout .sm\:p-6 {
    padding: 1.5rem;
  }

  .dokan-layout .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .dokan-layout .sm\:text-left {
    text-align: left;
  }

  .dokan-layout .sm\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .dokan-layout .sm\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .dokan-layout .sm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .dokan-layout .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .dokan-layout .sm\:tracking-tight {
    letter-spacing: -0.025em;
  }
}@media (min-width: 768px) {

  .dokan-layout .md\:mb-4 {
    margin-bottom: 1rem;
  }

  .dokan-layout .md\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .dokan-layout .md\:mb-8 {
    margin-bottom: 2rem;
  }

  .dokan-layout .md\:ml-4 {
    margin-left: 1rem;
  }

  .dokan-layout .md\:mt-0 {
    margin-top: 0px;
  }

  .dokan-layout .md\:flex {
    display: flex;
  }

  .dokan-layout .md\:h-\[33\.5rem\] {
    height: 33.5rem;
  }

  .dokan-layout .md\:\!w-1\/2 {
    width: 50% !important;
  }

  .dokan-layout .md\:w-1\/2 {
    width: 50%;
  }

  .dokan-layout .md\:w-\[1px\] {
    width: 1px;
  }

  .dokan-layout .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .dokan-layout .md\:\!flex-row {
    flex-direction: row !important;
  }

  .dokan-layout .md\:flex-row {
    flex-direction: row;
  }

  .dokan-layout .md\:items-center {
    align-items: center;
  }

  .dokan-layout .md\:\!justify-end {
    justify-content: flex-end !important;
  }

  .dokan-layout .md\:justify-between {
    justify-content: space-between;
  }

  .dokan-layout .md\:border-0 {
    border-width: 0px;
  }

  .dokan-layout .md\:bg-\[0px_20px\] {
    background-position: 0px 20px;
  }

  .dokan-layout .md\:bg-\[right_0px_bottom_-10px\] {
    background-position: right 0px bottom -10px;
  }

  .dokan-layout .md\:p-10 {
    padding: 2.5rem;
  }

  .dokan-layout .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .dokan-layout .md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .dokan-layout .md\:text-\[14px\] {
    font-size: 14px;
  }

  .dokan-layout .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .dokan-layout .md\:leading-\[1\.3\] {
    line-height: 1.3;
  }
}@media (min-width: 1024px) {

  .dokan-layout .lg\:col-span-12 {
    grid-column: span 12 / span 12;
  }

  .dokan-layout .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .dokan-layout .lg\:col-span-9 {
    grid-column: span 9 / span 9;
  }

  .dokan-layout .lg\:grid {
    display: grid;
  }

  .dokan-layout .lg\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .dokan-layout .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .dokan-layout .lg\:gap-6 {
    gap: 1.5rem;
  }

  .dokan-layout .lg\:gap-x-5 {
    -moz-column-gap: 1.25rem;
         column-gap: 1.25rem;
  }

  .dokan-layout .lg\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .dokan-layout .lg\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .dokan-layout .lg\:py-5 {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem;
  }

  .dokan-layout .lg\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}@media (min-width: 1280px) {

  .dokan-layout .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}@media (prefers-color-scheme: dark) {

  .dokan-layout .dark\:border-gray-700 {
    --tw-border-opacity: 1;
    border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
  }

  .dokan-layout .dark\:text-black {
    --tw-text-opacity: 1;
    color: rgb(0 0 0 / var(--tw-text-opacity, 1));
  }

  .dokan-layout .dark\:text-blue-500 {
    --tw-text-opacity: 1;
    color: rgb(59 130 246 / var(--tw-text-opacity, 1));
  }

  .dokan-layout .dark\:text-gray-400 {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity, 1));
  }

  .dokan-layout .dark\:text-gray-600 {
    --tw-text-opacity: 1;
    color: rgb(75 85 99 / var(--tw-text-opacity, 1));
  }
}.dokan-layout .\[\&\:\:-webkit-scrollbar-thumb\]\:rounded-full::-webkit-scrollbar-thumb {
  border-radius: 9999px;
}.dokan-layout .\[\&\:\:-webkit-scrollbar\]\:h-1::-webkit-scrollbar {
  height: 0.25rem;
}.dokan-layout .\[\&\:\:-webkit-scrollbar\]\:w-2::-webkit-scrollbar {
  width: 0.5rem;
}.dokan-layout :is(.\*\:\[\&\:not\(\:last-child\)\]\:\*\:border-b-2 > *:not(:last-child) > *) {
  border-bottom-width: 2px;
}.dokan-layout :is(.\[\&\:not\(\:last-child\)\]\:\*\:border-b > *:not(:last-child)) {
  border-bottom-width: 1px;
}.dokan-layout :is(.focus\:\*\:\[\&\:not\(\:last-child\)\]\:\*\:outline-transparent > *:not(:last-child) > *:focus) {
  outline-color: transparent;
}.dokan-layout :is(.\[\&_\*_p\]\:m-0 * p) {
  margin: 0px;
}
/**
 * Colors
 */
/**
 * Breakpoints & Media Queries
 */
/**
 * Colors
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 * For example, don't add rules to this sheet that affect block visuals. It's purely for UI.
 */
/**
 * Fonts & basic variables.
 */
/**
 * Typography
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Radius scale.
 */
/**
 * Elevation scale.
 */
/**
 * Dimensions.
 */
/**
 * Mobile specific styles
 */
/**
 * Editor styles.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
/**
 * Colors
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 * For example, don't add rules to this sheet that affect block visuals. It's purely for UI.
 */
/**
 * Fonts & basic variables.
 */
/**
 * Typography
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Radius scale.
 */
/**
 * Elevation scale.
 */
/**
 * Dimensions.
 */
/**
 * Mobile specific styles
 */
/**
 * Editor styles.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
/**
 * Breakpoints & Media Queries
 */
/**
*  Converts a hex value into the rgb equivalent.
*
* @param {string} hex - the hexadecimal value to convert
* @return {string} comma separated rgb values
*/
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * Typography
 */
/**
 * Breakpoint mixins
 */
/**
 * Focus styles.
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
/**
 * Allows users to opt-out of animations via OS-level preferences.
 */
/**
 * Reset default styles for JavaScript UI based pages.
 * This is a WP-admin agnostic reset
 */
/**
 * Reset the WP Admin page styles for Gutenberg-like pages.
 */
/**
 * Colors
 */
/**
 * SCSS Variables.
 *
 * Please use variables from this sheet to ensure consistency across the UI.
 * Don't add to this sheet unless you're pretty sure the value will be reused in many places.
 * For example, don't add rules to this sheet that affect block visuals. It's purely for UI.
 */
/**
 * Fonts & basic variables.
 */
/**
 * Typography
 */
/**
 * Grid System.
 * https://make.wordpress.org/design/2019/10/31/proposal-a-consistent-spacing-system-for-wordpress/
 */
/**
 * Radius scale.
 */
/**
 * Elevation scale.
 */
/**
 * Dimensions.
 */
/**
 * Mobile specific styles
 */
/**
 * Editor styles.
 */
/**
 * Block & Editor UI.
 */
/**
 * Block paddings.
 */
/**
 * React Native specific.
 * These variables do not appear to be used anywhere else.
 */
/**
 * Breakpoints & Media Queries
 */
/**
*  Converts a hex value into the rgb equivalent.
*
* @param {string} hex - the hexadecimal value to convert
* @return {string} comma separated rgb values
*/
/**
 * Long content fade mixin
 *
 * Creates a fading overlay to signify that the content is longer
 * than the space allows.
 */
/**
 * Typography
 */
/**
 * Breakpoint mixins
 */
/**
 * Focus styles.
 */
/**
 * Applies editor left position to the selector passed as argument
 */
/**
 * Styles that are reused verbatim in a few places
 */
/**
 * Allows users to opt-out of animations via OS-level preferences.
 */
/**
 * Reset default styles for JavaScript UI based pages.
 * This is a WP-admin agnostic reset
 */
/**
 * Reset the WP Admin page styles for Gutenberg-like pages.
 */
:root {
  --wp-block-synced-color: #7a00df;
  --wp-block-synced-color--rgb: 122, 0, 223;
  --wp-bound-block-color: var(--wp-block-synced-color);
  --wp-admin-theme-color: #007cba;
  --wp-admin-theme-color--rgb: 0, 124, 186;
  --wp-admin-theme-color-darker-10: #006ba1;
  --wp-admin-theme-color-darker-10--rgb: 0, 107, 161;
  --wp-admin-theme-color-darker-20: #005a87;
  --wp-admin-theme-color-darker-20--rgb: 0, 90, 135;
  --wp-admin-border-width-focus: 2px;
}
@media (min-resolution: 192dpi) {
  :root {
    --wp-admin-border-width-focus: 1.5px;
  }
}

.dataviews-wrapper {
  height: 100%;
  overflow: auto;
  box-sizing: border-box;
  scroll-padding-bottom: 64px;
  /* stylelint-disable-next-line property-no-unknown -- '@container' not globally permitted */
  container: dataviews-wrapper/inline-size;
  display: flex;
  flex-direction: column;
  font-size: 13px;
  line-height: 1.4;
}

.dataviews__view-actions,
.dataviews-filters__container {
  box-sizing: border-box;
  padding: 16px 48px;
  flex-shrink: 0;
  position: sticky;
  left: 0;
}
@media not (prefers-reduced-motion) {
  .dataviews__view-actions,
.dataviews-filters__container {
    transition: padding ease-out 0.1s;
  }
}

.dataviews-no-results,
.dataviews-loading {
  padding: 0 48px;
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
@media not (prefers-reduced-motion) {
  .dataviews-no-results,
.dataviews-loading {
    transition: padding ease-out 0.1s;
  }
}

@container (max-width: 430px) {
  .dataviews__view-actions,
.dataviews-filters__container {
    padding: 12px 24px;
  }
  .dataviews-no-results,
.dataviews-loading {
    padding-left: 24px;
    padding-right: 24px;
  }
}
.dataviews-title-field {
  font-size: 13px;
  font-weight: 500;
  color: #2f2f2f;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}
.dataviews-title-field a {
  text-decoration: none;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  display: block;
  flex-grow: 0;
  color: #2f2f2f;
}
.dataviews-title-field a:hover {
  color: var(--wp-admin-theme-color);
}
.dataviews-title-field a:focus {
  color: var(--wp-admin-theme-color--rgb);
  box-shadow: 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color, #007cba);
  border-radius: 2px;
}
.dataviews-title-field button.components-button.is-link {
  text-decoration: none;
  font-weight: inherit;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  display: block;
  width: 100%;
  color: #1e1e1e;
}
.dataviews-title-field button.components-button.is-link:hover {
  color: var(--wp-admin-theme-color);
}

.dataviews-title-field--clickable {
  cursor: pointer;
  color: #2f2f2f;
}
.dataviews-title-field--clickable:hover {
  color: var(--wp-admin-theme-color);
}
.dataviews-title-field--clickable:focus {
  color: var(--wp-admin-theme-color--rgb);
  box-shadow: 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color, #007cba);
  border-radius: 2px;
}

.dataviews-bulk-actions-footer__item-count {
  color: #1e1e1e;
  font-weight: 500;
  font-size: 11px;
  text-transform: uppercase;
}

.dataviews-bulk-actions-footer__container {
  margin-right: auto;
  min-height: 32px;
}

.dataviews-filters__button {
  position: relative;
}

.dataviews-filters__container {
  padding-top: 0;
}

.dataviews-filters__reset-button.dataviews-filters__reset-button[aria-disabled=true], .dataviews-filters__reset-button.dataviews-filters__reset-button[aria-disabled=true]:hover {
  opacity: 0;
}
.dataviews-filters__reset-button.dataviews-filters__reset-button[aria-disabled=true]:focus {
  opacity: 1;
}

.dataviews-filters__summary-popover {
  font-size: 13px;
  line-height: 1.4;
}
.dataviews-filters__summary-popover .components-popover__content {
  width: 230px;
  border-radius: 4px;
}
.dataviews-filters__summary-popover.components-dropdown__content .components-popover__content {
  padding: 0;
}

.dataviews-filters__summary-operators-container {
  padding: 8px 8px 0;
}
.dataviews-filters__summary-operators-container:has(+ .dataviews-filters__search-widget-listbox) {
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}
.dataviews-filters__summary-operators-container:empty {
  display: none;
}
.dataviews-filters__summary-operators-container .dataviews-filters__summary-operators-filter-name {
  color: #757575;
}

.dataviews-filters__summary-chip-container {
  position: relative;
  white-space: pre-wrap;
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip {
  border-radius: 16px;
  border: 1px solid transparent;
  cursor: pointer;
  padding: 4px 12px;
  min-height: 32px;
  background: #f0f0f0;
  color: #2f2f2f;
  position: relative;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip.has-reset {
  padding-inline-end: 28px;
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip:hover, .dataviews-filters__summary-chip-container .dataviews-filters__summary-chip:focus-visible, .dataviews-filters__summary-chip-container .dataviews-filters__summary-chip[aria-expanded=true] {
  background: #e0e0e0;
  color: #1e1e1e;
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip.has-values {
  color: var(--wp-admin-theme-color);
  background: rgba(var(--wp-admin-theme-color--rgb), 0.04);
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip.has-values:hover, .dataviews-filters__summary-chip-container .dataviews-filters__summary-chip.has-values[aria-expanded=true] {
  background: rgba(var(--wp-admin-theme-color--rgb), 0.12);
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip:focus-visible {
  outline: none;
  box-shadow: 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip .dataviews-filters-__summary-filter-text-name {
  font-weight: 500;
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip-remove {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 0;
  padding: 0;
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  cursor: pointer;
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip-remove svg {
  fill: #757575;
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip-remove:hover, .dataviews-filters__summary-chip-container .dataviews-filters__summary-chip-remove:focus {
  background: #e0e0e0;
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip-remove:hover svg, .dataviews-filters__summary-chip-container .dataviews-filters__summary-chip-remove:focus svg {
  fill: #1e1e1e;
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip-remove.has-values svg {
  fill: var(--wp-admin-theme-color);
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip-remove.has-values:hover {
  background: rgba(var(--wp-admin-theme-color--rgb), 0.08);
}
.dataviews-filters__summary-chip-container .dataviews-filters__summary-chip-remove:focus-visible {
  outline: none;
  box-shadow: 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
}

.dataviews-filters__search-widget-filter-combobox-list {
  max-height: 184px;
  padding: 4px;
  overflow: auto;
  border-top: 1px solid #e0e0e0;
}
.dataviews-filters__search-widget-filter-combobox-list .dataviews-filters__search-widget-filter-combobox-item-value [data-user-value] {
  font-weight: 600;
}

.dataviews-filters__search-widget-listbox {
  padding: 4px;
  overflow: auto;
}

.dataviews-filters__search-widget-listitem {
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 2px;
  box-sizing: border-box;
  padding: 8px 12px;
  cursor: default;
  margin-block-end: 2px;
}
.dataviews-filters__search-widget-listitem:last-child {
  margin-block-end: 0;
}
.dataviews-filters__search-widget-listitem:hover, .dataviews-filters__search-widget-listitem[data-active-item], .dataviews-filters__search-widget-listitem:focus {
  background-color: var(--wp-admin-theme-color);
  color: #fff;
}
.dataviews-filters__search-widget-listitem:hover .dataviews-filters__search-widget-listitem-check, .dataviews-filters__search-widget-listitem[data-active-item] .dataviews-filters__search-widget-listitem-check, .dataviews-filters__search-widget-listitem:focus .dataviews-filters__search-widget-listitem-check {
  fill: #fff;
}
.dataviews-filters__search-widget-listitem:hover .dataviews-filters__search-widget-listitem-description, .dataviews-filters__search-widget-listitem[data-active-item] .dataviews-filters__search-widget-listitem-description, .dataviews-filters__search-widget-listitem:focus .dataviews-filters__search-widget-listitem-description {
  color: #fff;
}
.dataviews-filters__search-widget-listitem .dataviews-filters__search-widget-listitem-check {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}
.dataviews-filters__search-widget-listitem .dataviews-filters__search-widget-listitem-description {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
  line-height: 16px;
  color: #757575;
}

.dataviews-filters__search-widget-filter-combobox__wrapper {
  position: relative;
  padding: 8px;
}
.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  padding: 6px 8px;
  /* Fonts smaller than 16px causes mobile safari to zoom. */
  /* Override core line-height. To be reviewed. */
  line-height: normal;
  box-shadow: 0 0 0 transparent;
  border-radius: 2px;
  border: 1px solid #949494;
  display: block;
  padding: 0 32px 0 8px;
  background: #f0f0f0;
  border: none;
  width: 100%;
  height: 32px;
  margin-left: 0;
  margin-right: 0;
  /* Fonts smaller than 16px causes mobile safari to zoom. */
  font-size: 16px;
}
@media not (prefers-reduced-motion) {
  .dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input {
    transition: box-shadow 0.1s linear;
  }
}
@media (min-width: 600px) {
  .dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input {
    font-size: 13px;
    /* Override core line-height. To be reviewed. */
    line-height: normal;
  }
}
.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input:focus {
  border-color: var(--wp-admin-theme-color);
  box-shadow: 0 0 0 0.5px var(--wp-admin-theme-color);
  outline: 2px solid transparent;
}
.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input::-webkit-input-placeholder {
  color: rgba(30, 30, 30, 0.62);
}
.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input::-moz-placeholder {
  color: rgba(30, 30, 30, 0.62);
}
.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input:-ms-input-placeholder {
  color: rgba(30, 30, 30, 0.62);
}
@media (min-width: 600px) {
  .dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input {
    font-size: 13px;
  }
}
.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input:focus {
  background: #fff;
  box-shadow: inset 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
}
.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input::-moz-placeholder {
  color: #757575;
}
.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input::placeholder {
  color: #757575;
}
.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input::-webkit-search-decoration, .dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input::-webkit-search-cancel-button, .dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input::-webkit-search-results-button, .dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__input::-webkit-search-results-decoration {
  -webkit-appearance: none;
}
.dataviews-filters__search-widget-filter-combobox__wrapper .dataviews-filters__search-widget-filter-combobox__icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
}

.dataviews-filters__container-visibility-toggle {
  position: relative;
  flex-shrink: 0;
}

.dataviews-filters-toggle__count {
  position: absolute;
  top: 0;
  right: 0;
  transform: translate(50%, -50%);
  background: var(--wp-admin-theme-color, #3858e9);
  height: 16px;
  min-width: 16px;
  line-height: 16px;
  padding: 0 4px;
  text-align: center;
  border-radius: 8px;
  font-size: 11px;
  outline: var(--wp-admin-border-width-focus) solid #fff;
  color: #fff;
  box-sizing: border-box;
}

.dataviews-search {
  width: -moz-fit-content;
  width: fit-content;
}

.dataviews-footer {
  position: sticky;
  bottom: 0;
  left: 0;
  background-color: #fff;
  padding: 12px 48px;
  border-top: 1px solid #f0f0f0;
  flex-shrink: 0;
  z-index: 2;
}
@media not (prefers-reduced-motion) {
  .dataviews-footer {
    transition: padding ease-out 0.1s;
  }
}

@container (max-width: 430px) {
  .dataviews-footer {
    padding: 12px 24px;
  }
}
@container (max-width: 560px) {
  .dataviews-footer {
    flex-direction: column !important;
  }
  .dataviews-footer .dataviews-bulk-actions-footer__container {
    width: 100%;
  }
  .dataviews-footer .dataviews-bulk-actions-footer__item-count {
    flex-grow: 1;
  }
  .dataviews-footer .dataviews-pagination {
    width: 100%;
    justify-content: space-between;
  }
}
.dataviews-pagination__page-select {
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}
@media (min-width: 600px) {
  .dataviews-pagination__page-select .components-select-control__input {
    font-size: 11px !important;
    font-weight: 500;
  }
}

.dataviews-action-modal {
  z-index: 1000001;
}

.dataviews-selection-checkbox {
  --checkbox-input-size: 24px;
  line-height: 0;
  flex-shrink: 0;
}
@media (min-width: 600px) {
  .dataviews-selection-checkbox {
    --checkbox-input-size: 16px;
  }
}
.dataviews-selection-checkbox .components-checkbox-control__input-container {
  margin: 0;
}

.dataviews-view-config {
  width: 320px;
  /* stylelint-disable-next-line property-no-unknown -- the linter needs to be updated to accepted the container-type property */
  container-type: inline-size;
  font-size: 13px;
  line-height: 1.4;
}

.dataviews-config__popover.is-expanded .dataviews-config__popover-content-wrapper {
  overflow-y: scroll;
  height: 100%;
}
.dataviews-config__popover.is-expanded .dataviews-config__popover-content-wrapper .dataviews-view-config {
  width: auto;
}

.dataviews-view-config__sort-direction .components-toggle-group-control-option-base {
  text-transform: uppercase;
}

.dataviews-settings-section__title.dataviews-settings-section__title {
  line-height: 24px;
  font-size: 15px;
}

.dataviews-settings-section__sidebar {
  grid-column: span 4;
}

.dataviews-settings-section__content,
.dataviews-settings-section__content > * {
  grid-column: span 8;
}

.dataviews-settings-section__content .is-divided-in-two {
  display: contents;
}
.dataviews-settings-section__content .is-divided-in-two > * {
  grid-column: span 4;
}

.dataviews-settings-section:has(.dataviews-settings-section__content:empty) {
  display: none;
}

@container (max-width: 500px) {
  .dataviews-settings-section.dataviews-settings-section {
    grid-template-columns: repeat(2, 1fr);
  }
  .dataviews-settings-section.dataviews-settings-section .dataviews-settings-section__sidebar {
    grid-column: span 2;
  }
  .dataviews-settings-section.dataviews-settings-section .dataviews-settings-section__content {
    grid-column: span 2;
  }
}
.dataviews-field-control__field {
  height: 32px;
}

.dataviews-field-control__actions {
  position: absolute;
  top: -9999em;
}

.dataviews-field-control__actions.dataviews-field-control__actions {
  gap: 4px;
}

.dataviews-field-control__field:hover .dataviews-field-control__actions,
.dataviews-field-control__field:focus-within .dataviews-field-control__actions,
.dataviews-field-control__field.is-interacting .dataviews-field-control__actions {
  position: unset;
  top: unset;
}

.dataviews-field-control__icon {
  display: flex;
  width: 24px;
}

.dataviews-field-control__label-sub-label-container {
  flex-grow: 1;
}

.dataviews-field-control__label {
  display: block;
}

.dataviews-field-control__sub-label {
  margin-top: 8px;
  margin-bottom: 0;
  font-size: 11px;
  font-style: normal;
  color: #757575;
}

.dataviews-view-grid {
  margin-bottom: auto;
  grid-template-rows: max-content;
  padding: 0 48px 24px;
  container-type: inline-size;
}
@media not (prefers-reduced-motion) {
  .dataviews-view-grid {
    transition: padding ease-out 0.1s;
  }
}
.dataviews-view-grid .dataviews-view-grid__card {
  height: 100%;
  justify-content: flex-start;
  position: relative;
}
.dataviews-view-grid .dataviews-view-grid__card .dataviews-view-grid__title-actions {
  padding: 8px 0 4px;
}
.dataviews-view-grid .dataviews-view-grid__card .dataviews-view-grid__title-field {
  min-height: 24px;
  display: flex;
  align-items: center;
}
.dataviews-view-grid .dataviews-view-grid__card .dataviews-view-grid__title-field--clickable {
  width: -moz-fit-content;
  width: fit-content;
}
.dataviews-view-grid .dataviews-view-grid__card.is-selected .dataviews-view-grid__fields .dataviews-view-grid__field .dataviews-view-grid__field-value {
  color: #1e1e1e;
}
.dataviews-view-grid .dataviews-view-grid__card.is-selected .dataviews-view-grid__media::after,
.dataviews-view-grid .dataviews-view-grid__card .dataviews-view-grid__media:focus::after {
  background-color: rgba(var(--wp-admin-theme-color--rgb), 0.08);
}
.dataviews-view-grid .dataviews-view-grid__card.is-selected .dataviews-view-grid__media::after {
  box-shadow: inset 0 0 0 1px var(--wp-admin-theme-color);
}
.dataviews-view-grid .dataviews-view-grid__card .dataviews-view-grid__media:focus::after {
  box-shadow: inset 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
}
.dataviews-view-grid .dataviews-view-grid__media {
  width: 100%;
  min-height: 200px;
  aspect-ratio: 1/1;
  background-color: #f0f0f0;
  border-radius: 4px;
  position: relative;
}
.dataviews-view-grid .dataviews-view-grid__media img {
  -o-object-fit: cover;
     object-fit: cover;
  width: 100%;
  height: 100%;
}
.dataviews-view-grid .dataviews-view-grid__media::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  pointer-events: none;
}
.dataviews-view-grid .dataviews-view-grid__fields {
  position: relative;
  font-size: 12px;
  line-height: 16px;
}
.dataviews-view-grid .dataviews-view-grid__fields:not(:empty) {
  padding: 0 0 12px;
}
.dataviews-view-grid .dataviews-view-grid__fields .dataviews-view-grid__field-value:not(:empty) {
  min-height: 24px;
  line-height: 20px;
  padding-top: 2px;
}
.dataviews-view-grid .dataviews-view-grid__fields .dataviews-view-grid__field {
  min-height: 24px;
  align-items: center;
}
.dataviews-view-grid .dataviews-view-grid__fields .dataviews-view-grid__field .dataviews-view-grid__field-name {
  width: 35%;
  color: #757575;
}
.dataviews-view-grid .dataviews-view-grid__fields .dataviews-view-grid__field .dataviews-view-grid__field-value {
  width: 65%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.dataviews-view-grid .dataviews-view-grid__fields .dataviews-view-grid__field:not(:has(.dataviews-view-grid__field-value:not(:empty))) {
  display: none;
}
.dataviews-view-grid .dataviews-view-grid__badge-fields:not(:empty) {
  padding-bottom: 12px;
}

.dataviews-view-grid.dataviews-view-grid {
  /**
   * Breakpoints were adjusted from media queries breakpoints to account for
   * the sidebar width. This was done to match the existing styles we had.
   */
}
@container (max-width: 480px) {
  .dataviews-view-grid.dataviews-view-grid {
    grid-template-columns: repeat(1, minmax(0, 1fr));
    padding-left: 24px;
    padding-right: 24px;
  }
}
@container (min-width: 480px) {
  .dataviews-view-grid.dataviews-view-grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
@container (min-width: 780px) {
  .dataviews-view-grid.dataviews-view-grid {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}
@container (min-width: 1140px) {
  .dataviews-view-grid.dataviews-view-grid {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
@container (min-width: 1520px) {
  .dataviews-view-grid.dataviews-view-grid {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
}

.dataviews-view-grid__field-value:empty,
.dataviews-view-grid__field:empty {
  display: none;
}

.dataviews-view-grid__card .dataviews-selection-checkbox {
  position: absolute;
  top: -9999em;
  left: 8px;
  z-index: 1;
}
@media (hover: none) {
  .dataviews-view-grid__card .dataviews-selection-checkbox {
    top: 8px;
  }
}

.dataviews-view-grid__card:hover .dataviews-selection-checkbox,
.dataviews-view-grid__card:focus-within .dataviews-selection-checkbox,
.dataviews-view-grid__card.is-selected .dataviews-selection-checkbox {
  top: 8px;
}

.dataviews-view-grid__media--clickable {
  cursor: pointer;
}

div.dataviews-view-list {
  list-style-type: none;
}

.dataviews-view-list {
  margin: 0 0 auto;
}
.dataviews-view-list div[role=row] {
  margin: 0;
  border-top: 1px solid #f0f0f0;
}
.dataviews-view-list div[role=row] .dataviews-view-list__item-wrapper {
  position: relative;
  padding: 16px 24px;
  box-sizing: border-box;
}
.dataviews-view-list div[role=row] .dataviews-view-list__item-actions {
  display: flex;
  width: -moz-max-content;
  width: max-content;
  flex: 0 0 auto;
  gap: 4px;
}
.dataviews-view-list div[role=row] .dataviews-view-list__item-actions .components-button {
  position: relative;
  z-index: 1;
}
.dataviews-view-list div[role=row] .dataviews-view-list__item-actions > div {
  height: 24px;
}
.dataviews-view-list div[role=row] .dataviews-view-list__item-actions > :not(:last-child) {
  flex: 0;
  overflow: hidden;
  width: 0;
}
.dataviews-view-list div[role=row]:where(.is-selected, .is-hovered, :focus-within) .dataviews-view-list__item-actions > :not(:last-child) {
  flex-basis: min-content;
  width: auto;
  overflow: unset;
}
@media (hover: none) {
  .dataviews-view-list div[role=row] .dataviews-view-list__item-actions > :not(:last-child) {
    flex-basis: min-content;
    width: auto;
    overflow: unset;
  }
}
.dataviews-view-list div[role=row].is-selected.is-selected {
  border-top: 1px solid rgba(var(--wp-admin-theme-color--rgb), 0.12);
}
.dataviews-view-list div[role=row].is-selected.is-selected + div[role=row] {
  border-top: 1px solid rgba(var(--wp-admin-theme-color--rgb), 0.12);
}
.dataviews-view-list div[role=row]:not(.is-selected) .dataviews-view-list__title-field {
  color: #1e1e1e;
}
.dataviews-view-list div[role=row]:not(.is-selected):hover, .dataviews-view-list div[role=row]:not(.is-selected).is-hovered, .dataviews-view-list div[role=row]:not(.is-selected):focus-within {
  color: var(--wp-admin-theme-color);
  background-color: #f8f8f8;
}
.dataviews-view-list div[role=row]:not(.is-selected):hover .dataviews-view-list__title-field,
.dataviews-view-list div[role=row]:not(.is-selected):hover .dataviews-view-list__fields, .dataviews-view-list div[role=row]:not(.is-selected).is-hovered .dataviews-view-list__title-field,
.dataviews-view-list div[role=row]:not(.is-selected).is-hovered .dataviews-view-list__fields, .dataviews-view-list div[role=row]:not(.is-selected):focus-within .dataviews-view-list__title-field,
.dataviews-view-list div[role=row]:not(.is-selected):focus-within .dataviews-view-list__fields {
  color: var(--wp-admin-theme-color);
}
.dataviews-view-list div[role=row].is-selected .dataviews-view-list__item-wrapper,
.dataviews-view-list div[role=row].is-selected:focus-within .dataviews-view-list__item-wrapper {
  background-color: rgba(var(--wp-admin-theme-color--rgb), 0.04);
  color: #1e1e1e;
}
.dataviews-view-list div[role=row].is-selected .dataviews-view-list__item-wrapper .dataviews-view-list__title-field,
.dataviews-view-list div[role=row].is-selected .dataviews-view-list__item-wrapper .dataviews-view-list__fields,
.dataviews-view-list div[role=row].is-selected:focus-within .dataviews-view-list__item-wrapper .dataviews-view-list__title-field,
.dataviews-view-list div[role=row].is-selected:focus-within .dataviews-view-list__item-wrapper .dataviews-view-list__fields {
  color: var(--wp-admin-theme-color);
}
.dataviews-view-list .dataviews-view-list__item {
  position: absolute;
  z-index: 1;
  inset: 0;
  scroll-margin: 8px 0;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border: none;
  background: none;
  padding: 0;
  cursor: pointer;
}
.dataviews-view-list .dataviews-view-list__item:focus-visible {
  outline: none;
}
.dataviews-view-list .dataviews-view-list__item:focus-visible::before {
  position: absolute;
  content: "";
  inset: var(--wp-admin-border-width-focus);
  box-shadow: inset 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  border-radius: 2px;
  outline: 2px solid transparent;
}
.dataviews-view-list .dataviews-view-list__title-field {
  flex: 1;
  min-height: 24px;
  line-height: 24px;
  overflow: hidden;
}
.dataviews-view-list .dataviews-view-list__title-field:has(a, button) {
  z-index: 1;
}
.dataviews-view-list .dataviews-view-list__media-wrapper {
  width: 52px;
  height: 52px;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
  background-color: #f0f0f0;
  border-radius: 4px;
}
.dataviews-view-list .dataviews-view-list__media-wrapper img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.dataviews-view-list .dataviews-view-list__media-wrapper::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}
.dataviews-view-list .dataviews-view-list__field-wrapper {
  min-height: 52px;
  flex-grow: 1;
}
.dataviews-view-list .dataviews-view-list__fields {
  color: #757575;
  display: flex;
  gap: 12px;
  row-gap: 4px;
  flex-wrap: wrap;
  font-size: 12px;
}
.dataviews-view-list .dataviews-view-list__fields:empty {
  display: none;
}
.dataviews-view-list .dataviews-view-list__fields .dataviews-view-list__field:has(.dataviews-view-list__field-value:empty) {
  display: none;
}
.dataviews-view-list .dataviews-view-list__fields .dataviews-view-list__field-value {
  min-height: 24px;
  line-height: 20px;
  display: flex;
  align-items: center;
}
.dataviews-view-list + .dataviews-pagination {
  justify-content: space-between;
}

.dataviews-view-table {
  width: 100%;
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
  position: relative;
  color: #757575;
  margin-bottom: auto;
}
.dataviews-view-table th {
  text-align: left;
  color: #1e1e1e;
  font-weight: normal;
  font-size: 13px;
}
.dataviews-view-table td,
.dataviews-view-table th {
  padding: 12px;
  white-space: nowrap;
}
.dataviews-view-table td.dataviews-view-table__actions-column,
.dataviews-view-table th.dataviews-view-table__actions-column {
  text-align: right;
}
.dataviews-view-table td.dataviews-view-table__checkbox-column,
.dataviews-view-table th.dataviews-view-table__checkbox-column {
  padding-right: 0;
  width: 1%;
}
.dataviews-view-table tr {
  border-top: 1px solid #f0f0f0;
}
.dataviews-view-table tr .dataviews-view-table-header-button {
  gap: 4px;
}
.dataviews-view-table tr td:first-child,
.dataviews-view-table tr th:first-child {
  padding-left: 48px;
}
.dataviews-view-table tr td:first-child .dataviews-view-table-header-button,
.dataviews-view-table tr th:first-child .dataviews-view-table-header-button {
  margin-left: -8px;
}
.dataviews-view-table tr td:last-child,
.dataviews-view-table tr th:last-child {
  padding-right: 48px;
}
.dataviews-view-table tr:last-child {
  border-bottom: 0;
}
.dataviews-view-table tr.is-hovered {
  background-color: #f8f8f8;
}
.dataviews-view-table tr .components-checkbox-control__input.components-checkbox-control__input {
  opacity: 0;
}
.dataviews-view-table tr .components-checkbox-control__input.components-checkbox-control__input:checked, .dataviews-view-table tr .components-checkbox-control__input.components-checkbox-control__input:indeterminate, .dataviews-view-table tr .components-checkbox-control__input.components-checkbox-control__input:focus {
  opacity: 1;
}
.dataviews-view-table tr .dataviews-item-actions .components-button:not(.dataviews-all-actions-button) {
  opacity: 0;
}
.dataviews-view-table tr:focus-within .components-checkbox-control__input,
.dataviews-view-table tr:focus-within .dataviews-item-actions .components-button:not(.dataviews-all-actions-button), .dataviews-view-table tr.is-hovered .components-checkbox-control__input,
.dataviews-view-table tr.is-hovered .dataviews-item-actions .components-button:not(.dataviews-all-actions-button), .dataviews-view-table tr:hover .components-checkbox-control__input,
.dataviews-view-table tr:hover .dataviews-item-actions .components-button:not(.dataviews-all-actions-button) {
  opacity: 1;
}
@media (hover: none) {
  .dataviews-view-table tr .components-checkbox-control__input.components-checkbox-control__input,
.dataviews-view-table tr .dataviews-item-actions .components-button:not(.dataviews-all-actions-button) {
    opacity: 1;
  }
}
.dataviews-view-table tr.is-selected {
  background-color: rgba(var(--wp-admin-theme-color--rgb), 0.04);
  color: #757575;
}
.dataviews-view-table tr.is-selected, .dataviews-view-table tr.is-selected + tr {
  border-top: 1px solid rgba(var(--wp-admin-theme-color--rgb), 0.12);
}
.dataviews-view-table tr.is-selected:hover {
  background-color: rgba(var(--wp-admin-theme-color--rgb), 0.08);
}
.dataviews-view-table thead {
  position: sticky;
  inset-block-start: 0;
  z-index: 1;
}
.dataviews-view-table thead tr {
  border: 0;
}
.dataviews-view-table thead th {
  background-color: #fff;
  padding-top: 8px;
  padding-bottom: 8px;
  padding-left: 12px;
  font-size: 11px;
  text-transform: uppercase;
  font-weight: 500;
}
.dataviews-view-table thead th:has(.dataviews-view-table-header-button):not(:first-child) {
  padding-left: 4px;
}
.dataviews-view-table tbody td {
  vertical-align: top;
}
.dataviews-view-table tbody .dataviews-view-table__cell-content-wrapper {
  min-height: 32px;
  display: flex;
  align-items: center;
}
.dataviews-view-table tbody .components-v-stack > .dataviews-view-table__cell-content-wrapper:not(:first-child) {
  min-height: 0;
}
.dataviews-view-table .dataviews-view-table-header-button {
  padding: 4px 8px;
  font-size: 11px;
  text-transform: uppercase;
  font-weight: 500;
}
.dataviews-view-table .dataviews-view-table-header-button:not(:hover) {
  color: #1e1e1e;
}
.dataviews-view-table .dataviews-view-table-header-button span {
  speak: none;
}
.dataviews-view-table .dataviews-view-table-header-button span:empty {
  display: none;
}
.dataviews-view-table .dataviews-view-table-header {
  padding-left: 4px;
}
.dataviews-view-table .dataviews-view-table__actions-column {
  width: 1%;
}
.dataviews-view-table:has(tr.is-selected) .components-checkbox-control__input {
  opacity: 1;
}
.dataviews-view-table.has-compact-density thead th:has(.dataviews-view-table-header-button):not(:first-child) {
  padding-left: 0;
}
.dataviews-view-table.has-compact-density td,
.dataviews-view-table.has-compact-density th {
  padding: 4px 8px;
}
.dataviews-view-table.has-comfortable-density td,
.dataviews-view-table.has-comfortable-density th {
  padding: 16px 12px;
}
.dataviews-view-table.has-compact-density td.dataviews-view-table__checkbox-column,
.dataviews-view-table.has-compact-density th.dataviews-view-table__checkbox-column, .dataviews-view-table.has-comfortable-density td.dataviews-view-table__checkbox-column,
.dataviews-view-table.has-comfortable-density th.dataviews-view-table__checkbox-column {
  padding-right: 0;
}

@container (max-width: 430px) {
  .dataviews-view-table tr td:first-child,
.dataviews-view-table tr th:first-child {
    padding-left: 24px;
  }
  .dataviews-view-table tr td:last-child,
.dataviews-view-table tr th:last-child {
    padding-right: 24px;
  }
}
.dataviews-view-table-selection-checkbox {
  --checkbox-input-size: 24px;
}
@media (min-width: 600px) {
  .dataviews-view-table-selection-checkbox {
    --checkbox-input-size: 16px;
  }
}

.dataviews-column-primary__media {
  max-width: 60px;
}

.dataviews-controls__datetime {
  border: none;
  padding: 0;
}

.dataforms-layouts-panel__field {
  width: 100%;
  min-height: 32px;
  justify-content: flex-start !important;
  align-items: flex-start !important;
}

.dataforms-layouts-panel__field-label {
  width: 38%;
  flex-shrink: 0;
  min-height: 32px;
  display: flex;
  align-items: center;
  line-height: 20px;
  -webkit-hyphens: auto;
          hyphens: auto;
  align-self: center;
}

.dataforms-layouts-panel__field-control {
  flex-grow: 1;
  min-height: 32px;
  display: flex;
  align-items: center;
}
.dataforms-layouts-panel__field-control .components-button {
  max-width: 100%;
  text-align: left;
  white-space: normal;
  text-wrap: balance;
  text-wrap: pretty;
  min-height: 32px;
}
.dataforms-layouts-panel__field-control .components-dropdown {
  max-width: 100%;
}

.dataforms-layouts-panel__field-dropdown .components-popover__content {
  min-width: 320px;
  padding: 16px;
}

.dataforms-layouts-panel__dropdown-header {
  margin-bottom: 16px;
}

.components-popover.components-dropdown__content.dataforms-layouts-panel__field-dropdown {
  z-index: 159990;
}

.dataforms-layouts-regular__field {
  width: 100%;
  min-height: 32px;
  justify-content: flex-start !important;
  align-items: flex-start !important;
}

.dataforms-layouts-regular__field .components-base-control__label {
  font-size: inherit;
  font-weight: normal;
  text-transform: none;
}

.dataforms-layouts-regular__field-label {
  width: 38%;
  flex-shrink: 0;
  min-height: 32px;
  display: flex;
  align-items: center;
  line-height: 20px;
  -webkit-hyphens: auto;
          hyphens: auto;
  align-self: center;
}

.dataforms-layouts-regular__field-control {
  flex-grow: 1;
  min-height: 32px;
  display: flex;
  align-items: center;
}
.dokan-layout .dokan-dashboard-datatable .dataviews-wrapper{height:auto}.dokan-layout .dokan-dashboard-datatable .dataviews-wrapper .dataviews__view-actions{display:none}.dokan-layout .dokan-dashboard-datatable .dataviews-wrapper .components-button.is-compact.has-icon:not(.has-text){width:auto;padding:6px}.dokan-layout .dokan-dashboard-datatable .dataviews-wrapper .components-button.is-compact.has-icon:not(.has-text):hover,.dokan-layout .dokan-dashboard-datatable .dataviews-wrapper .components-button.is-compact.has-icon:not(.has-text):focus{background-color:rgba(0,0,0,0);box-shadow:none;border:none}
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*:where(.dokan-layout,.dokan-layout *),
:where(.dokan-layout,.dokan-layout *)::before,
:where(.dokan-layout,.dokan-layout *)::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

:where(.dokan-layout,.dokan-layout *)::before,
:where(.dokan-layout,.dokan-layout *)::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

.dokan-layout {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

.dokan-layout {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr:where(.dokan-layout,.dokan-layout *) {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]):where(.dokan-layout,.dokan-layout *) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1:where(.dokan-layout,.dokan-layout *),
h2:where(.dokan-layout,.dokan-layout *),
h3:where(.dokan-layout,.dokan-layout *),
h4:where(.dokan-layout,.dokan-layout *),
h5:where(.dokan-layout,.dokan-layout *),
h6:where(.dokan-layout,.dokan-layout *) {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a:where(.dokan-layout,.dokan-layout *) {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b:where(.dokan-layout,.dokan-layout *),
strong:where(.dokan-layout,.dokan-layout *) {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code:where(.dokan-layout,.dokan-layout *),
kbd:where(.dokan-layout,.dokan-layout *),
samp:where(.dokan-layout,.dokan-layout *),
pre:where(.dokan-layout,.dokan-layout *) {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small:where(.dokan-layout,.dokan-layout *) {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub:where(.dokan-layout,.dokan-layout *),
sup:where(.dokan-layout,.dokan-layout *) {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub:where(.dokan-layout,.dokan-layout *) {
  bottom: -0.25em;
}

sup:where(.dokan-layout,.dokan-layout *) {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table:where(.dokan-layout,.dokan-layout *) {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button:where(.dokan-layout,.dokan-layout *),
input:where(.dokan-layout,.dokan-layout *),
optgroup:where(.dokan-layout,.dokan-layout *),
select:where(.dokan-layout,.dokan-layout *),
textarea:where(.dokan-layout,.dokan-layout *) {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button:where(.dokan-layout,.dokan-layout *),
select:where(.dokan-layout,.dokan-layout *) {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button:where(.dokan-layout,.dokan-layout *),
input:where([type='button']):where(.dokan-layout,.dokan-layout *),
input:where([type='reset']):where(.dokan-layout,.dokan-layout *),
input:where([type='submit']):where(.dokan-layout,.dokan-layout *) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring:where(.dokan-layout,.dokan-layout *) {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid:where(.dokan-layout,.dokan-layout *) {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress:where(.dokan-layout,.dokan-layout *) {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

:where(.dokan-layout,.dokan-layout *) ::-webkit-inner-spin-button,
:where(.dokan-layout,.dokan-layout *) ::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search']:where(.dokan-layout,.dokan-layout *) {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

:where(.dokan-layout,.dokan-layout *) ::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

:where(.dokan-layout,.dokan-layout *) ::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary:where(.dokan-layout,.dokan-layout *) {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote:where(.dokan-layout,.dokan-layout *),
dl:where(.dokan-layout,.dokan-layout *),
dd:where(.dokan-layout,.dokan-layout *),
h1:where(.dokan-layout,.dokan-layout *),
h2:where(.dokan-layout,.dokan-layout *),
h3:where(.dokan-layout,.dokan-layout *),
h4:where(.dokan-layout,.dokan-layout *),
h5:where(.dokan-layout,.dokan-layout *),
h6:where(.dokan-layout,.dokan-layout *),
hr:where(.dokan-layout,.dokan-layout *),
figure:where(.dokan-layout,.dokan-layout *),
p:where(.dokan-layout,.dokan-layout *),
pre:where(.dokan-layout,.dokan-layout *) {
  margin: 0;
}

fieldset:where(.dokan-layout,.dokan-layout *) {
  margin: 0;
  padding: 0;
}

legend:where(.dokan-layout,.dokan-layout *) {
  padding: 0;
}

ol:where(.dokan-layout,.dokan-layout *),
ul:where(.dokan-layout,.dokan-layout *),
menu:where(.dokan-layout,.dokan-layout *) {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog:where(.dokan-layout,.dokan-layout *) {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea:where(.dokan-layout,.dokan-layout *) {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

:where(.dokan-layout,.dokan-layout *) input::-moz-placeholder, :where(.dokan-layout,.dokan-layout *) textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

:where(.dokan-layout,.dokan-layout *) input::placeholder,
:where(.dokan-layout,.dokan-layout *) textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button:where(.dokan-layout,.dokan-layout *),
[role="button"]:where(.dokan-layout,.dokan-layout *) {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled:where(.dokan-layout,.dokan-layout *) {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img:where(.dokan-layout,.dokan-layout *),
svg:where(.dokan-layout,.dokan-layout *),
video:where(.dokan-layout,.dokan-layout *),
canvas:where(.dokan-layout,.dokan-layout *),
audio:where(.dokan-layout,.dokan-layout *),
iframe:where(.dokan-layout,.dokan-layout *),
embed:where(.dokan-layout,.dokan-layout *),
object:where(.dokan-layout,.dokan-layout *) {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img:where(.dokan-layout,.dokan-layout *),
video:where(.dokan-layout,.dokan-layout *) {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])):where(.dokan-layout,.dokan-layout *) {
  display: none;
}

[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #2563eb;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  color: #6b7280;
  opacity: 1;
}

input::placeholder,textarea::placeholder {
  color: #6b7280;
  opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

::-webkit-date-and-time-value {
  min-height: 1.5em;
  text-align: inherit;
}

::-webkit-datetime-edit {
  display: inline-flex;
}

::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {
  padding-top: 0;
  padding-bottom: 0;
}

select {
  background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 20 20%27%3e%3cpath stroke=%27%236b7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%271.5%27 d=%27M6 8l4 4 4-4%27/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[multiple],[size]:where(select:not([size="1"])) {
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: unset;
          print-color-adjust: unset;
}

[type='checkbox'],[type='radio'] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #2563eb;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

[type='checkbox'] {
  border-radius: 0px;
}

[type='radio'] {
  border-radius: 100%;
}

[type='checkbox']:focus,[type='radio']:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

[type='checkbox']:checked,[type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {

  [type='checkbox']:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {

  [type='radio']:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='checkbox']:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 16%27%3e%3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27M4 8h8%27/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@media (forced-colors: active)  {

  [type='checkbox']:indeterminate {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='file'] {
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}

[type='file']:focus {
  outline: 1px solid ButtonText;
  outline: 1px auto -webkit-focus-ring-color;
}:root{--colors-primary-500: var(--dokan-button-border-color, #7047EB);--wp-components-color-accent: var(--dokan-button-background-color, #7047EB);--wp-components-color-accent-darker-20: var(--dokan-button-hover-background-color, #502BBF)}#headlessui-portal-root{display:none}#headlessui-portal-root.dokan-layout button[type=button]:is(.absolute.right-2.top-2) {
  border-radius: 9999px;
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}#headlessui-portal-root.dokan-layout button[type=button]:is(.absolute.right-2.top-2):hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
  --tw-ring-offset-width: 2px;
}.dokan-layout table:not(.dataviews-view-table),.dokan-layout table:not(.dataviews-view-table) th,.dokan-layout table:not(.dataviews-view-table) td{margin:0;padding:0;border:0;border-spacing:0;border-collapse:collapse;font-size:inherit;font-weight:inherit;text-align:inherit;vertical-align:inherit;box-sizing:border-box}.dokan-layout a:focus:not([role=switch],[role=combobox]),.dokan-layout button:focus:not([role=switch],[role=combobox]),.dokan-layout .button.alt:focus:not([role=switch],[role=combobox]),.dokan-layout textarea:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=button]:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=reset]:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=submit]:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=tel]:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=url]:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=password]:focus:not([role=switch],[role=combobox]),.dokan-layout input[type=search]:focus:not([role=switch],[role=combobox]){outline-color:var(--dokan-button-border-color, #7047EB)}.dokan-layout  a:not(.dokan-btn):not([class*=dokan-btn-],.skip-color-module) {
  color: var(--dokan-link-color, #7047EB);
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}.dokan-layout  a:not(.dokan-btn):not([class*=dokan-btn-],.skip-color-module):hover,:is(.dokan-layout a:not(.dokan-btn):not([class*=dokan-btn-],.skip-color-module):focus) {
  color: var(--dokan-link-hover-color, var(--dokan-sidebar-background-color, #322067));
}.dokan-layout  input[type=checkbox]::before {
  --tw-content: "" !important;
  content: var(--tw-content) !important;
}.dokan-layout textarea:focus,.dokan-layout input[type=text]:focus,.dokan-layout input[type=number]:focus{outline-offset:0}button[data-headlessui-state=checked]:hover,button[data-headlessui-state=checked]:focus{background-color:var(--dokan-button-background-color, #7047EB) !important}div[data-radix-popper-content-wrapper],div[data-headlessui-state=open][role=dialog]{z-index:999 !important}.container {
  width: 100%;
}@media (min-width: 640px) {

  .container {
    max-width: 640px;
  }
}@media (min-width: 768px) {

  .container {
    max-width: 768px;
  }
}@media (min-width: 1024px) {

  .container {
    max-width: 1024px;
  }
}@media (min-width: 1280px) {

  .container {
    max-width: 1280px;
  }
}@media (min-width: 1536px) {

  .container {
    max-width: 1536px;
  }
}.dokan-layout .sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}.dokan-layout .visible {
  visibility: visible;
}.dokan-layout .fixed {
  position: fixed;
}.dokan-layout .absolute {
  position: absolute;
}.dokan-layout .relative {
  position: relative;
}.dokan-layout .sticky {
  position: sticky;
}.dokan-layout .-start-6 {
  inset-inline-start: -1.5rem;
}.dokan-layout .-top-2 {
  top: -0.5rem;
}.dokan-layout .right-0 {
  right: 0px;
}.dokan-layout .right-0\.5 {
  right: 0.125rem;
}.dokan-layout .right-1 {
  right: 0.25rem;
}.dokan-layout .right-3 {
  right: 0.75rem;
}.dokan-layout .top-0 {
  top: 0px;
}.dokan-layout .top-0\.5 {
  top: 0.125rem;
}.dokan-layout .top-1 {
  top: 0.25rem;
}.dokan-layout .top-full {
  top: 100%;
}.dokan-layout .z-10 {
  z-index: 10;
}.dokan-layout .z-50 {
  z-index: 50;
}.dokan-layout .col-span-12 {
  grid-column: span 12 / span 12;
}.dokan-layout .col-span-4 {
  grid-column: span 4 / span 4;
}.dokan-layout .-m-4 {
  margin: -1rem;
}.dokan-layout .m-0 {
  margin: 0px;
}.dokan-layout .mx-auto {
  margin-left: auto;
  margin-right: auto;
}.dokan-layout .my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}.dokan-layout .my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}.dokan-layout .-mb-px {
  margin-bottom: -1px;
}.dokan-layout .-mt-10 {
  margin-top: -2.5rem;
}.dokan-layout .mb-0 {
  margin-bottom: 0px;
}.dokan-layout .mb-14 {
  margin-bottom: 3.5rem;
}.dokan-layout .mb-2 {
  margin-bottom: 0.5rem;
}.dokan-layout .mb-3 {
  margin-bottom: 0.75rem;
}.dokan-layout .mb-4 {
  margin-bottom: 1rem;
}.dokan-layout .mb-5 {
  margin-bottom: 1.25rem;
}.dokan-layout .mb-6 {
  margin-bottom: 1.5rem;
}.dokan-layout .mb-7 {
  margin-bottom: 1.75rem;
}.dokan-layout .mb-8 {
  margin-bottom: 2rem;
}.dokan-layout .ml-1 {
  margin-left: 0.25rem;
}.dokan-layout .ml-2 {
  margin-left: 0.5rem;
}.dokan-layout .ml-5 {
  margin-left: 1.25rem;
}.dokan-layout .ml-auto {
  margin-left: auto;
}.dokan-layout .mr-2 {
  margin-right: 0.5rem;
}.dokan-layout .mr-20 {
  margin-right: 5rem;
}.dokan-layout .mr-4 {
  margin-right: 1rem;
}.dokan-layout .mr-auto {
  margin-right: auto;
}.dokan-layout .ms-10 {
  margin-inline-start: 2.5rem;
}.dokan-layout .mt-0 {
  margin-top: 0px;
}.dokan-layout .mt-0\.5 {
  margin-top: 0.125rem;
}.dokan-layout .mt-1 {
  margin-top: 0.25rem;
}.dokan-layout .mt-1\.5 {
  margin-top: 0.375rem;
}.dokan-layout .mt-10 {
  margin-top: 2.5rem;
}.dokan-layout .mt-2 {
  margin-top: 0.5rem;
}.dokan-layout .mt-4 {
  margin-top: 1rem;
}.dokan-layout .mt-5 {
  margin-top: 1.25rem;
}.dokan-layout .mt-6 {
  margin-top: 1.5rem;
}.dokan-layout .box-border {
  box-sizing: border-box;
}.dokan-layout .block {
  display: block;
}.dokan-layout .inline-block {
  display: inline-block;
}.dokan-layout .flex {
  display: flex;
}.dokan-layout .inline-flex {
  display: inline-flex;
}.dokan-layout .grid {
  display: grid;
}.dokan-layout .hidden {
  display: none;
}.dokan-layout .aspect-video {
  aspect-ratio: 16 / 9;
}.dokan-layout .size-5 {
  width: 1.25rem;
  height: 1.25rem;
}.dokan-layout .h-1\.5 {
  height: 0.375rem;
}.dokan-layout .h-1\/2 {
  height: 50%;
}.dokan-layout .h-10 {
  height: 2.5rem;
}.dokan-layout .h-12 {
  height: 3rem;
}.dokan-layout .h-3\.5 {
  height: 0.875rem;
}.dokan-layout .h-4 {
  height: 1rem;
}.dokan-layout .h-5 {
  height: 1.25rem;
}.dokan-layout .h-6 {
  height: 1.5rem;
}.dokan-layout .h-7 {
  height: 1.75rem;
}.dokan-layout .h-8 {
  height: 2rem;
}.dokan-layout .h-\[3rem\] {
  height: 3rem;
}.dokan-layout .h-full {
  height: 100%;
}.dokan-layout .max-h-\[500px\] {
  max-height: 500px;
}.dokan-layout .min-h-\[194px\] {
  min-height: 194px;
}.dokan-layout .min-h-\[3rem\] {
  min-height: 3rem;
}.dokan-layout .min-h-screen {
  min-height: 100vh;
}.dokan-layout .w-1\.5 {
  width: 0.375rem;
}.dokan-layout .w-1\/2 {
  width: 50%;
}.dokan-layout .w-1\/3 {
  width: 33.333333%;
}.dokan-layout .w-1\/4 {
  width: 25%;
}.dokan-layout .w-10 {
  width: 2.5rem;
}.dokan-layout .w-12 {
  width: 3rem;
}.dokan-layout .w-2\/3 {
  width: 66.666667%;
}.dokan-layout .w-24 {
  width: 6rem;
}.dokan-layout .w-3\.5 {
  width: 0.875rem;
}.dokan-layout .w-3\/4 {
  width: 75%;
}.dokan-layout .w-3\/6 {
  width: 50%;
}.dokan-layout .w-36 {
  width: 9rem;
}.dokan-layout .w-4 {
  width: 1rem;
}.dokan-layout .w-4\/6 {
  width: 66.666667%;
}.dokan-layout .w-5 {
  width: 1.25rem;
}.dokan-layout .w-5\/6 {
  width: 83.333333%;
}.dokan-layout .w-6 {
  width: 1.5rem;
}.dokan-layout .w-64 {
  width: 16rem;
}.dokan-layout .w-7 {
  width: 1.75rem;
}.dokan-layout .w-8 {
  width: 2rem;
}.dokan-layout .w-\[11rem\] {
  width: 11rem;
}.dokan-layout .w-\[5px\] {
  width: 5px;
}.dokan-layout .w-full {
  width: 100%;
}.dokan-layout .min-w-72 {
  min-width: 18rem;
}.dokan-layout .max-w-3xl {
  max-width: 48rem;
}.dokan-layout .max-w-7xl {
  max-width: 80rem;
}.dokan-layout .max-w-\[530px\] {
  max-width: 530px;
}.dokan-layout .max-w-\[720px\] {
  max-width: 720px;
}.dokan-layout .max-w-sm {
  max-width: 24rem;
}.dokan-layout .shrink {
  flex-shrink: 1;
}.dokan-layout .rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.dokan-layout .transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}@keyframes pulse {

  50% {
    opacity: .5;
  }
}.dokan-layout .animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}.dokan-layout .cursor-pointer {
  cursor: pointer;
}.dokan-layout .grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}.dokan-layout .grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}.dokan-layout .grid-cols-8 {
  grid-template-columns: repeat(8, minmax(0, 1fr));
}.dokan-layout .flex-row {
  flex-direction: row;
}.dokan-layout .flex-col {
  flex-direction: column;
}.dokan-layout .flex-wrap {
  flex-wrap: wrap;
}.dokan-layout .items-start {
  align-items: flex-start;
}.dokan-layout .items-center {
  align-items: center;
}.dokan-layout .justify-start {
  justify-content: flex-start;
}.dokan-layout .justify-end {
  justify-content: flex-end;
}.dokan-layout .justify-center {
  justify-content: center;
}.dokan-layout .justify-between {
  justify-content: space-between;
}.dokan-layout .gap-1 {
  gap: 0.25rem;
}.dokan-layout .gap-1\.5 {
  gap: 0.375rem;
}.dokan-layout .gap-2 {
  gap: 0.5rem;
}.dokan-layout .gap-2\.5 {
  gap: 0.625rem;
}.dokan-layout .gap-3 {
  gap: 0.75rem;
}.dokan-layout .gap-4 {
  gap: 1rem;
}.dokan-layout .gap-5 {
  gap: 1.25rem;
}.dokan-layout .gap-6 {
  gap: 1.5rem;
}.dokan-layout .gap-7 {
  gap: 1.75rem;
}.dokan-layout .gap-x-1 {
  -moz-column-gap: 0.25rem;
       column-gap: 0.25rem;
}.dokan-layout .gap-x-2 {
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}.dokan-layout .gap-y-4 {
  row-gap: 1rem;
}.dokan-layout :is(.space-x-2 > :not([hidden]) ~ :not([hidden])) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}.dokan-layout :is(.space-x-4 > :not([hidden]) ~ :not([hidden])) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}.dokan-layout :is(.space-x-8 > :not([hidden]) ~ :not([hidden])) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}.dokan-layout :is(.space-y-2\.5 > :not([hidden]) ~ :not([hidden])) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.625rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.625rem * var(--tw-space-y-reverse));
}.dokan-layout :is(.space-y-3 > :not([hidden]) ~ :not([hidden])) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}.dokan-layout :is(.space-y-6 > :not([hidden]) ~ :not([hidden])) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}.dokan-layout :is(.divide-y > :not([hidden]) ~ :not([hidden])) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}.dokan-layout :is(.divide-gray-200 > :not([hidden]) ~ :not([hidden])) {
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}.dokan-layout .overflow-hidden {
  overflow: hidden;
}.dokan-layout .overflow-y-auto {
  overflow-y: auto;
}.dokan-layout .truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}.dokan-layout .rounded {
  border-radius: 0.25rem;
}.dokan-layout .rounded-\[20px\] {
  border-radius: 20px;
}.dokan-layout .rounded-full {
  border-radius: 9999px;
}.dokan-layout .rounded-lg {
  border-radius: 0.5rem;
}.dokan-layout .rounded-md {
  border-radius: 0.375rem;
}.dokan-layout .rounded-sm {
  border-radius: 0.125rem;
}.dokan-layout .rounded-l-md {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}.dokan-layout .rounded-l-none {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}.dokan-layout .rounded-r-md {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}.dokan-layout .rounded-r-none {
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}.dokan-layout .border {
  border-width: 1px;
}.dokan-layout .border-0 {
  border-width: 0px;
}.dokan-layout .border-\[0\.957434px\] {
  border-width: 0.957434px;
}.dokan-layout .border-\[1px\] {
  border-width: 1px;
}.dokan-layout .\!border-b-\[1px\] {
  border-bottom-width: 1px !important;
}.dokan-layout .\!border-r-\[1px\] {
  border-right-width: 1px !important;
}.dokan-layout .border-b {
  border-bottom-width: 1px;
}.dokan-layout .border-b-0 {
  border-bottom-width: 0px;
}.dokan-layout .border-b-2 {
  border-bottom-width: 2px;
}.dokan-layout .border-b-\[1px\] {
  border-bottom-width: 1px;
}.dokan-layout .border-l {
  border-left-width: 1px;
}.dokan-layout .border-l-0 {
  border-left-width: 0px;
}.dokan-layout .border-l-\[1px\] {
  border-left-width: 1px;
}.dokan-layout .border-r-\[0\.957434px\] {
  border-right-width: 0.957434px;
}.dokan-layout .border-r-\[1px\] {
  border-right-width: 1px;
}.dokan-layout .border-s-2 {
  border-inline-start-width: 2px;
}.dokan-layout .border-t {
  border-top-width: 1px;
}.dokan-layout .border-solid {
  border-style: solid;
}.dokan-layout .border-dashed {
  border-style: dashed;
}.dokan-layout .border-none {
  border-style: none;
}.dokan-layout .border-\[\#1a9ed4\] {
  --tw-border-opacity: 1;
  border-color: rgb(26 158 212 / var(--tw-border-opacity, 1));
}.dokan-layout .border-\[\#7047EB\] {
  --tw-border-opacity: 1;
  border-color: rgb(112 71 235 / var(--tw-border-opacity, 1));
}.dokan-layout .border-\[\#E5E0F2\] {
  --tw-border-opacity: 1;
  border-color: rgb(229 224 242 / var(--tw-border-opacity, 1));
}.dokan-layout .border-\[\#E9E9E9\] {
  --tw-border-opacity: 1;
  border-color: rgb(233 233 233 / var(--tw-border-opacity, 1));
}.dokan-layout .border-\[\#e9e9ea\] {
  --tw-border-opacity: 1;
  border-color: rgb(233 233 234 / var(--tw-border-opacity, 1));
}.dokan-layout .border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}.dokan-layout .border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}.dokan-layout .border-orange-500 {
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}.dokan-layout .border-red-300 {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}.dokan-layout .border-transparent {
  border-color: transparent;
}.dokan-layout .bg-\[\#0C5F9A\] {
  --tw-bg-opacity: 1;
  background-color: rgb(12 95 154 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#7047EB\] {
  --tw-bg-opacity: 1;
  background-color: rgb(112 71 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#D8D8FE\] {
  --tw-bg-opacity: 1;
  background-color: rgb(216 216 254 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#E4E6EB\] {
  --tw-bg-opacity: 1;
  background-color: rgb(228 230 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#EFEAFF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(239 234 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#F1EDFD\] {
  --tw-bg-opacity: 1;
  background-color: rgb(241 237 253 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#F8F9F8\] {
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 248 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-\[\#FF9B5366\] {
  background-color: #FF9B5366;
}.dokan-layout .bg-\[\#e4e6eb\] {
  --tw-bg-opacity: 1;
  background-color: rgb(228 230 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-amber-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-indigo-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-orange-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-pink-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 242 248 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-teal-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 250 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-transparent {
  background-color: transparent;
}.dokan-layout .bg-violet-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(76 29 149 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-yellow-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}.dokan-layout .bg-close-icon {
  background-image: url(../images/close-icon.png);
}.dokan-layout .bg-upgrade-popup-crown {
  background-image: url(../images/upgrade-popup-crown.png);
}.dokan-layout .bg-upgrade-popup-money {
  background-image: url(../images/upgrade-popup-money.png);
}.dokan-layout .bg-upgrade-popup-pattern {
  background-image: url(../images/upgrade-popup-bg.png);
}.dokan-layout .bg-upgrade-popup-saving {
  background-image: url(../images/upgrade-popup-saving.png);
}.dokan-layout .bg-auto {
  background-size: auto;
}.dokan-layout .bg-cover {
  background-size: cover;
}.dokan-layout .bg-\[-20px_10px\] {
  background-position: -20px 10px;
}.dokan-layout .bg-\[right_-40px_bottom_-40px\] {
  background-position: right -40px bottom -40px;
}.dokan-layout .bg-center {
  background-position: center;
}.dokan-layout .bg-no-repeat {
  background-repeat: no-repeat;
}.dokan-layout .\!fill-\[\#7047EB\] {
  fill: #7047EB !important;
}.dokan-layout .fill-neutral-400 {
  fill: #a3a3a3;
}.dokan-layout .fill-white {
  fill: #fff;
}.dokan-layout .object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}.dokan-layout .p-0 {
  padding: 0px;
}.dokan-layout .p-1 {
  padding: 0.25rem;
}.dokan-layout .p-2 {
  padding: 0.5rem;
}.dokan-layout .p-3 {
  padding: 0.75rem;
}.dokan-layout .p-4 {
  padding: 1rem;
}.dokan-layout .p-6 {
  padding: 1.5rem;
}.dokan-layout .px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}.dokan-layout .px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}.dokan-layout .px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}.dokan-layout .px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}.dokan-layout .px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}.dokan-layout .px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}.dokan-layout .px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}.dokan-layout .px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}.dokan-layout .py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}.dokan-layout .py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}.dokan-layout .py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}.dokan-layout .py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}.dokan-layout .py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}.dokan-layout .py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}.dokan-layout .py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}.dokan-layout .py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}.dokan-layout .py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}.dokan-layout .pl-1 {
  padding-left: 0.25rem;
}.dokan-layout .pl-2 {
  padding-left: 0.5rem;
}.dokan-layout .pl-3 {
  padding-left: 0.75rem;
}.dokan-layout .pl-4 {
  padding-left: 1rem;
}.dokan-layout .pl-\[5px\] {
  padding-left: 5px;
}.dokan-layout .pr-0 {
  padding-right: 0px;
}.dokan-layout .pr-1\.5 {
  padding-right: 0.375rem;
}.dokan-layout .pr-2 {
  padding-right: 0.5rem;
}.dokan-layout .text-left {
  text-align: left;
}.dokan-layout .text-center {
  text-align: center;
}.dokan-layout .align-middle {
  vertical-align: middle;
}.dokan-layout .text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}.dokan-layout .text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}.dokan-layout .text-\[10px\] {
  font-size: 10px;
}.dokan-layout .text-\[12px\] {
  font-size: 12px;
}.dokan-layout .text-\[14px\] {
  font-size: 14px;
}.dokan-layout .text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}.dokan-layout .text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}.dokan-layout .text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}.dokan-layout .text-sm\/6 {
  font-size: 0.875rem;
  line-height: 1.5rem;
}.dokan-layout .text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}.dokan-layout .text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}.dokan-layout .font-bold {
  font-weight: 700;
}.dokan-layout .font-medium {
  font-weight: 500;
}.dokan-layout .font-normal {
  font-weight: 400;
}.dokan-layout .font-semibold {
  font-weight: 600;
}.dokan-layout .capitalize {
  text-transform: capitalize;
}.dokan-layout .leading-5 {
  line-height: 1.25rem;
}.dokan-layout .leading-6 {
  line-height: 1.5rem;
}.dokan-layout .\!text-gray-800 {
  --tw-text-opacity: 1 !important;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1)) !important;
}.dokan-layout .\!text-gray-900 {
  --tw-text-opacity: 1 !important;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1)) !important;
}.dokan-layout .\!text-white {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}.dokan-layout .text-\[\#1a9ed4\] {
  --tw-text-opacity: 1;
  color: rgb(26 158 212 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#25252D\] {
  --tw-text-opacity: 1;
  color: rgb(37 37 45 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#393939\] {
  --tw-text-opacity: 1;
  color: rgb(57 57 57 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#4C19E6\] {
  --tw-text-opacity: 1;
  color: rgb(76 25 230 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#7047EB\] {
  --tw-text-opacity: 1;
  color: rgb(112 71 235 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#788383\] {
  --tw-text-opacity: 1;
  color: rgb(120 131 131 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#7B4E2E\] {
  --tw-text-opacity: 1;
  color: rgb(123 78 46 / var(--tw-text-opacity, 1));
}.dokan-layout .text-\[\#828282\] {
  --tw-text-opacity: 1;
  color: rgb(130 130 130 / var(--tw-text-opacity, 1));
}.dokan-layout .text-amber-800 {
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity, 1));
}.dokan-layout .text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}.dokan-layout .text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}.dokan-layout .text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}.dokan-layout .text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}.dokan-layout .text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}.dokan-layout .text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}.dokan-layout .text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}.dokan-layout .text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}.dokan-layout .text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}.dokan-layout .text-indigo-800 {
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}.dokan-layout .text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}.dokan-layout .text-orange-800 {
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}.dokan-layout .text-pink-800 {
  --tw-text-opacity: 1;
  color: rgb(157 23 77 / var(--tw-text-opacity, 1));
}.dokan-layout .text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}.dokan-layout .text-purple-800 {
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}.dokan-layout .text-red-300 {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}.dokan-layout .text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}.dokan-layout .text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}.dokan-layout .text-teal-800 {
  --tw-text-opacity: 1;
  color: rgb(17 94 89 / var(--tw-text-opacity, 1));
}.dokan-layout .text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}.dokan-layout .text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}.dokan-layout .text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}.dokan-layout .underline {
  text-decoration-line: underline;
}.dokan-layout .no-underline {
  text-decoration-line: none;
}.dokan-layout .opacity-100 {
  opacity: 1;
}.dokan-layout .shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.dokan-layout .shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.dokan-layout .shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.dokan-layout .shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.dokan-layout .outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}.dokan-layout .ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.dokan-layout .ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.dokan-layout .ring-gray-900\/5 {
  --tw-ring-color: rgb(17 24 39 / 0.05);
}.dokan-layout .ring-white {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));
}.dokan-layout .filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}.dokan-layout .transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.dokan-layout .transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.dokan-layout .transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.dokan-layout .transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.dokan-layout .transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.dokan-layout .duration-150 {
  transition-duration: 150ms;
}.dokan-layout .duration-300 {
  transition-duration: 300ms;
}.dokan-layout .duration-500 {
  transition-duration: 500ms;
}.dokan-layout .\@container\/combine {
  container-type: inline-size;
  container-name: combine;
}.dokan-layout .\@container\/currency {
  container-type: inline-size;
  container-name: currency;
}.dokan-layout .\@container\/main {
  container-type: inline-size;
  container-name: main;
}.dokan-layout .\@container\/radio {
  container-type: inline-size;
  container-name: radio;
}.dokan-layout .\@container\/step-body {
  container-type: inline-size;
  container-name: step-body;
}.dokan-layout .\@container\/switcher {
  container-type: inline-size;
  container-name: switcher;
}@keyframes completeStep{0%{opacity:0}100%{opacity:1}}.animate-complete-step{animation:completeStep .9s ease-in-out}#headlessui-portal-root.dokan-layout button[type=button]:is(.before\:absolute.right-2.top-2)::before{content: var(--tw-content);border-radius: 9999px;padding-top: 0.125rem;padding-bottom: 0.125rem;padding-left: 0.375rem;padding-right: 0.375rem}#headlessui-portal-root.dokan-layout button[type=button]:is(.before\:absolute.right-2.top-2):hover::before{content: var(--tw-content);--tw-bg-opacity: 1;background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color: rgb(239 68 68 / var(--tw-text-opacity, 1));--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);--tw-ring-opacity: 1;--tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));--tw-ring-offset-width: 2px}.dokan-layout .before\:absolute::before {
  content: var(--tw-content);
  position: absolute;
}.dokan-layout .before\:bottom-full::before {
  content: var(--tw-content);
  bottom: 100%;
}.dokan-layout .before\:left-0::before {
  content: var(--tw-content);
  left: 0px;
}.dokan-layout .before\:h-12::before {
  content: var(--tw-content);
  height: 3rem;
}.dokan-layout .before\:w-full::before {
  content: var(--tw-content);
  width: 100%;
}.dokan-layout .before\:content-\[\'\'\]::before {
  --tw-content: '';
  content: var(--tw-content);
}.dokan-layout .last\:mb-0:last-child {
  margin-bottom: 0px;
}.dokan-layout .last\:border-b-0:last-child {
  border-bottom-width: 0px;
}.dokan-layout .hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}.dokan-layout .hover\:bg-blue-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}.dokan-layout .hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}.dokan-layout .hover\:bg-indigo-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}.dokan-layout .hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}.dokan-layout .hover\:bg-yellow-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}.dokan-layout .hover\:bg-opacity-85:hover {
  --tw-bg-opacity: 0.85;
}.dokan-layout .hover\:\!text-\[\#7047EB\]:hover {
  --tw-text-opacity: 1 !important;
  color: rgb(112 71 235 / var(--tw-text-opacity, 1)) !important;
}.dokan-layout .hover\:text-\[\#7047EB\]:hover {
  --tw-text-opacity: 1;
  color: rgb(112 71 235 / var(--tw-text-opacity, 1));
}.dokan-layout .hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}.dokan-layout .hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}.dokan-layout .hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}.dokan-layout .focus\:border-gray-300:focus {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}.dokan-layout .focus\:border-gray-900:focus {
  --tw-border-opacity: 1;
  border-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}.dokan-layout .focus\:border-orange-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}.dokan-layout .focus\:\!outline-none:focus {
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
}.dokan-layout .focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}.dokan-layout .focus\:\!ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
}.dokan-layout .focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.dokan-layout .focus\:ring-gray-900:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(17 24 39 / var(--tw-ring-opacity, 1));
}.dokan-layout .focus\:ring-orange-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity, 1));
}.dokan-layout .disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}.dokan-layout :is(.group:hover .group-hover\:\!bg-\[\#EFEAFF\]) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(239 234 255 / var(--tw-bg-opacity, 1)) !important;
}.dokan-layout :is(.group:hover .group-hover\:fill-\[\#7047EB\]) {
  fill: #7047EB;
}.dokan-layout .data-\[closed\]\:translate-y-1[data-closed] {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.dokan-layout .data-\[closed\]\:opacity-0[data-closed] {
  opacity: 0;
}.dokan-layout .data-\[enter\]\:duration-200[data-enter] {
  transition-duration: 200ms;
}.dokan-layout .data-\[leave\]\:duration-150[data-leave] {
  transition-duration: 150ms;
}.dokan-layout .data-\[enter\]\:ease-out[data-enter] {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}.dokan-layout .data-\[leave\]\:ease-in[data-leave] {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}@container main (min-width: 20rem) {

  .dokan-layout .\@xs\/main\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}@container currency (min-width: 24rem) {

  .dokan-layout .\@sm\/currency\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .dokan-layout .\@sm\/currency\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .dokan-layout .\@sm\/currency\:justify-end {
    justify-content: flex-end;
  }

  .dokan-layout .\@sm\/currency\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .dokan-layout .\@sm\/currency\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}@container (min-width: 24rem) {

  .dokan-layout .\@sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}@container combine (min-width: 28rem) {

  .dokan-layout .\@md\/combine\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .dokan-layout .\@md\/combine\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}@container radio (min-width: 28rem) {

  .dokan-layout .\@md\/radio\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .dokan-layout .\@md\/radio\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .dokan-layout .\@md\/radio\:justify-end {
    justify-content: flex-end;
  }
}@container step-body (min-width: 28rem) {

  .dokan-layout .\@md\/step-body\:p-12 {
    padding: 3rem;
  }
}@container switcher (min-width: 28rem) {

  .dokan-layout .\@md\/switcher\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .dokan-layout .\@md\/switcher\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .dokan-layout .\@md\/switcher\:justify-end {
    justify-content: flex-end;
  }
}@container (min-width: 28rem) {

  .dokan-layout .\@md\:gap-6 {
    gap: 1.5rem;
  }

  .dokan-layout .\@md\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}@container combine (min-width: 36rem) {

  .dokan-layout .\@xl\/combine\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .dokan-layout .\@xl\/combine\:col-span-7 {
    grid-column: span 7 / span 7;
  }
}@container main (min-width: 36rem) {

  .dokan-layout .\@xl\/main\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .dokan-layout .\@xl\/main\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .dokan-layout .\@xl\/main\:my-0 {
    margin-top: 0px;
    margin-bottom: 0px;
  }
}@container main (min-width: 42rem) {

  .dokan-layout .\@2xl\/main\:mb-10 {
    margin-bottom: 2.5rem;
  }
}@container main (min-width: 48rem) {

  .dokan-layout .\@3xl\/main\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .dokan-layout .\@3xl\/main\:col-span-9 {
    grid-column: span 9 / span 9;
  }

  .dokan-layout .\@3xl\/main\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }
}@container main (min-width: 64rem) {

  .dokan-layout .\@5xl\/main\:pl-12 {
    padding-left: 3rem;
  }
}@container step-body (min-width: 64rem) {

  .dokan-layout .\@5xl\/step-body\:px-28 {
    padding-left: 7rem;
    padding-right: 7rem;
  }

  .dokan-layout .\@5xl\/step-body\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}@media (min-width: 640px) {

  .dokan-layout .sm\:block {
    display: block;
  }

  .dokan-layout .sm\:hidden {
    display: none;
  }

  .dokan-layout .sm\:w-\[70\%\] {
    width: 70%;
  }

  .dokan-layout .sm\:w-auto {
    width: auto;
  }

  .dokan-layout .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .dokan-layout .sm\:rounded-md {
    border-radius: 0.375rem;
  }

  .dokan-layout .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .dokan-layout .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}@media (min-width: 768px) {

  .dokan-layout .md\:flex {
    display: flex;
  }

  .dokan-layout .md\:w-1\/2 {
    width: 50%;
  }

  .dokan-layout .md\:w-\[1px\] {
    width: 1px;
  }

  .dokan-layout .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .dokan-layout .md\:\!flex-row {
    flex-direction: row !important;
  }

  .dokan-layout .md\:border-0 {
    border-width: 0px;
  }

  .dokan-layout .md\:bg-\[0px_20px\] {
    background-position: 0px 20px;
  }

  .dokan-layout .md\:bg-\[right_0px_bottom_-10px\] {
    background-position: right 0px bottom -10px;
  }

  .dokan-layout .md\:text-\[14px\] {
    font-size: 14px;
  }

  .dokan-layout .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}@media (min-width: 1024px) {

  .dokan-layout .lg\:col-span-12 {
    grid-column: span 12 / span 12;
  }

  .dokan-layout .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .dokan-layout .lg\:grid {
    display: grid;
  }

  .dokan-layout .lg\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .dokan-layout .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .dokan-layout .lg\:gap-6 {
    gap: 1.5rem;
  }

  .dokan-layout .lg\:gap-x-5 {
    -moz-column-gap: 1.25rem;
         column-gap: 1.25rem;
  }

  .dokan-layout .lg\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .dokan-layout .lg\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }
}@media (min-width: 1280px) {

  .dokan-layout .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}@media (prefers-color-scheme: dark) {

  .dokan-layout .dark\:border-gray-700 {
    --tw-border-opacity: 1;
    border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
  }

  .dokan-layout .dark\:text-black {
    --tw-text-opacity: 1;
    color: rgb(0 0 0 / var(--tw-text-opacity, 1));
  }

  .dokan-layout .dark\:text-gray-400 {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity, 1));
  }
}
