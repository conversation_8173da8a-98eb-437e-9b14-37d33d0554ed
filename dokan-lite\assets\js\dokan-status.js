(()=>{"use strict";var e={n:t=>{var r=t&&t.__esModule?()=>t.default:()=>t;return e.d(r,{a:r}),r},d:(t,r)=>{for(var o in r)e.o(r,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:r[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=window.wp.domReady;var r=e.n(t);const o=window.wp.element,l=window.ReactJSXRuntime;function s(...e){return e.filter(Boolean).join(" ")}const n=({pages:e,loading:t,activePage:r,onMenuClick:o})=>(0,l.jsx)("aside",{className:"py-6 px-2 sm:px-6 lg:py-0 lg:px-0 lg:col-span-3",children:(0,l.jsx)("nav",{className:" bg-white rounded-md",children:!t&&(e||[]).map(e=>(0,l.jsx)("a",{"data-hook":e.hook_key,href:e.id,onClick:t=>{t.preventDefault(),o(e.id)},className:s(e.id===r?"bg-gray-50 text-orange-600 hover:bg-white":"text-gray-900 hover:text-gray-900 hover:bg-gray-50","group rounded-md px-6 py-3 flex items-center text-sm font-medium"),"aria-current":e.id===r?"page":void 0,children:(0,l.jsx)("span",{className:"truncate",children:e.title})},e.id))})}),a=window.wp.i18n;function i(...e){return e.filter(Boolean).join(" ")}const d=({tabs:e,loading:t,selectedTab:r,onTabClick:o})=>!t&&(e||[]).length>0&&(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"sm:hidden",children:[(0,l.jsx)("label",{htmlFor:"tabs",className:"sr-only",children:(0,a.__)("Select a tab","dokan-lite")}),(0,l.jsx)("select",{id:"tabs",name:"tabs",className:"block w-full focus:ring-orange-500 focus:border-orange-500 border-gray-300 rounded-md",defaultValue:r,children:e.map(e=>(0,l.jsx)("option",{"data-hook":e.hook_key,children:e.title},e.id))})]}),(0,l.jsx)("div",{className:"hidden sm:block",children:(0,l.jsx)("div",{className:"border-b border-gray-200",children:(0,l.jsx)("nav",{className:"-mb-px flex space-x-8","aria-label":"Tabs",children:e.map(e=>(0,l.jsx)("a",{"data-hook":e.hook_key,href:e.id,onClick:t=>{t.preventDefault(),o(e.id)},className:i(e.id===r?"border-orange-500 text-orange-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300","group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm"),"aria-current":e.id===r?"page":void 0,children:(0,l.jsx)("span",{children:e.title})},e.id))})})})]}),c=({element:e})=>(0,l.jsxs)("div",{className:"overflow-hidden rounded-lg bg-white shadow",children:[(0,l.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[(0,l.jsx)("h3",{className:"text-lg font-medium leading-3 text-gray-900",children:e.title}),e.description&&(0,l.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:e.description})]}),(0,l.jsx)("div",{className:"bg-gray-50 px-4 py-5 flex flex-col gap-4 sm:p-6",children:(e?.children||[]).map(t=>(0,l.jsx)(k,{element:t},e.hook_key+"-"+t.id+"-parser"))})]}),p=({element:e})=>(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[(0,l.jsx)("h3",{className:"text-lg font-medium leading-3 text-gray-900",children:e.title}),e.description&&(0,l.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:e.description})]}),(0,l.jsx)("div",{className:"bg-gray-50 flex flex-col gap-4 px-4 py-5 sm:p-6",children:(e?.children||[]).map(t=>(0,l.jsx)(k,{element:t},e.hook_key+"-"+t.id+"-parser"))})]}),u=({element:e})=>(0,l.jsxs)("div",{className:"md:flex md:items-center md:justify-between","data-hook":e.hook_key,children:[(0,l.jsx)("div",{className:"min-w-0 flex-1",children:(0,l.jsx)("h2",{className:"text-2xl/7 font-bold text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight",children:(0,l.jsx)(o.RawHTML,{children:e.title})})}),(0,l.jsx)("div",{className:"mt-4 flex md:ml-4 md:mt-0",children:(e?.children||[]).map(t=>(0,l.jsx)(k,{element:t},e.hook_key+"-"+t.id+"-parser"))})]}),h=({element:e})=>(0,l.jsx)("div",{className:"overflow-hidden shadow ring-1 ring-black/5 sm:rounded-lg","data-hook":e.hook_key,children:(0,l.jsxs)("table",{className:"min-w-full divide-y divide-gray-300",children:[e.headers.length>0&&(0,l.jsx)("thead",{className:"bg-gray-50",children:(0,l.jsx)("tr",{children:e.headers.map(t=>(0,l.jsx)("th",{scope:"col",className:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900",children:t},e.hook_key+"_table_head_"+t))})}),(0,l.jsx)("tbody",{className:"divide-y divide-gray-200 bg-white",children:(e?.children||[]).map(t=>(0,l.jsx)(k,{element:t},e.hook_key+"-"+t.id+"-parser"))})]})}),m=({element:e})=>(0,l.jsx)("tr",{"data-hook":e.hook_key,children:(e?.children||[]).map(t=>(0,l.jsx)(k,{element:t},e.hook_key+"-"+t.id+"-parser"))}),f=({element:e})=>(0,l.jsx)("td",{className:"whitespace-nowrap px-3 py-4 text-sm text-gray-500","data-hook":e.hook_key,children:(e?.children||[]).map(t=>(0,l.jsx)(k,{element:t},e.hook_key+"-"+t.id+"-parser"))}),b=({element:e})=>(0,l.jsx)("p",{className:"max-w-2xl text-sm text-gray-600","data-hook":e.hook_key,children:(0,l.jsx)(o.RawHTML,{children:e.title})}),x=({element:e})=>(0,l.jsx)("a",{"data-hook":e.hook_key,href:e?.url,title:e?.title_text,className:"font-medium text-blue-600 dark:text-blue-500 hover:underline",children:(0,l.jsx)(o.RawHTML,{children:e.title})}),g=window.wp.apiFetch;var y=e.n(g);const w=window.wp.url,v=({element:e})=>{const[t,r]=(0,o.useState)(!1);return(0,l.jsx)("button",{"data-hook":e.hook_key,onClick:()=>{r(!0),"GET"===e.request?(0,w.addQueryArgs)(e.endpoint,e.payload):e.endpoint;const t={path:e.endpoint,method:e.request,data:"GET"!==e.request?e.payload:{}};y()(t).then(e=>{r(!1)})},disabled:t,type:"button",className:"rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600",children:(0,l.jsx)(o.RawHTML,{children:e.title})})},k=({element:e})=>{switch(e.type){case"section":return(0,l.jsx)(c,{element:e},e.hook_key);case"sub-section":return(0,l.jsx)(p,{element:e},e.hook_key);case"table":return(0,l.jsx)(h,{element:e},e.hook_key);case"table-row":return(0,l.jsx)(m,{element:e},e.hook_key);case"table-column":return(0,l.jsx)(f,{element:e},e.hook_key);case"heading":return(0,l.jsx)(u,{element:e},e.hook_key);case"paragraph":return(0,l.jsx)(b,{element:e},e.hook_key);case"link":return(0,l.jsx)(x,{element:e},e.hook_key);case"button":return(0,l.jsx)(v,{element:e},e.hook_key);default:return wp.hooks.applyFilters("dokan_status_custom_element",(0,l.jsx)(l.Fragment,{}),e)}},j=e=>{const t=F(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{const r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),C(r,t)||N(e)},getConflictingClassGroupIds:(e,t)=>{const l=r[e]||[];return t&&o[e]?[...l,...o[e]]:l}}},C=(e,t)=>{if(0===e.length)return t.classGroupId;const r=e[0],o=t.nextPart.get(r),l=o?C(e.slice(1),o):void 0;if(l)return l;if(0===t.validators.length)return;const s=e.join("-");return t.validators.find(({validator:e})=>e(s))?.classGroupId},_=/^\[(.+)\]$/,N=e=>{if(_.test(e)){const t=_.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},F=e=>{const{theme:t,prefix:r}=e,o={nextPart:new Map,validators:[]};return S(Object.entries(e.classGroups),r).forEach(([e,r])=>{M(r,o,e,t)}),o},M=(e,t,r,o)=>{e.forEach(e=>{if("string"!=typeof e)return"function"==typeof e?z(e)?void M(e(o),t,r,o):void t.validators.push({validator:e,classGroupId:r}):void Object.entries(e).forEach(([e,l])=>{M(l,H(t,e),r,o)});(""===e?t:H(t,e)).classGroupId=r})},H=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},z=e=>e.isThemeGetter,S=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,B=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,o=new Map;const l=(l,s)=>{r.set(l,s),t++,t>e&&(t=0,o=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=o.get(e))?(l(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):l(e,t)}}},G=e=>{const{separator:t,experimentalParseClassName:r}=e,o=1===t.length,l=t[0],s=t.length,n=e=>{const r=[];let n,a=0,i=0;for(let d=0;d<e.length;d++){let c=e[d];if(0===a){if(c===l&&(o||e.slice(d,d+s)===t)){r.push(e.slice(i,d)),i=d+s;continue}if("/"===c){n=d;continue}}"["===c?a++:"]"===c&&a--}const d=0===r.length?e:e.substring(i),c=d.startsWith("!");return{modifiers:r,hasImportantModifier:c,baseClassName:c?d.substring(1):d,maybePostfixModifierPosition:n&&n>i?n-i:void 0}};return r?e=>r({className:e,parseClassName:n}):n},L=e=>{if(e.length<=1)return e;const t=[];let r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},O=/\s+/;function Z(){let e,t,r=0,o="";for(;r<arguments.length;)(e=arguments[r++])&&(t=E(e))&&(o&&(o+=" "),o+=t);return o}const E=e=>{if("string"==typeof e)return e;let t,r="";for(let o=0;o<e.length;o++)e[o]&&(t=E(e[o]))&&(r&&(r+=" "),r+=t);return r};function I(e,...t){let r,o,l,s=function(a){const i=t.reduce((e,t)=>t(e),e());return r=(e=>({cache:B(e.cacheSize),parseClassName:G(e),...j(e)}))(i),o=r.cache.get,l=r.cache.set,s=n,n(a)};function n(e){const t=o(e);if(t)return t;const s=((e,t)=>{const{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:l}=t,s=[],n=e.trim().split(O);let a="";for(let e=n.length-1;e>=0;e-=1){const t=n[e],{modifiers:i,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:p}=r(t);let u=Boolean(p),h=o(u?c.substring(0,p):c);if(!h){if(!u){a=t+(a.length>0?" "+a:a);continue}if(h=o(c),!h){a=t+(a.length>0?" "+a:a);continue}u=!1}const m=L(i).join(":"),f=d?m+"!":m,b=f+h;if(s.includes(b))continue;s.push(b);const x=l(h,u);for(let e=0;e<x.length;++e){const t=x[e];s.push(f+t)}a=t+(a.length>0?" "+a:a)}return a})(e,r);return l(e,s),s}return function(){return s(Z.apply(null,arguments))}}const V=e=>{const t=t=>t[e]||[];return t.isThemeGetter=!0,t},P=/^\[(?:([a-z-]+):)?(.+)\]$/i,A=/^\d+\/\d+$/,R=new Set(["px","full","screen"]),T=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,U=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,W=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,$=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,D=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,q=e=>Q(e)||R.has(e)||A.test(e),J=e=>de(e,"length",ce),Q=e=>Boolean(e)&&!Number.isNaN(Number(e)),X=e=>de(e,"number",Q),K=e=>Boolean(e)&&Number.isInteger(Number(e)),Y=e=>e.endsWith("%")&&Q(e.slice(0,-1)),ee=e=>P.test(e),te=e=>T.test(e),re=new Set(["length","size","percentage"]),oe=e=>de(e,re,pe),le=e=>de(e,"position",pe),se=new Set(["image","url"]),ne=e=>de(e,se,he),ae=e=>de(e,"",ue),ie=()=>!0,de=(e,t,r)=>{const o=P.exec(e);return!!o&&(o[1]?"string"==typeof t?o[1]===t:t.has(o[1]):r(o[2]))},ce=e=>U.test(e)&&!W.test(e),pe=()=>!1,ue=e=>$.test(e),he=e=>D.test(e),me=I((Symbol.toStringTag,()=>{const e=V("colors"),t=V("spacing"),r=V("blur"),o=V("brightness"),l=V("borderColor"),s=V("borderRadius"),n=V("borderSpacing"),a=V("borderWidth"),i=V("contrast"),d=V("grayscale"),c=V("hueRotate"),p=V("invert"),u=V("gap"),h=V("gradientColorStops"),m=V("gradientColorStopPositions"),f=V("inset"),b=V("margin"),x=V("opacity"),g=V("padding"),y=V("saturate"),w=V("scale"),v=V("sepia"),k=V("skew"),j=V("space"),C=V("translate"),_=()=>["auto",ee,t],N=()=>[ee,t],F=()=>["",q,J],M=()=>["auto",Q,ee],H=()=>["","0",ee],z=()=>[Q,ee];return{cacheSize:500,separator:":",theme:{colors:[ie],spacing:[q,J],blur:["none","",te,ee],brightness:z(),borderColor:[e],borderRadius:["none","","full",te,ee],borderSpacing:N(),borderWidth:F(),contrast:z(),grayscale:H(),hueRotate:z(),invert:H(),gap:N(),gradientColorStops:[e],gradientColorStopPositions:[Y,J],inset:_(),margin:_(),opacity:z(),padding:N(),saturate:z(),scale:z(),sepia:H(),skew:z(),space:N(),translate:N()},classGroups:{aspect:[{aspect:["auto","square","video",ee]}],container:["container"],columns:[{columns:[te]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",ee]}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[f]}],"inset-x":[{"inset-x":[f]}],"inset-y":[{"inset-y":[f]}],start:[{start:[f]}],end:[{end:[f]}],top:[{top:[f]}],right:[{right:[f]}],bottom:[{bottom:[f]}],left:[{left:[f]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",K,ee]}],basis:[{basis:_()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",ee]}],grow:[{grow:H()}],shrink:[{shrink:H()}],order:[{order:["first","last","none",K,ee]}],"grid-cols":[{"grid-cols":[ie]}],"col-start-end":[{col:["auto",{span:["full",K,ee]},ee]}],"col-start":[{"col-start":M()}],"col-end":[{"col-end":M()}],"grid-rows":[{"grid-rows":[ie]}],"row-start-end":[{row:["auto",{span:[K,ee]},ee]}],"row-start":[{"row-start":M()}],"row-end":[{"row-end":M()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",ee]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",ee]}],gap:[{gap:[u]}],"gap-x":[{"gap-x":[u]}],"gap-y":[{"gap-y":[u]}],"justify-content":[{justify:["normal","start","end","center","between","around","evenly","stretch"]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal","start","end","center","between","around","evenly","stretch","baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":["start","end","center","between","around","evenly","stretch","baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[g]}],px:[{px:[g]}],py:[{py:[g]}],ps:[{ps:[g]}],pe:[{pe:[g]}],pt:[{pt:[g]}],pr:[{pr:[g]}],pb:[{pb:[g]}],pl:[{pl:[g]}],m:[{m:[b]}],mx:[{mx:[b]}],my:[{my:[b]}],ms:[{ms:[b]}],me:[{me:[b]}],mt:[{mt:[b]}],mr:[{mr:[b]}],mb:[{mb:[b]}],ml:[{ml:[b]}],"space-x":[{"space-x":[j]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[j]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",ee,t]}],"min-w":[{"min-w":[ee,t,"min","max","fit"]}],"max-w":[{"max-w":[ee,t,"none","full","min","max","fit","prose",{screen:[te]},te]}],h:[{h:[ee,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[ee,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[ee,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[ee,t,"auto","min","max","fit"]}],"font-size":[{text:["base",te,J]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",X]}],"font-family":[{font:[ie]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",ee]}],"line-clamp":[{"line-clamp":["none",Q,X]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",q,ee]}],"list-image":[{"list-image":["none",ee]}],"list-style-type":[{list:["none","disc","decimal",ee]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[x]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[x]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:["solid","dashed","dotted","double","none","wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",q,J]}],"underline-offset":[{"underline-offset":["auto",q,ee]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:N()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ee]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ee]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[x]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",le]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",oe]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},ne]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[h]}],"gradient-via":[{via:[h]}],"gradient-to":[{to:[h]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[x]}],"border-style":[{border:["solid","dashed","dotted","double","none","hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[x]}],"divide-style":[{divide:["solid","dashed","dotted","double","none"]}],"border-color":[{border:[l]}],"border-color-x":[{"border-x":[l]}],"border-color-y":[{"border-y":[l]}],"border-color-s":[{"border-s":[l]}],"border-color-e":[{"border-e":[l]}],"border-color-t":[{"border-t":[l]}],"border-color-r":[{"border-r":[l]}],"border-color-b":[{"border-b":[l]}],"border-color-l":[{"border-l":[l]}],"divide-color":[{divide:[l]}],"outline-style":[{outline:["","solid","dashed","dotted","double","none"]}],"outline-offset":[{"outline-offset":[q,ee]}],"outline-w":[{outline:[q,J]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:F()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[x]}],"ring-offset-w":[{"ring-offset":[q,J]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",te,ae]}],"shadow-color":[{shadow:[ie]}],opacity:[{opacity:[x]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[o]}],contrast:[{contrast:[i]}],"drop-shadow":[{"drop-shadow":["","none",te,ee]}],grayscale:[{grayscale:[d]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[p]}],saturate:[{saturate:[y]}],sepia:[{sepia:[v]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[i]}],"backdrop-grayscale":[{"backdrop-grayscale":[d]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[p]}],"backdrop-opacity":[{"backdrop-opacity":[x]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[v]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[n]}],"border-spacing-x":[{"border-spacing-x":[n]}],"border-spacing-y":[{"border-spacing-y":[n]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",ee]}],duration:[{duration:z()}],ease:[{ease:["linear","in","out","in-out",ee]}],delay:[{delay:z()}],animate:[{animate:["none","spin","ping","pulse","bounce",ee]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[K,ee]}],"translate-x":[{"translate-x":[C]}],"translate-y":[{"translate-y":[C]}],"skew-x":[{"skew-x":[k]}],"skew-y":[{"skew-y":[k]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",ee]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ee]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":N()}],"scroll-mx":[{"scroll-mx":N()}],"scroll-my":[{"scroll-my":N()}],"scroll-ms":[{"scroll-ms":N()}],"scroll-me":[{"scroll-me":N()}],"scroll-mt":[{"scroll-mt":N()}],"scroll-mr":[{"scroll-mr":N()}],"scroll-mb":[{"scroll-mb":N()}],"scroll-ml":[{"scroll-ml":N()}],"scroll-p":[{"scroll-p":N()}],"scroll-px":[{"scroll-px":N()}],"scroll-py":[{"scroll-py":N()}],"scroll-ps":[{"scroll-ps":N()}],"scroll-pe":[{"scroll-pe":N()}],"scroll-pt":[{"scroll-pt":N()}],"scroll-pr":[{"scroll-pr":N()}],"scroll-pb":[{"scroll-pb":N()}],"scroll-pl":[{"scroll-pl":N()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ee]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[q,J,X]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})),fe=()=>{const[e,t]=(0,o.useState)([]),[r,s]=(0,o.useState)(!0),[i,c]=(0,o.useState)([]),[p,u]=(0,o.useState)(""),[h,m]=(0,o.useState)([]),[f,b]=(0,o.useState)(""),[x,g]=(0,o.useState)([]);return(0,o.useEffect)(()=>{y()({path:"dokan/v1/admin/dashboard/status"}).then(e=>{t(e),s(!1)}).catch(e=>{s(!1)})},[]),(0,o.useEffect)(()=>{r||c(e.filter(e=>"page"===e.type))},[e,r]),(0,o.useEffect)(()=>{!r&&i.length>0&&u(""===p?i[0].id:p)},[i,r,p]),(0,o.useEffect)(()=>{r||m(i?.length>0?i?.find(e=>e.id===p)?.children.filter(e=>"tab"===e.type):e?.filter(e=>"tab"===e.type))},[e,i,p,r]),(0,o.useEffect)(()=>{r||h?.length>0&&b(""===f?h[0].id:f)},[h,r,f]),(0,o.useEffect)(()=>{r||(i?.length||h?.length?i?.length&&!h?.length&&""!==p?g(i.find(e=>e.id===p).children):(!i?.length&&h?.length&&""!==f||i?.length&&h?.length&&""!==p&&""!==f)&&g(h.find(e=>e.id===f).children):g(e))},[e,i,p,h,f,r]),(0,l.jsx)("div",{className:"h-full",children:(0,l.jsx)("main",{className:"max-w-full mx-auto pb-10 lg:py-5 lg:px-0",children:(0,l.jsxs)("div",{className:"lg:grid lg:grid-cols-12 lg:gap-x-5",children:[i&&""!==p&&i.length>0&&(0,l.jsx)(n,{pages:i,loading:r,activePage:p,onMenuClick:e=>{e&&u(e)}},"admin-status-menu"),(0,l.jsxs)("div",{className:me("space-y-6 sm:px-6 lg:px-0 ",i.length?"lg:col-span-9":"lg:col-span-12"),children:[h&&""!==f&&(0,l.jsx)(d,{tabs:h,loading:r,selectedTab:f,onTabClick:e=>{e&&b(e)}},"admin-status-tab"),x.map(e=>(0,l.jsx)(k,{element:e},e.hook_key+"admin-status-parser")),!r&&0===x.length&&(0,l.jsxs)("div",{className:"bg-white text-center rounded-lg shadow-sm p-6 min-h-screen",children:[(0,l.jsxs)("svg",{width:"269",height:"190",viewBox:"0 0 269 190",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-4 mt-16",children:[(0,l.jsx)("path",{d:"M219.114 0H56.2571C48.7618 0 42.6857 6.07614 42.6857 13.5714V176.429C42.6857 183.924 48.7618 190 56.2571 190H219.114C226.61 190 232.686 183.924 232.686 176.429V13.5714C232.686 6.07614 226.61 0 219.114 0Z",fill:"url(#paint0_linear_529_1266)"}),(0,l.jsx)("g",{filter:"url(#filter0_d_529_1266)",children:(0,l.jsx)("path",{d:"M72.5428 73.2861H255.757C257.557 73.2861 259.283 74.0011 260.555 75.2736C261.828 76.5462 262.543 78.2722 262.543 80.0718V114C262.543 115.8 261.828 117.526 260.555 118.799C259.283 120.071 257.557 120.786 255.757 120.786H72.5428C70.7431 120.786 69.0171 120.071 67.7446 118.799C66.472 117.526 65.7571 115.8 65.7571 114V80.0718C65.7571 78.2722 66.472 76.5462 67.7446 75.2736C69.0171 74.0011 70.7431 73.2861 72.5428 73.2861V73.2861Z",fill:"white"})}),(0,l.jsx)("path",{d:"M172.971 84.1426H137.686C135.437 84.1426 133.614 85.9654 133.614 88.214C133.614 90.4626 135.437 92.2854 137.686 92.2854H172.971C175.22 92.2854 177.043 90.4626 177.043 88.214C177.043 85.9654 175.22 84.1426 172.971 84.1426Z",fill:"#BFB5FF",fillOpacity:"0.6"}),(0,l.jsx)("path",{d:"M197.4 101.786H137.686C135.437 101.786 133.614 103.608 133.614 105.857C133.614 108.106 135.437 109.929 137.686 109.929H197.4C199.649 109.929 201.471 108.106 201.471 105.857C201.471 103.608 199.649 101.786 197.4 101.786Z",fill:"#E8E5FF",fillOpacity:"0.6"}),(0,l.jsx)("path",{d:"M107.15 109.928C114.271 109.928 120.043 104.156 120.043 97.0354C120.043 89.9149 114.271 84.1426 107.15 84.1426C100.029 84.1426 94.2571 89.9149 94.2571 97.0354C94.2571 104.156 100.029 109.928 107.15 109.928Z",fill:"#A244FF"}),(0,l.jsx)("path",{d:"M104.746 98.6431H105.53L103.71 93.8201H102.905L101.085 98.6431H101.876L102.338 97.4391H104.284L104.746 98.6431ZM103.311 94.7511L104.032 96.7741H102.59L103.311 94.7511ZM108.13 94.5061C108.655 94.5061 108.872 94.9471 108.872 95.3531C108.872 95.7661 108.634 96.1931 108.102 96.1931H106.954V94.5061H108.13ZM106.205 98.6431H106.954V96.8721H108.144C109.18 96.8721 109.614 96.1161 109.614 95.3531C109.614 94.5901 109.18 93.8201 108.144 93.8201H106.205V98.6431Z",fill:"white"}),(0,l.jsx)("g",{filter:"url(#filter1_d_529_1266)",children:(0,l.jsx)("path",{d:"M14.1857 131.643H197.4C199.2 131.643 200.926 132.357 202.198 133.63C203.471 134.903 204.186 136.629 204.186 138.428V172.357C204.186 174.157 203.471 175.883 202.198 177.155C200.926 178.428 199.2 179.143 197.4 179.143H14.1857C12.3861 179.143 10.6601 178.428 9.38751 177.155C8.11495 175.883 7.40002 174.157 7.40002 172.357V138.428C7.40002 136.629 8.11495 134.903 9.38751 133.63C10.6601 132.357 12.3861 131.643 14.1857 131.643V131.643Z",fill:"white"})}),(0,l.jsx)("path",{d:"M114.614 142.5H79.3286C77.08 142.5 75.2571 144.323 75.2571 146.571C75.2571 148.82 77.08 150.643 79.3286 150.643H114.614C116.863 150.643 118.686 148.82 118.686 146.571C118.686 144.323 116.863 142.5 114.614 142.5Z",fill:"#BFB5FF",fillOpacity:"0.6"}),(0,l.jsx)("path",{d:"M139.043 160.143H79.3286C77.08 160.143 75.2571 161.966 75.2571 164.214C75.2571 166.463 77.08 168.286 79.3286 168.286H139.043C141.291 168.286 143.114 166.463 143.114 164.214C143.114 161.966 141.291 160.143 139.043 160.143Z",fill:"#E8E5FF",fillOpacity:"0.6"}),(0,l.jsx)("path",{d:"M31.1499 168.286C38.2705 168.286 44.0428 162.513 44.0428 155.393C44.0428 148.272 38.2705 142.5 31.1499 142.5C24.0294 142.5 18.2571 148.272 18.2571 155.393C18.2571 162.513 24.0294 168.286 31.1499 168.286Z",fill:"#A244FF"}),(0,l.jsx)("path",{d:"M26.323 157.274C27.415 157.274 27.828 156.553 27.828 155.699V150.82H27.107V155.699C27.107 156.175 26.89 156.581 26.323 156.581C26.267 156.581 25.952 156.567 25.833 156.525L25.763 157.197C25.945 157.239 26.19 157.274 26.323 157.274ZM30.5431 155.713C31.5161 155.713 32.1741 155.076 32.1741 154.271C32.1741 153.564 31.6631 153.123 30.8511 152.92L30.2421 152.766C29.6331 152.612 29.5841 152.29 29.5841 152.136C29.5841 151.695 29.9901 151.422 30.4451 151.422C30.9421 151.422 31.2991 151.73 31.2991 152.185H32.0131C32.0131 151.31 31.3341 150.764 30.4661 150.764C29.6121 150.764 28.8631 151.303 28.8631 152.15C28.8631 152.549 29.0311 153.158 30.0601 153.417L30.6761 153.571C31.0681 153.676 31.4531 153.879 31.4531 154.306C31.4531 154.705 31.1171 155.055 30.5431 155.055C29.9411 155.055 29.5561 154.656 29.5421 154.243H28.8281C28.8281 155.02 29.5141 155.713 30.5431 155.713Z",fill:"white"}),(0,l.jsx)("g",{filter:"url(#filter2_d_529_1266)",children:(0,l.jsx)("path",{d:"M197.4 14.9282H14.1857C10.4381 14.9282 7.40002 17.9663 7.40002 21.7139V55.6425C7.40002 59.3902 10.4381 62.4282 14.1857 62.4282H197.4C201.148 62.4282 204.186 59.3902 204.186 55.6425V21.7139C204.186 17.9663 201.148 14.9282 197.4 14.9282Z",fill:"white"})}),(0,l.jsx)("path",{d:"M111.9 25.7861H76.6143C74.3657 25.7861 72.5428 27.609 72.5428 29.8576C72.5428 32.1061 74.3657 33.929 76.6143 33.929H111.9C114.149 33.929 115.971 32.1061 115.971 29.8576C115.971 27.609 114.149 25.7861 111.9 25.7861Z",fill:"#BFB5FF",fillOpacity:"0.6"}),(0,l.jsx)("path",{d:"M136.329 43.4287H76.6143C74.3657 43.4287 72.5428 45.2516 72.5428 47.5001C72.5428 49.7487 74.3657 51.5716 76.6143 51.5716H136.329C138.577 51.5716 140.4 49.7487 140.4 47.5001C140.4 45.2516 138.577 43.4287 136.329 43.4287Z",fill:"#E8E5FF",fillOpacity:"0.6"}),(0,l.jsx)("path",{d:"M48.7929 51.5718C55.9134 51.5718 61.6857 45.7995 61.6857 38.679C61.6857 31.5585 55.9134 25.7861 48.7929 25.7861C41.6724 25.7861 35.9 31.5585 35.9 38.679C35.9 45.7995 41.6724 51.5718 48.7929 51.5718Z",fill:"#A244FF"}),(0,l.jsx)("path",{d:"M43.9389 39.5282C43.9389 40.3122 44.6389 40.9982 45.6399 40.9982C46.5569 40.9982 47.2079 40.4592 47.2849 39.7032C47.3619 39.0032 46.9629 38.4642 45.9829 38.2052L45.3669 38.0442C44.7929 37.8972 44.7229 37.6032 44.7229 37.4142C44.7229 37.0222 45.1079 36.7492 45.5559 36.7492C46.0599 36.7492 46.3959 37.0292 46.3959 37.4772H47.1379C47.1379 36.5952 46.4659 36.0492 45.5699 36.0492C44.7229 36.0492 43.9809 36.6022 43.9809 37.4282C43.9809 37.8552 44.1419 38.4432 45.1779 38.7232L45.7869 38.8772C46.2349 38.9822 46.5779 39.2132 46.5499 39.6612C46.5009 40.0182 46.2069 40.3192 45.6399 40.3192C45.0589 40.3192 44.7019 39.9202 44.6809 39.5282H43.9389ZM48.9805 36.7982H50.1145C51.0595 36.7982 51.5425 37.5752 51.5425 38.5272C51.5425 39.4862 51.0595 40.2422 50.1145 40.2422H48.9805V36.7982ZM50.1145 40.9282C51.4865 40.9282 52.2845 39.8992 52.2845 38.5272C52.2845 37.1552 51.4865 36.1052 50.1145 36.1052H48.2385V40.9282H50.1145Z",fill:"white"}),(0,l.jsx)("circle",{cx:"48.8195",cy:"37.7204",r:"6.05392",fill:"#A244FF"}),(0,l.jsx)("circle",{cx:"106.565",cy:"96.3972",r:"6.05392",fill:"#A244FF"}),(0,l.jsx)("circle",{cx:"30.192",cy:"154.142",r:"6.05392",fill:"#A244FF"}),(0,l.jsx)("path",{d:"M54.8734 35.625L47.1896 42.8819L43.697 39.5833",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,l.jsx)("path",{d:"M112.618 93.3701L104.935 100.627L101.442 97.3285",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,l.jsx)("path",{d:"M37.1773 152.047L29.4935 159.304L26.0009 156.005",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,l.jsxs)("defs",{children:[(0,l.jsxs)("filter",{id:"filter0_d_529_1266",x:"59.7571",y:"70.2861",width:"208.786",height:"59.5",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,l.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,l.jsx)("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),(0,l.jsx)("feOffset",{dy:"3"}),(0,l.jsx)("feGaussianBlur",{stdDeviation:"3"}),(0,l.jsx)("feColorMatrix",{type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.161 0"}),(0,l.jsx)("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_529_1266"}),(0,l.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_529_1266",result:"shape"})]}),(0,l.jsxs)("filter",{id:"filter1_d_529_1266",x:"1.40002",y:"128.643",width:"208.786",height:"59.5",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,l.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,l.jsx)("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),(0,l.jsx)("feOffset",{dy:"3"}),(0,l.jsx)("feGaussianBlur",{stdDeviation:"3"}),(0,l.jsx)("feColorMatrix",{type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.161 0"}),(0,l.jsx)("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_529_1266"}),(0,l.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_529_1266",result:"shape"})]}),(0,l.jsxs)("filter",{id:"filter2_d_529_1266",x:"0.400024",y:"11.9282",width:"208.786",height:"59.5",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,l.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,l.jsx)("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),(0,l.jsx)("feOffset",{dx:"-1",dy:"3"}),(0,l.jsx)("feGaussianBlur",{stdDeviation:"3"}),(0,l.jsx)("feColorMatrix",{type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.161 0"}),(0,l.jsx)("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_529_1266"}),(0,l.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_529_1266",result:"shape"})]}),(0,l.jsxs)("linearGradient",{id:"paint0_linear_529_1266",x1:"137.686",y1:"0",x2:"137.686",y2:"190",gradientUnits:"userSpaceOnUse",children:[(0,l.jsx)("stop",{stopColor:"#D3CCFF",stopOpacity:"0.6"}),(0,l.jsx)("stop",{offset:"1",stopColor:"#CFC8FB",stopOpacity:"0.6"})]})]})]}),(0,l.jsx)("p",{className:"text-gray-700 text-md font-medium",children:(0,a.__)("All are up-to-date.","dokan-lite")})]})]})]})})})};r()(()=>{wp.hooks.addFilter("dokan-admin-dashboard-routes","dokan-admin-dashboard-status",e=>(e.push({id:"dokan-status",element:(0,l.jsx)(fe,{}),path:"/status"}),e))})})();