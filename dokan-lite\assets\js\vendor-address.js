!function(e,t){const s=e(".dokan-address-fields"),a={init:function(){s.on("change","select.country_to_state",this.state_select)},state_select:function(){let t=wc_country_select_params.countries.replace(/&quot;/g,'"'),s=e.parseJSON(t),a=e("#dokan_address_state");a.addClass("wc-enhanced-select");let d=a.attr("name"),n=a.attr("id"),o=a.attr("class"),l=(a.val(),e("#dokan_selected_state").val()),r=e("#dokan_selected_state").val(),_=e(this).val();if(s[_])if(e.isEmptyObject(s[_]))e("div#dokan-states-box").slideUp(2),a.is("select")&&e("select#dokan_address_state").replaceWith('<input type="text" class="'+o+'" name="'+d+'" id="'+n+'" required />'),e("#dokan_address_state").val("N/A");else{r="";let t="",i=s[_],c="";for(let e in i)i.hasOwnProperty(e)&&(l&&(c=l==e?'selected="selected"':""),t=t+'<option value="'+e+'"'+c+">"+i[e]+"</option>");a.is("select")&&e("select#dokan_address_state").html('<option value="">'+wc_country_select_params.i18n_select_state_text+"</option>"+t),a.is("input")&&(e("input#dokan_address_state").replaceWith('<select type="text" class="'+o+'" name="'+d+'" id="'+n+'" required ></select>'),e("select#dokan_address_state").html('<option value="">'+wc_country_select_params.i18n_select_state_text+"</option>"+t)),e("#dokan_address_state").removeClass("dokan-hide"),e("div#dokan-states-box").slideDown()}else a.is("select")&&(r="",e("select#dokan_address_state").replaceWith('<input type="text" class="'+o+'" name="'+d+'" id="'+n+'" required="required"/>')),e("#dokan_address_state").val(r),"N/A"==e("#dokan_address_state").val()&&e("#dokan_address_state").val(""),e("#dokan_address_state").removeClass("dokan-hide"),e("div#dokan-states-box").slideDown();e(document.body).trigger("dokan_vendor_country_to_state_changing",[_])}};t.dokan_address_select=a,t.dokan_address_select.init(),e(document.body).on("dokan_vendor_country_to_state_changing",function(t,s){if("undefined"==typeof wc_address_i18n_params)return!1;var a,d=wc_address_i18n_params.locale.replace(/&quot;/g,'"'),n=JSON.parse(d);a=void 0!==n[s]?n[s]:n.default;let o=a?.state?.required||void 0===a?.state?.required;if(a?.state?.label){let t=`${a?.state?.label} ${o?'<span class="required"> *</span>':""}`;e(".dokan-address-fields #dokan-states-box label").html(t),e(".dokan-address-fields #dokan-states-box #dokan_address_state").attr("data-state",a?.state?.label)}e(".dokan-address-fields #dokan-states-box #dokan_address_state").attr("required",o)})}(jQuery,window);