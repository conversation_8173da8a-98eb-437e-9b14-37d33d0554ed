#reverse-withdrawal-filter-form {
  display: flex;
  align-items: center;
  margin-top: 10px;
}
#reverse-withdrawal-filter-form input[type="submit"] {
  margin-left: 5px;
}
.reverse-balance-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border: 1px solid #b3b3b3;
  padding: 10px;
  border-radius: 5px;
}
.reverse-balance-section .reverse-balance {
  font-size: 1.02em;
  font-weight: bold;
}
.reverse-balance-section .reverse-threshold {
  font-size: 1em;
  color: #999;
}
.reverse-balance-section .reverse-threshold span {
  margin-right: 5px;
}
.reverse-balance-section .reverse-pay-form {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.reverse-balance-section .reverse-pay-form input {
  width: 100px;
  margin-left: 10px;
}
.reverse-balance-section .reverse-pay-form input.button {
  width: auto;
}
.reverse-balance-section .reverse-balance-notice {
  color: #ff0000;
  font-size: 0.8em;
}

