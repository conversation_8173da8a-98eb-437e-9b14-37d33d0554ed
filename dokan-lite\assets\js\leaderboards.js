"use strict";(self.webpackChunkdokan=self.webpackChunkdokan||[]).push([[823],{6751:(e,r,s)=>{s.r(r),s.d(r,{default:()=>T});var o=s(7723),a=s(6087),d=s(9491),l=s(5556),t=s.n(l),n=s(6427),i=s(7143),c=s(8846),b=s(314),h=s(3306),u=s(2326),m=s(8232),k=s(790);const _=({allLeaderboards:e,hiddenBlocks:r,onToggleHiddenBlock:s})=>e.map(e=>{const o=!r.includes(e.id);return(0,k.jsx)(c.MenuItem,{checked:o,isCheckbox:!0,isClickable:!0,onInvoke:()=>{s(e.id)(),(0,h.recordEvent)("dash_leaderboards_toggle",{status:o?"off":"on",key:e.id})},children:e.label},e.id)}),g=({allLeaderboards:e,hiddenBlocks:r,query:s,rowsPerTable:o,filters:a})=>e.map(e=>{if(!r.includes(e.id))return(0,k.jsx)(u.A,{headers:e.headers,id:e.id,query:s,title:e.label,totalRows:o,filters:a},e.id)}),p=e=>{const{allLeaderboards:r,controls:s,isFirst:d,isLast:l,hiddenBlocks:t,onMove:i,onRemove:h,onTitleBlur:u,onTitleChange:m,onToggleHiddenBlock:p,query:T,title:f,titleInput:j,filters:w}=e,{updateUserPreferences:x,...I}=(0,b.useUserPreferences)(),[v,B]=(0,a.useState)(parseInt(I.dashboard_leaderboard_rows||5,10)),C=e=>{B(parseInt(e,10));const r={dashboard_leaderboard_rows:parseInt(e,10)};x(r)};return(0,k.jsx)(a.Fragment,{children:(0,k.jsxs)("div",{className:"woocommerce-dashboard__dashboard-leaderboards",children:[(0,k.jsx)(c.SectionHeader,{title:f||(0,o.__)("Leaderboards","dokan-lite"),menu:(0,k.jsx)(c.EllipsisMenu,{label:(0,o.__)("Choose which leaderboards to display and other settings","dokan-lite"),renderContent:({onToggle:e})=>(0,k.jsxs)(a.Fragment,{children:[(0,k.jsx)(c.MenuTitle,{children:(0,o.__)("Leaderboards","dokan-lite")}),_({allLeaderboards:r,hiddenBlocks:t,onToggleHiddenBlock:p}),(0,k.jsx)(c.MenuItem,{children:(0,k.jsx)(n.SelectControl,{className:"woocommerce-dashboard__dashboard-leaderboards__select",label:(0,o.__)("Rows per table","dokan-lite"),value:v,options:Array.from({length:20},(e,r)=>({v:r+1,label:r+1})),onChange:C})}),(0,k.jsx)(s,{onToggle:e,onMove:i,onRemove:h,isFirst:d,isLast:l,onTitleBlur:u,onTitleChange:m,titleInput:j})]})})}),(0,k.jsx)("div",{className:"woocommerce-dashboard__columns",children:g({allLeaderboards:r,hiddenBlocks:t,query:T,rowsPerTable:v,filters:w})})]})})};p.propTypes={query:t().object.isRequired};const T=(0,d.compose)((0,i.withSelect)(e=>{const{getItems:r,getItemsError:s}=e(b.ITEMS_STORE_NAME),{leaderboards:o}=(0,m.Qk)("dataEndpoints",{leaderboards:[]});return{allLeaderboards:o,getItems:r,getItemsError:s}}))(p)}}]);