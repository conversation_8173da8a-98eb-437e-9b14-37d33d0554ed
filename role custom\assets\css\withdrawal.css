/* Para Çekme Sistemi CSS */

.role-custom-withdrawal {
    max-width: 1200px;
}

.role-custom-earnings-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.role-custom-earnings-card h2 {
    margin-top: 0;
    color: #1d2327;
    font-size: 18px;
}

.earnings-amount {
    font-size: 36px;
    font-weight: bold;
    color: #00a32a;
    margin: 15px 0;
}

.earnings-description {
    color: #646970;
    font-style: italic;
    margin-bottom: 0;
}

.role-custom-withdrawal-form,
.role-custom-account-details {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.role-custom-withdrawal-form h2,
.role-custom-account-details h2 {
    margin-top: 0;
    color: #1d2327;
    font-size: 18px;
    border-bottom: 1px solid #c3c4c7;
    padding-bottom: 10px;
}

.role-custom-withdrawal-form .form-table th,
.role-custom-account-details .form-table th {
    width: 200px;
    font-weight: 600;
}

.role-custom-withdrawal-form .form-table td,
.role-custom-account-details .form-table td {
    padding-left: 20px;
}

.role-custom-withdrawal-form input[type="number"],
.role-custom-withdrawal-form select,
.role-custom-withdrawal-form textarea,
.role-custom-account-details input[type="text"],
.role-custom-account-details input[type="email"] {
    width: 100%;
    max-width: 400px;
}

.role-custom-withdrawal-form .description {
    color: #646970;
    font-size: 13px;
    margin-top: 5px;
}

/* Loading durumu */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading .button {
    position: relative;
}

.loading .button:after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: #ffffff;
    border-radius: 50%;
    animation: button-loading-spinner 1s ease infinite;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

@keyframes button-loading-spinner {
    from {
        transform: rotate(0turn);
    }
    to {
        transform: rotate(1turn);
    }
}

/* Başarı ve hata mesajları */
.withdrawal-message {
    padding: 12px;
    margin: 15px 0;
    border-radius: 4px;
    border-left: 4px solid;
}

.withdrawal-message.success {
    background-color: #d1e7dd;
    border-left-color: #00a32a;
    color: #0f5132;
}

.withdrawal-message.error {
    background-color: #f8d7da;
    border-left-color: #dc3545;
    color: #721c24;
}

/* Responsive tasarım */
@media (max-width: 768px) {
    .role-custom-withdrawal {
        margin: 10px;
    }
    
    .role-custom-earnings-card,
    .role-custom-withdrawal-form,
    .role-custom-account-details {
        margin-left: 0;
        margin-right: 0;
    }
    
    .earnings-amount {
        font-size: 28px;
    }
    
    .role-custom-withdrawal-form .form-table th,
    .role-custom-account-details .form-table th {
        width: auto;
        display: block;
        padding-bottom: 5px;
    }
    
    .role-custom-withdrawal-form .form-table td,
    .role-custom-account-details .form-table td {
        display: block;
        padding-left: 0;
        padding-top: 0;
    }
}

/* Form validasyonu */
.form-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 1px #dc3545;
}

.form-valid {
    border-color: #00a32a !important;
    box-shadow: 0 0 0 1px #00a32a;
}

/* Tooltip */
.withdrawal-tooltip {
    position: relative;
    display: inline-block;
    cursor: help;
}

.withdrawal-tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #555;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.withdrawal-tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Kart hover efekti */
.role-custom-earnings-card:hover,
.role-custom-withdrawal-form:hover,
.role-custom-account-details:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,.1);
    transition: box-shadow 0.3s ease;
}
