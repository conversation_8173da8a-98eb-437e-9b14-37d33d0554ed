"use strict";(self.webpackChunkdokan=self.webpackChunkdokan||[]).push([[6115],{1461:(e,t,r)=>{r.r(t),r.d(t,{default:()=>x});var a=r(7723),s=r(6087),n=r(9491),o=r(6476),i=r(7143),l=r(8846),c=r(7374),p=r(3306),d=r(4111),u=r(6154),h=r.n(u),m=r(8468),y=r(314),f=r(3577),_=r(5703);var k=r(8232),g=r(790);const{performanceIndicators:v}=(0,k.Qk)("dataEndpoints",{performanceIndicators:[{stat:"revenue/total_sales",chart:"total_sales",label:"Total sales",_links:{api:[{href:"https://core-dokan.test/wp-json/wc-analytics/reports/revenue/stats"}],report:[{href:"/analytics/revenue"}]}},{stat:"revenue/net_revenue",chart:"net_revenue",label:"Net sales",_links:{api:[{href:"https://core-dokan.test/wp-json/wc-analytics/reports/revenue/stats"}],report:[{href:"/analytics/revenue"}]}},{stat:"orders/orders_count",chart:"orders_count",label:"Orders",_links:{api:[{href:"https://core-dokan.test/wp-json/wc-analytics/reports/orders/stats"}],report:[{href:"/analytics/orders"}]}},{stat:"orders/avg_order_value",chart:"avg_order_value",label:"Average order value",_links:{api:[{href:"https://core-dokan.test/wp-json/wc-analytics/reports/orders/stats"}],report:[{href:"/analytics/orders"}]}},{stat:"products/items_sold",chart:"items_sold",label:"Products sold",_links:{api:[{href:"https://core-dokan.test/wp-json/wc-analytics/reports/products/stats"}],report:[{href:"/analytics/products"}]}},{stat:"revenue/refunds",chart:"refunds",label:"Returns",_links:{api:[{href:"https://core-dokan.test/wp-json/wc-analytics/reports/revenue/stats"}],report:[{href:"/analytics/revenue"}]}},{stat:"coupons/orders_count",chart:"orders_count",label:"Discounted orders",_links:{api:[{href:"https://core-dokan.test/wp-json/wc-analytics/reports/coupons/stats"}],report:[{href:"/analytics/coupons"}]}},{stat:"coupons/amount",chart:"amount",label:"Net discount amount",_links:{api:[{href:"https://core-dokan.test/wp-json/wc-analytics/reports/coupons/stats"}],report:[{href:"/analytics/coupons"}]}},{stat:"taxes/total_tax",chart:"total_tax",label:"Total tax",_links:{api:[{href:"https://core-dokan.test/wp-json/wc-analytics/reports/taxes/stats"}],report:[{href:"/analytics/taxes"}]}},{stat:"taxes/order_tax",chart:"order_tax",label:"Order tax",_links:{api:[{href:"https://core-dokan.test/wp-json/wc-analytics/reports/taxes/stats"}],report:[{href:"/analytics/taxes"}]}},{stat:"taxes/shipping_tax",chart:"shipping_tax",label:"Shipping tax",_links:{api:[{href:"https://core-dokan.test/wp-json/wc-analytics/reports/taxes/stats"}],report:[{href:"/analytics/taxes"}]}},{stat:"revenue/shipping",chart:"shipping",label:"Shipping",_links:{api:[{href:"https://core-dokan.test/wp-json/wc-analytics/reports/revenue/stats"}],report:[{href:"/analytics/revenue"}]}},{stat:"downloads/download_count",chart:"download_count",label:"Downloads",_links:{api:[{href:"https://core-dokan.test/wp-json/wc-analytics/reports/downloads/stats"}],report:[{href:"/analytics/downloads"}]}},{stat:"revenue/gross_sales",chart:"gross_sales",label:"Gross sales",_links:{api:[{href:"https://core-dokan.test/wp-json/wc-analytics/reports/revenue/stats"}],report:[{href:"/analytics/revenue"}]}},{stat:"variations/items_sold",chart:"items_sold",label:"Variations Sold",_links:{api:[{href:"https://core-dokan.test/wp-json/wc-analytics/reports/variations/stats"}],report:[{href:"/analytics/variations"}]}}]});class w extends s.Component{renderMenu(){const{hiddenBlocks:e,isFirst:t,isLast:r,onMove:n,onRemove:o,onTitleBlur:i,onTitleChange:c,onToggleHiddenBlock:d,titleInput:u,controls:h}=this.props;return(0,g.jsx)(l.EllipsisMenu,{label:(0,a.__)("Choose which analytics to display and the section name","dokan-lite"),renderContent:({onToggle:m})=>(0,g.jsxs)(s.Fragment,{children:[(0,g.jsx)(l.MenuTitle,{children:(0,a.__)("Display stats:","dokan-lite")}),v.map((t,r)=>{const a=!e.includes(t.stat);return(0,g.jsx)(l.MenuItem,{checked:a,isCheckbox:!0,isClickable:!0,onInvoke:()=>{d(t.stat)(),(0,p.recordEvent)("dash_indicators_toggle",{status:a?"off":"on",key:t.stat})},children:t.label},r)}),(0,g.jsx)(h,{onToggle:m,onMove:n,onRemove:o,isFirst:t,isLast:r,onTitleBlur:i,onTitleChange:c,titleInput:u})]})})}renderList(){const{query:e,primaryRequesting:t,secondaryRequesting:r,primaryError:s,secondaryError:n,primaryData:i,secondaryData:d,userIndicators:u,defaultDateRange:h}=this.props;if(t||r)return(0,g.jsx)(l.SummaryListPlaceholder,{numberOfItems:u.length});if(s||n)return null;const y=(0,o.getPersistedQuery)(e),{compare:k}=(0,c.getDateParamsFromQuery)(e,h),v="previous_period"===k?(0,a.__)("Previous period:","dokan-lite"):(0,a.__)("Previous year:","dokan-lite"),{formatAmount:w,getCurrencyConfig:x}=this.context,b=x();return(0,g.jsx)(l.SummaryList,{children:()=>u.map((e,t)=>{const{primaryValue:r,secondaryValue:a,delta:s,reportUrl:n,reportUrlType:c}=(({indicator:e,primaryData:t,secondaryData:r,currency:a,formatAmount:s,persistedQuery:n})=>{const i=(0,m.find)(t.data,t=>t.stat===e.stat),l=(0,m.find)(r.data,t=>t.stat===e.stat);if(!i||!l)return{};const c=i._links&&i._links.report[0]&&i._links.report[0].href||"",p=function(e,t,r){return e?"/jetpack"===e?(0,_.getAdminLink)("admin.php?page=jetpack#/dashboard"):(0,o.getNewPath)(t,e,{chart:r.chart}):""}(c,n,i),d="/jetpack"===c?"wp-admin":"wc-admin",u="currency"===i.format,h=(0,f.calculateDelta)(i.value,l.value);return{primaryValue:u?s(i.value):(0,f.formatValue)(a,i.format,i.value),secondaryValue:u?s(l.value):(0,f.formatValue)(a,l.format,l.value),delta:h,reportUrl:p,reportUrlType:d}})({indicator:e,primaryData:i,secondaryData:d,currency:b,formatAmount:w,persistedQuery:y});return(0,g.jsx)(l.SummaryNumber,{href:n,hrefType:c,label:e.label,value:r,prevLabel:v,prevValue:a,delta:s,onLinkClickCallback:()=>{(0,p.recordEvent)("dash_indicators_click",{key:e.stat})}},t)})})}render(){const{userIndicators:e,title:t}=this.props;return(0,g.jsxs)(s.Fragment,{children:[(0,g.jsx)(l.SectionHeader,{title:t||(0,a.__)("Store Performance","dokan-lite"),menu:this.renderMenu()}),e.length>0&&(0,g.jsx)("div",{className:"woocommerce-dashboard__store-performance",children:this.renderList()})]})}}w.contextType=d.CurrencyContext;const x=(0,n.compose)((0,i.withSelect)((e,t)=>{const{hiddenBlocks:r,query:a,filters:s}=t,n=v.filter(e=>!r.includes(e.stat)),o={hiddenBlocks:r,userIndicators:n,indicators:v};if(0===n.length)return o;const i=((e,t,r,a)=>{const{getReportItems:s,getReportItemsError:n,isResolving:o}=e(y.REPORTS_STORE_NAME),{woocommerce_default_date_range:i}=e(y.SETTINGS_STORE_NAME).getSetting("wc_admin","wcAdminSettings"),l=(0,c.getCurrentDates)(r,i),p=l.primary.before,d=l.secondary.before,u=t.map(e=>e.stat).join(","),m=(0,y.getFilterQuery)({filters:a,query:r}),f={...m,after:(0,c.appendTimestamp)(l.primary.after,"start"),before:(0,c.appendTimestamp)(p,p.isSame(h()(),"day")?"now":"end"),stats:u},_={...m,after:(0,c.appendTimestamp)(l.secondary.after,"start"),before:(0,c.appendTimestamp)(d,d.isSame(h()(),"day")?"now":"end"),stats:u};return{primaryData:s("performance-indicators",f),primaryError:n("performance-indicators",f)||null,primaryRequesting:o("getReportItems",["performance-indicators",f]),secondaryData:s("performance-indicators",_),secondaryError:n("performance-indicators",_)||null,secondaryRequesting:o("getReportItems",["performance-indicators",_]),defaultDateRange:i}})(e,n,a,s);return{...o,...i}}))(w)}}]);