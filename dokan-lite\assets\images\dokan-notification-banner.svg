<?xml version="1.0" encoding="UTF-8"?>
<svg width="617px" height="103px" viewBox="0 0 617 103" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 57.1 (83088) - https://sketch.com -->
    <title>Dokan data update required (1)</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="617" height="103"></rect>
        <linearGradient x1="109.152945%" y1="48.1909776%" x2="-2.91361133%" y2="40.5199955%" id="linearGradient-3">
            <stop stop-color="#FEFEFE" offset="0%"></stop>
            <stop stop-color="#FCEFEF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="114.949133%" x2="50%" y2="0%" id="linearGradient-4">
            <stop stop-color="#F99C83" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#F1634C" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Dokan-data-update-required-(1)">
            <g id="Group-4">
                <g id="Mask" fill="#FFFFFF" fill-rule="nonzero">
                    <rect id="path-1" x="0" y="0" width="617" height="103"></rect>
                </g>
                <g id="Clipped">
                    <mask id="mask-2" fill="white">
                        <use xlink:href="#path-1"></use>
                    </mask>
                    <g id="path-1"></g>
                    <g id="Group" mask="url(#mask-2)">
                        <g transform="translate(47.000000, 6.000000)">
                            <g id="Group-3" stroke-width="1" fill="none" fill-rule="evenodd" transform="translate(302.500000, 125.500000) scale(-1, 1) translate(-302.500000, -125.500000) ">
                                <path d="M0.589139584,70.1449694 C0.589139584,70.1449694 114.694276,-44.2011554 252.50014,20.3530378 C390.306005,84.9072311 460.585436,29.8632283 532.281676,49.5562001 C592.451945,66.0829275 602.575815,96.1826129 604.275832,105.434272 C604.601527,107.206735 604.618032,250.306108 604.618032,250.306108 L0.589139584,250.306108 L0.589139584,70.1449694 Z" id="BG" fill="url(#linearGradient-3)" fill-rule="nonzero"></path>
                            </g>
                            <path d="M443.093476,33.511663 C459.854772,23.3107173 546.144408,10.6314507 546.144408,98.7446088 C546.144408,186.857767 478.478434,170.018462 478.478434,170.018462 C494.747381,166.057574 513.306824,153.488947 513.306824,90.8228331 C513.306824,28.1567197 443.093476,33.511663 443.093476,33.511663 Z M500.278109,140.024742 C500.278109,140.024742 493.517416,164.342492 473.042785,167.415371 C452.568154,170.48825 448.759915,159.014694 434.532507,159.810547 C434.204664,156.65879 435.117278,153.503216 437.064931,151.054106 C439.012585,148.604997 441.831615,147.068153 444.887493,146.789498 C456.419182,145.175683 478.027726,148.314884 492.383502,138.941497 C492.383502,138.941497 500.941342,134.343231 502.545937,132.11042 L500.278109,140.024742 Z M442.394035,37.8580944 C462.104662,37.566944 481.387103,43.8383657 497.416705,55.7536691 C492.588641,53.1065224 486.903538,52.7198992 481.784295,54.6905657 C475.371281,57.2855271 470.871173,63.3575675 470.097309,70.4599335 C469.372204,82.2205154 470.097309,94.2468732 470.097309,106.029603 L470.097309,141.466385 C465.49076,141.909344 460.926864,142.086528 456.661541,142.086528 C452.608409,142.073621 448.557563,142.28803 444.526697,142.72882 C443.808572,142.836029 443.096624,142.983902 442.394035,143.17178 L442.394035,68.0458028 L442.394035,37.8580944 Z" id="Combined-Shape" fill="url(#linearGradient-4)" fill-rule="nonzero" opacity="0.07"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>