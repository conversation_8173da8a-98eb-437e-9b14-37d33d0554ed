(()=>{"use strict";var e={n:r=>{var t=r&&r.__esModule?()=>r.default:()=>r;return e.d(t,{a:t}),t},d:(r,t)=>{for(var a in t)e.o(t,a)&&!e.o(r,a)&&Object.defineProperty(r,a,{enumerable:!0,get:t[a]})},o:(e,r)=>Object.prototype.hasOwnProperty.call(e,r),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},r={};e.r(r),e.d(r,{DOKAN_CORE_STORE:()=>c,default:()=>u});const t=window.wp.data,a={currentUser:{id:0,username:"",name:"",first_name:"",last_name:"",email:"",url:"",description:"",link:"",locale:"",nickname:"",slug:"",registered_date:"",roles:[],capabilities:void 0,extra_capabilities:void 0,avatar_urls:void 0,meta:void 0},global:{},store:{}},s={getCurrentUser:e=>e.currentUser,hasCap:(e,r)=>e.currentUser.capabilities&&e.currentUser.extra_capabilities?e.currentUser.capabilities.hasOwnProperty(r)?JSON.parse(e.currentUser.capabilities[r]):!!e.currentUser.extra_capabilities.hasOwnProperty(r)&&JSON.parse(e.currentUser.extra_capabilities[r]):null,isVendor:e=>e.currentUser.roles.includes("vendor")||e.currentUser.roles.includes("administrator")&&JSON.parse(e.currentUser.capabilities.dokandar),isVendorStaff:e=>e.currentUser.roles.includes("vendor_staff"),getStoreSettings:e=>e.store,getGlobalSettings:e=>e.global},n=window.wp.coreData,o=window.wp.apiFetch;var i=e.n(o);const l={getCurrentUser:()=>async({registry:e,dispatch:r})=>{const t=await e.resolveSelect(n.store).getCurrentUser(),a=await e.resolveSelect(n.store).getUser(t.id);return r.setCurrentUser(a)},hasCap:e=>async({resolveSelect:r})=>!!e&&await r.getCurrentUser(),getStoreSettings:()=>async({dispatch:e})=>{const r=await i()({path:"/dokan/v1/settings"});return e.setStoreSettings(r),r},getGlobalSettings:()=>async({dispatch:e})=>{const r=await i()({path:"/dokan/v1/admin/settings"});e.setGlobalSettings(r)},isVendor:()=>async({resolveSelect:e})=>await e.getCurrentUser(),isVendorStaff:()=>async({resolveSelect:e})=>await e.getCurrentUser()},c="dokan/core",d=(0,t.createReduxStore)(c,{reducer:(e=a,r)=>{switch(r.type){case"SET_CURRENT_USER":return{...e,currentUser:r.payload};case"SET_STORE_SETTINGS":return{...e,store:r.payload};case"SET_GLOBAL_SETTINGS":return{...e,global:r.payload}}return e},selectors:s,actions:{setCurrentUser:e=>({type:"SET_CURRENT_USER",payload:e}),setStoreSettings:e=>({type:"SET_STORE_SETTINGS",payload:e}),setGlobalSettings:e=>({type:"SET_GLOBAL_SETTINGS",payload:e})},resolvers:l});(0,t.register)(d);const u=d;(window.dokan=window.dokan||{}).coreStore=r})();