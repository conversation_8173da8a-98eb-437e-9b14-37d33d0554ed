"use strict";(self.webpackChunkdokan=self.webpackChunkdokan||[]).push([[1133],{2480:(e,t,a)=>{a.r(t),a.d(t,{default:()=>be});var r=a(7723),l=a(4164),o=a(6087),n=a(5331),s=a(6283),i=a(5556),d=a.n(i),c=a(6427),u=a(8846),_=a(314),p=a(7374),m=a(3306),h=a(2619),y=a(6476),k=a(5703),b=a(4908),f=a(9491),v=a(8443),g=a(7143),x=a(8468),C=a(4111),S=a(3317);function w(e,t,a={}){if(!e||0===e.length)return null;const r=e.slice(0),l=r.pop();if(l.showFilters(t,a)){const e=(0,y.flattenFilters)(l.filters),a=t[l.param]||l.defaultValue||"all";return(0,x.find)(e,{value:a})}return w(r,t,a)}function T(e){return t=>(0,v.format)(e,t)}function R(e){if(e?.data?.intervals?.length>1){const t=e.data.intervals[0].date_start,a=e.data.intervals[e.data.intervals.length-1].date_end;if(p.containsLeapYear&&(0,p.containsLeapYear)(t,a))return!0}return!1}var F=a(790);class j extends o.Component{shouldComponentUpdate(e){return e.isRequesting!==this.props.isRequesting||e.primaryData.isRequesting!==this.props.primaryData.isRequesting||e.secondaryData.isRequesting!==this.props.secondaryData.isRequesting||!(0,x.isEqual)(e.query,this.props.query)}getItemChartData(){const{primaryData:e,selectedChart:t}=this.props;return e.data.intervals.map(function(e){const a={};return e.subtotals.segments.forEach(function(e){if(e.segment_label){const r=a[e.segment_label]?e.segment_label+" (#"+e.segment_id+")":e.segment_label;a[e.segment_id]={label:r,value:e.subtotals[t.key]||0}}}),{date:(0,v.format)("Y-m-d\\TH:i:s",e.date_start),...a}})}getTimeChartData(){const{query:e,primaryData:t,secondaryData:a,selectedChart:r,defaultDateRange:l}=this.props,o=(0,p.getIntervalForQuery)(e,l),{primary:n,secondary:s}=(0,p.getCurrentDates)(e,l);return function(e,t,a,r,l,o,n){const s=R(e),i=R(t),d=[...e.data.intervals],c=[...t.data.intervals],u=[];for(let e=0;e<d.length;e++){const t=d[e],_=(0,v.format)("Y-m-d\\TH:i:s",t.date_start),m=`${a.label} (${a.range})`,h=t.date_start,y=t.subtotals[o]||0,k=c[e],b=`${r.label} (${r.range})`;let f=(0,p.getPreviousDate)(t.date_start,a.after,r.after,l,n).format("YYYY-MM-DD HH:mm:ss"),g=k&&k.subtotals[o]||0;if("day"===n&&s&&!i&&c?.[e]){const a=new Date(t.date_start),r=new Date(c[e].date_start);(0,p.isLeapYear)(a.getFullYear())&&1===a.getMonth()&&29===a.getDate()&&2===r.getMonth()&&1===r.getDate()&&(f="-",g=0,c.splice(e,0,c[e]))}u.push({date:_,primary:{label:m,labelDate:h,value:y},secondary:{label:b,labelDate:f,value:g}})}return u}(t,a,n,s,e.compare,r.key,o)}getTimeChartTotals(){const{primaryData:e,secondaryData:t,selectedChart:a}=this.props;return{primary:(0,x.get)(e,["data","totals",a.key],null),secondary:(0,x.get)(t,["data","totals",a.key],null)}}renderChart(e,t,a,l){const{emptySearchResults:o,filterParam:n,interactiveLegend:s,itemsLabel:i,legendPosition:d,path:c,query:m,selectedChart:h,showHeaderControls:y,primaryData:k,defaultDateRange:b}=this.props,f=(0,p.getIntervalForQuery)(m,b),v=(0,p.getAllowedIntervalsForQuery)(m,b),g=(0,p.getDateFormatsForInterval)(f,k.data.intervals.length,{type:"php"}),x=o?(0,r.__)("No data for the current search","dokan-lite"):(0,r.__)("No data for the selected date range","dokan-lite"),{formatAmount:C,getCurrencyConfig:S}=this.context;return(0,F.jsx)(u.Chart,{allowedIntervals:v,data:a,dateParser:"%Y-%m-%dT%H:%M:%S",emptyMessage:x,filterParam:n,interactiveLegend:s,interval:f,isRequesting:t,itemsLabel:i,legendPosition:d,legendTotals:l,mode:e,path:c,query:m,screenReaderFormat:T(g.screenReaderFormat),showHeaderControls:y,title:h.label,tooltipLabelFormat:T(g.tooltipLabelFormat),tooltipTitle:"time-comparison"===e&&h.label||null,tooltipValueFormat:(0,_.getTooltipValueFormat)(h.type,C),chartType:(0,p.getChartTypeForQuery)(m),valueType:h.type,xFormat:T(g.xFormat),x2Format:T(g.x2Format),currency:S()})}renderItemComparison(){const{isRequesting:e,primaryData:t}=this.props;if(t.isError)return(0,F.jsx)(S.A,{});const a=e||t.isRequesting,r=this.getItemChartData();return this.renderChart("item-comparison",a,r)}renderTimeComparison(){const{isRequesting:e,primaryData:t,secondaryData:a}=this.props;if(!t||t.isError||a.isError)return(0,F.jsx)(S.A,{});const r=e||t.isRequesting||a.isRequesting,l=this.getTimeChartData(),o=this.getTimeChartTotals();return this.renderChart("time-comparison",r,l,o)}render(){const{mode:e}=this.props;return"item-comparison"===e?this.renderItemComparison():this.renderTimeComparison()}}j.contextType=C.CurrencyContext,j.propTypes={filters:d().array,isRequesting:d().bool,itemsLabel:d().string,limitProperties:d().array,mode:d().string,path:d().string.isRequired,primaryData:d().object,query:d().object.isRequired,secondaryData:d().object,selectedChart:d().shape({key:d().string.isRequired,label:d().string.isRequired,order:d().oneOf(["asc","desc"]),orderby:d().string,type:d().oneOf(["average","number","currency"]).isRequired}).isRequired},j.defaultProps={isRequesting:!1,primaryData:{data:{intervals:[]},isError:!1,isRequesting:!1},secondaryData:{data:{intervals:[]},isError:!1,isRequesting:!1}};const P=(0,f.compose)((0,g.withSelect)((e,t)=>{const{charts:a,endpoint:r,filters:l,isRequesting:o,limitProperties:n,query:s,advancedFilters:i}=t,d=n||[r],c=w(l,s),u=(0,x.get)(c,["settings","param"]),p=t.mode||function(e,t){if(e&&t){const a=(0,x.get)(e,["settings","param"]);if(!a||Object.keys(t).includes(a))return(0,x.get)(e,["chartMode"])}return null}(c,s)||"time-comparison",{woocommerce_default_date_range:m}=e(_.SETTINGS_STORE_NAME).getSetting("wc_admin","wcAdminSettings"),h=e(_.REPORTS_STORE_NAME),y={mode:p,filterParam:u,defaultDateRange:m};if(o)return y;const k=d.some(e=>s[e]&&s[e].length);if(s.search&&!k)return{...y,emptySearchResults:!0};const b=a&&a.map(e=>e.key),f=(0,_.getReportChartData)({endpoint:r,dataType:"primary",query:s,selector:h,limitBy:d,filters:l,advancedFilters:i,defaultDateRange:m,fields:b});if("item-comparison"===p)return{...y,primaryData:f};const v=(0,_.getReportChartData)({endpoint:r,dataType:"secondary",query:s,selector:h,limitBy:d,filters:l,advancedFilters:i,defaultDateRange:m,fields:b});return{...y,primaryData:f,secondaryData:v}}))(j);class D extends o.Component{getChartPath(e){return(0,y.getNewPath)({chart:e.key},"/analytics/"+e.endpoint,(0,y.getPersistedQuery)())}render(){const{charts:e,endpoint:t,path:a,query:l,selectedChart:o,filters:n}=this.props;return o?(0,F.jsx)("div",{role:"presentation",className:"woocommerce-dashboard__chart-block-wrapper",onClick:(0,h.applyFilters)("dokan_handle_chart_redirection",()=>{},o,y.getHistory,this.getChartPath),children:(0,F.jsxs)(c.Card,{className:"woocommerce-dashboard__chart-block",children:[(0,F.jsx)(c.CardHeader,{children:(0,F.jsx)(b.Text,{as:"h3",size:16,weight:600,color:"#23282d",children:o.label})}),(0,F.jsxs)(c.CardBody,{size:null,children:[(0,F.jsx)("a",{className:"screen-reader-text",href:(0,k.getAdminLink)(this.getChartPath(o)),children:(0,r.sprintf)((0,r.__)("%s Report","dokan-lite"),o.label)}),(0,F.jsx)(P,{charts:e,endpoint:t,query:l,interactiveLegend:(0,h.applyFilters)("dokan_handle_chart_legends_availability",!1),legendPosition:"bottom",path:a,selectedChart:o,showHeaderControls:!1,filters:n})]})]})}):null}}D.propTypes={charts:d().array,endpoint:d().string.isRequired,path:d().string.isRequired,query:d().object.isRequired,selectedChart:d().object.isRequired};const O=D;var q=a(3832),A=a(1455),I=a.n(A);function E(e){return[e.country,e.state,e.name||(0,r.__)("TAX","dokan-lite"),e.priority].map(e=>e.toString().toUpperCase().trim()).filter(Boolean).join("-")}var L=a(8232);const M="/wc-analytics";function N(e,t=x.identity){return function(a="",r){const l="function"==typeof e?e(r):e,o=(0,y.getIdsFromQuery)(a);if(o.length<1)return Promise.resolve([]);const n={include:o.join(","),per_page:o.length};return I()({path:(0,q.addQueryArgs)(l,n)}).then(e=>e.map(t))}}N(M+"/products/attributes",e=>({key:e.id,label:e.name})),N(M+"/products/categories",e=>({key:e.id,label:e.name}));const B=N(M+"/coupons",e=>({key:e.id,label:e.code})),H=N(M+"/customers",e=>({key:e.id,label:e.name})),Y=N(M+"/products",e=>({key:e.id,label:e.name})),V=N(M+"/taxes",e=>({key:e.id,label:E(e)}));function z({attributes:e,name:t}){const a=(0,L.Qk)("variationTitleAttributesSeparator"," - ");if(t&&t.indexOf(a)>-1)return t;const r=(e||[]).map(({option:e})=>e).join(", ");return r?t+a+r:t}const U=N(({products:e})=>e?M+`/products/${e}/variations`:M+"/variations",e=>({key:e.id,label:z(e)})),Q=(0,h.applyFilters)("dokan_analytics_orders_report_charts",[{key:"orders_count",label:(0,r.__)("Orders","dokan-lite"),type:"number"},{key:"net_revenue",label:(0,r.__)("Net sales","dokan-lite"),order:"desc",orderby:"net_total",type:"currency"},{key:"avg_order_value",label:(0,r.__)("Average order value","dokan-lite"),type:"currency"},{key:"avg_items_per_order",label:(0,r.__)("Average items per order","dokan-lite"),order:"desc",orderby:"num_items_sold",type:"average"}]);(0,h.applyFilters)("dokan_analytics_orders_report_filters",[{label:(0,r.__)("Show","dokan-lite"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:[{label:(0,r.__)("All orders","dokan-lite"),value:"all"},{label:(0,r.__)("Advanced filters","dokan-lite"),value:"advanced"}]}]),(0,h.applyFilters)("dokan_analytics_orders_report_advanced_filters",{title:(0,r._x)("Orders match <select/> filters","A sentence describing filters for Orders. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","dokan-lite"),filters:{status:{labels:{add:(0,r.__)("Order status","dokan-lite"),remove:(0,r.__)("Remove order status filter","dokan-lite"),rule:(0,r.__)("Select an order status filter match","dokan-lite"),title:(0,r.__)("<title>Order status</title> <rule/> <filter/>","dokan-lite"),filter:(0,r.__)("Select an order status","dokan-lite")},rules:[{value:"is",label:(0,r._x)("Is","order status","dokan-lite")},{value:"is_not",label:(0,r._x)("Is Not","order status","dokan-lite")}],input:{component:"SelectControl",options:Object.keys(L.wm).map(e=>({value:e,label:L.wm[e]}))}},product:{labels:{add:(0,r.__)("Product","dokan-lite"),placeholder:(0,r.__)("Search products","dokan-lite"),remove:(0,r.__)("Remove product filter","dokan-lite"),rule:(0,r.__)("Select a product filter match","dokan-lite"),title:(0,r.__)("<title>Product</title> <rule/> <filter/>","dokan-lite"),filter:(0,r.__)("Select products","dokan-lite")},rules:[{value:"includes",label:(0,r._x)("Includes","products","dokan-lite")},{value:"excludes",label:(0,r._x)("Excludes","products","dokan-lite")}],input:{component:"Search",type:"products",getLabels:Y}},variation:{labels:{add:(0,r.__)("Product variation","dokan-lite"),placeholder:(0,r.__)("Search product variations","dokan-lite"),remove:(0,r.__)("Remove product variation filter","dokan-lite"),rule:(0,r.__)("Select a product variation filter match","dokan-lite"),title:(0,r.__)("<title>Product variation</title> <rule/> <filter/>","dokan-lite"),filter:(0,r.__)("Select variation","dokan-lite")},rules:[{value:"includes",label:(0,r._x)("Includes","variations","dokan-lite")},{value:"excludes",label:(0,r._x)("Excludes","variations","dokan-lite")}],input:{component:"Search",type:"variations",getLabels:U}},coupon:{labels:{add:(0,r.__)("Coupon code","dokan-lite"),placeholder:(0,r.__)("Search coupons","dokan-lite"),remove:(0,r.__)("Remove coupon filter","dokan-lite"),rule:(0,r.__)("Select a coupon filter match","dokan-lite"),title:(0,r.__)("<title>Coupon code</title> <rule/> <filter/>","dokan-lite"),filter:(0,r.__)("Select coupon codes","dokan-lite")},rules:[{value:"includes",label:(0,r._x)("Includes","coupon code","dokan-lite")},{value:"excludes",label:(0,r._x)("Excludes","coupon code","dokan-lite")}],input:{component:"Search",type:"coupons",getLabels:B}},customer_type:{labels:{add:(0,r.__)("Customer type","dokan-lite"),remove:(0,r.__)("Remove customer filter","dokan-lite"),rule:(0,r.__)("Select a customer filter match","dokan-lite"),title:(0,r.__)("<title>Customer is</title> <filter/>","dokan-lite"),filter:(0,r.__)("Select a customer type","dokan-lite")},input:{component:"SelectControl",options:[{value:"new",label:(0,r.__)("New","dokan-lite")},{value:"returning",label:(0,r.__)("Returning","dokan-lite")}],defaultOption:"new"}},refunds:{labels:{add:(0,r.__)("Refund","dokan-lite"),remove:(0,r.__)("Remove refund filter","dokan-lite"),rule:(0,r.__)("Select a refund filter match","dokan-lite"),title:(0,r.__)("<title>Refund</title> <filter/>","dokan-lite"),filter:(0,r.__)("Select a refund type","dokan-lite")},input:{component:"SelectControl",options:[{value:"all",label:(0,r.__)("All","dokan-lite")},{value:"partial",label:(0,r.__)("Partially refunded","dokan-lite")},{value:"full",label:(0,r.__)("Fully refunded","dokan-lite")},{value:"none",label:(0,r.__)("None","dokan-lite")}],defaultOption:"all"}},tax_rate:{labels:{add:(0,r.__)("Tax rate","dokan-lite"),placeholder:(0,r.__)("Search tax rates","dokan-lite"),remove:(0,r.__)("Remove tax rate filter","dokan-lite"),rule:(0,r.__)("Select a tax rate filter match","dokan-lite"),title:(0,r.__)("<title>Tax Rate</title> <rule/> <filter/>","dokan-lite"),filter:(0,r.__)("Select tax rates","dokan-lite")},rules:[{value:"includes",label:(0,r._x)("Includes","tax rate","dokan-lite")},{value:"excludes",label:(0,r._x)("Excludes","tax rate","dokan-lite")}],input:{component:"Search",type:"taxes",getLabels:V}},attribute:{allowMultiple:!0,labels:{add:(0,r.__)("Product attribute","dokan-lite"),placeholder:(0,r.__)("Search product attributes","dokan-lite"),remove:(0,r.__)("Remove product attribute filter","dokan-lite"),rule:(0,r.__)("Select a product attribute filter match","dokan-lite"),title:(0,r.__)("<title>Product attribute</title> <rule/> <filter/>","dokan-lite"),filter:(0,r.__)("Select attributes","dokan-lite")},rules:[{value:"is",label:(0,r._x)("Is","product attribute","dokan-lite")},{value:"is_not",label:(0,r._x)("Is Not","product attribute","dokan-lite")}],input:{component:"ProductAttribute"}}}});var J=a(7752);const{addCesSurveyForAnalytics:$}=(0,g.dispatch)(J.STORE_KEY),G=(0,h.applyFilters)("dokan_analytics_products_report_charts",[{key:"items_sold",label:(0,r.__)("Items sold","dokan-lite"),order:"desc",orderby:"items_sold",type:"number"},{key:"net_revenue",label:(0,r.__)("Net sales","dokan-lite"),order:"desc",orderby:"net_revenue",type:"currency"},{key:"orders_count",label:(0,r.__)("Orders","dokan-lite"),order:"desc",orderby:"orders_count",type:"number"}]),K={label:(0,r.__)("Show","dokan-lite"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:[{label:(0,r.__)("All products","dokan-lite"),value:"all"},{label:(0,r.__)("Single product","dokan-lite"),value:"select_product",chartMode:"item-comparison",subFilters:[{component:"Search",value:"single_product",chartMode:"item-comparison",path:["select_product"],settings:{type:"products",param:"products",getLabels:Y,labels:{placeholder:(0,r.__)("Type to search for a product","dokan-lite"),button:(0,r.__)("Single product","dokan-lite")}}}]},{label:(0,r.__)("Comparison","dokan-lite"),value:"compare-products",chartMode:"item-comparison",settings:{type:"products",param:"products",getLabels:Y,labels:{helpText:(0,r.__)("Check at least two products below to compare","dokan-lite"),placeholder:(0,r.__)("Search for products to compare","dokan-lite"),title:(0,r.__)("Compare Products","dokan-lite"),update:(0,r.__)("Compare","dokan-lite")},onClick:$}}]},X={showFilters:e=>"single_product"===e.filter&&!!e.products&&e["is-variable"],staticParams:["filter","products","chartType","paged","per_page"],param:"filter-variations",filters:[{label:(0,r.__)("All variations","dokan-lite"),chartMode:"item-comparison",value:"all"},{label:(0,r.__)("Single variation","dokan-lite"),value:"select_variation",subFilters:[{component:"Search",value:"single_variation",path:["select_variation"],settings:{type:"variations",param:"variations",getLabels:U,labels:{placeholder:(0,r.__)("Type to search for a variation","dokan-lite"),button:(0,r.__)("Single variation","dokan-lite")}}}]},{label:(0,r.__)("Comparison","dokan-lite"),chartMode:"item-comparison",value:"compare-variations",settings:{type:"variations",param:"variations",getLabels:U,labels:{helpText:(0,r.__)("Check at least two variations below to compare","dokan-lite"),placeholder:(0,r.__)("Search for variations to compare","dokan-lite"),title:(0,r.__)("Compare Variations","dokan-lite"),update:(0,r.__)("Compare","dokan-lite")}}}]},W=(0,h.applyFilters)("dokan_analytics_products_report_advanced_filters",{filters:{},title:(0,r._x)("Products Match <select/> Filters","A sentence describing filters for Products. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","dokan-lite")});Object.keys(W.filters).length&&(K.filters.push({label:(0,r.__)("Advanced Filters","dokan-lite"),value:"advanced"}),X.filters.push({label:(0,r.__)("Advanced Filters","dokan-lite"),value:"advanced"})),(0,h.applyFilters)("dokan_analytics_products_report_filters",[K,X]);const Z=(0,h.applyFilters)("dokan_analytics_revenue_report_charts",[{key:"gross_sales",label:(0,r.__)("Gross sales","dokan-lite"),order:"desc",orderby:"gross_sales",type:"currency",isReverseTrend:!1},{key:"refunds",label:(0,r.__)("Returns","dokan-lite"),order:"desc",orderby:"refunds",type:"currency",isReverseTrend:!0},{key:"coupons",label:(0,r.__)("Coupons","dokan-lite"),order:"desc",orderby:"coupons",type:"currency",isReverseTrend:!1},{key:"net_revenue",label:(0,r.__)("Net sales","dokan-lite"),orderby:"net_revenue",type:"currency",isReverseTrend:!1,labelTooltipText:(0,r.__)("Full refunds are not deducted from tax or net sales totals","dokan-lite")},{key:"taxes",label:(0,r.__)("Taxes","dokan-lite"),order:"desc",orderby:"taxes",type:"currency",isReverseTrend:!1,labelTooltipText:(0,r.__)("Full refunds are not deducted from tax or net sales totals","dokan-lite")},{key:"shipping",label:(0,r.__)("Shipping","dokan-lite"),orderby:"shipping",type:"currency",isReverseTrend:!1},{key:"total_sales",label:(0,r.__)("Total sales","dokan-lite"),order:"desc",orderby:"total_sales",type:"currency",isReverseTrend:!1}]),ee=(0,h.applyFilters)("dokan_analytics_revenue_report_advanced_filters",{filters:{},title:(0,r._x)("Revenue Matches <select/> Filters","A sentence describing filters for Revenue. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","dokan-lite")}),te=[];Object.keys(ee.filters).length&&(te.push({label:(0,r.__)("All Revenue","dokan-lite"),value:"all"}),te.push({label:(0,r.__)("Advanced Filters","dokan-lite"),value:"advanced"})),(0,h.applyFilters)("dokan_analytics_revenue_report_filters",[{label:(0,r.__)("Show","dokan-lite"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>te.length>0,filters:te}]);const{addCesSurveyForAnalytics:ae}=(0,g.dispatch)(J.STORE_KEY),re=(0,h.applyFilters)("dokan_analytics_coupons_report_charts",[{key:"orders_count",label:(0,r.__)("Discounted orders","dokan-lite"),order:"desc",orderby:"orders_count",type:"number"},{key:"amount",label:(0,r.__)("Amount","dokan-lite"),order:"desc",orderby:"amount",type:"currency"}]),le=(0,h.applyFilters)("dokan_analytics_coupon_report_advanced_filters",{filters:{},title:(0,r._x)("Coupons match <select/> filters","A sentence describing filters for Coupons. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","dokan-lite")}),oe=[{label:(0,r.__)("All coupons","dokan-lite"),value:"all"},{label:(0,r.__)("Single coupon","dokan-lite"),value:"select_coupon",chartMode:"item-comparison",subFilters:[{component:"Search",value:"single_coupon",chartMode:"item-comparison",path:["select_coupon"],settings:{type:"coupons",param:"coupons",getLabels:B,labels:{placeholder:(0,r.__)("Type to search for a coupon","dokan-lite"),button:(0,r.__)("Single Coupon","dokan-lite")}}}]},{label:(0,r.__)("Comparison","dokan-lite"),value:"compare-coupons",settings:{type:"coupons",param:"coupons",getLabels:B,labels:{title:(0,r.__)("Compare Coupon Codes","dokan-lite"),update:(0,r.__)("Compare","dokan-lite"),helpText:(0,r.__)("Check at least two coupon codes below to compare","dokan-lite")},onClick:ae}}];Object.keys(le.filters).length&&oe.push({label:(0,r.__)("Advanced filters","dokan-lite"),value:"advanced"}),(0,h.applyFilters)("dokan_analytics_coupons_report_filters",[{label:(0,r.__)("Show","dokan-lite"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:oe}]);const{addCesSurveyForAnalytics:ne}=(0,g.dispatch)(J.STORE_KEY),se=(0,h.applyFilters)("dokan_analytics_taxes_report_charts",[{key:"total_tax",label:(0,r.__)("Total tax","dokan-lite"),order:"desc",orderby:"total_tax",type:"currency"},{key:"order_tax",label:(0,r.__)("Order tax","dokan-lite"),order:"desc",orderby:"order_tax",type:"currency"},{key:"shipping_tax",label:(0,r.__)("Shipping tax","dokan-lite"),order:"desc",orderby:"shipping_tax",type:"currency"},{key:"orders_count",label:(0,r.__)("Orders","dokan-lite"),order:"desc",orderby:"orders_count",type:"number"}]),ie=(0,h.applyFilters)("dokan_analytics_taxes_report_advanced_filters",{filters:{},title:(0,r._x)("Taxes match <select/> filters","A sentence describing filters for Taxes. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","dokan-lite")}),de=[{label:(0,r.__)("All taxes","dokan-lite"),value:"all"},{label:(0,r.__)("Comparison","dokan-lite"),value:"compare-taxes",chartMode:"item-comparison",settings:{type:"taxes",param:"taxes",getLabels:N(_.NAMESPACE+"/taxes",e=>({id:e.id,key:e.id,label:E(e)})),labels:{helpText:(0,r.__)("Check at least two tax codes below to compare","dokan-lite"),placeholder:(0,r.__)("Search for tax codes to compare","dokan-lite"),title:(0,r.__)("Compare Tax Codes","dokan-lite"),update:(0,r.__)("Compare","dokan-lite")},onClick:ne}}];Object.keys(ie.filters).length&&de.push({label:(0,r.__)("Advanced filters","dokan-lite"),value:"advanced"}),(0,h.applyFilters)("dokan_analytics_taxes_report_filters",[{label:(0,r.__)("Show","dokan-lite"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:de}]);const ce=(0,h.applyFilters)("dokan_analytics_downloads_report_charts",[{key:"download_count",label:(0,r.__)("Downloads","dokan-lite"),type:"number"}]),ue=((0,h.applyFilters)("dokan_analytics_downloads_report_filters",[{label:(0,r.__)("Show","dokan-lite"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:[{label:(0,r.__)("All downloads","dokan-lite"),value:"all"},{label:(0,r.__)("Advanced filters","dokan-lite"),value:"advanced"}]}]),(0,h.applyFilters)("dokan_analytics_downloads_report_advanced_filters",{title:(0,r._x)("Downloads match <select/> filters","A sentence describing filters for Downloads. See screen shot for context: https://cloudup.com/ccxhyH2mEDg","dokan-lite"),filters:{product:{labels:{add:(0,r.__)("Product","dokan-lite"),placeholder:(0,r.__)("Search","dokan-lite"),remove:(0,r.__)("Remove product filter","dokan-lite"),rule:(0,r.__)("Select a product filter match","dokan-lite"),title:(0,r.__)("<title>Product</title> <rule/> <filter/>","dokan-lite"),filter:(0,r.__)("Select product","dokan-lite")},rules:[{value:"includes",label:(0,r._x)("Includes","products","dokan-lite")},{value:"excludes",label:(0,r._x)("Excludes","products","dokan-lite")}],input:{component:"Search",type:"products",getLabels:Y}},customer:{labels:{add:(0,r.__)("Username","dokan-lite"),placeholder:(0,r.__)("Search customer username","dokan-lite"),remove:(0,r.__)("Remove customer username filter","dokan-lite"),rule:(0,r.__)("Select a customer username filter match","dokan-lite"),title:(0,r.__)("<title>Username</title> <rule/> <filter />","dokan-lite"),filter:(0,r.__)("Select customer username","dokan-lite")},rules:[{value:"includes",label:(0,r._x)("Includes","customer usernames","dokan-lite")},{value:"excludes",label:(0,r._x)("Excludes","customer usernames","dokan-lite")}],input:{component:"Search",type:"usernames",getLabels:H}},order:{labels:{add:(0,r.__)("Order #","dokan-lite"),placeholder:(0,r.__)("Search order number","dokan-lite"),remove:(0,r.__)("Remove order number filter","dokan-lite"),rule:(0,r.__)("Select a order number filter match","dokan-lite"),title:(0,r.__)("<title>Order #</title> <rule/> <filter/>","dokan-lite"),filter:(0,r.__)("Select order number","dokan-lite")},rules:[{value:"includes",label:(0,r._x)("Includes","order numbers","dokan-lite")},{value:"excludes",label:(0,r._x)("Excludes","order numbers","dokan-lite")}],input:{component:"Search",type:"orders",getLabels:async e=>{const t=e.split(",");return await t.map(e=>({id:e,label:"#"+e}))}}},ip_address:{labels:{add:(0,r.__)("IP Address","dokan-lite"),placeholder:(0,r.__)("Search IP address","dokan-lite"),remove:(0,r.__)("Remove IP address filter","dokan-lite"),rule:(0,r.__)("Select an IP address filter match","dokan-lite"),title:(0,r.__)("<title>IP Address</title> <rule/> <filter/>","dokan-lite"),filter:(0,r.__)("Select IP address","dokan-lite")},rules:[{value:"includes",label:(0,r._x)("Includes","IP addresses","dokan-lite")},{value:"excludes",label:(0,r._x)("Excludes","IP addresses","dokan-lite")}],input:{component:"Search",type:"downloadIps",getLabels:async e=>{const t=e.split(",");return await t.map(e=>({id:e,label:e}))}}}}}),{revenue:Z,orders:Q,products:G,coupons:re,taxes:se,downloads:ce}),_e=[{label:(0,r.__)("Total sales","dokan-lite"),report:"revenue",key:"total_sales"},{label:(0,r.__)("Net sales","dokan-lite"),report:"revenue",key:"net_revenue"},{label:(0,r.__)("Orders","dokan-lite"),report:"orders",key:"orders_count"},{label:(0,r.__)("Average order value","dokan-lite"),report:"orders",key:"avg_order_value"},{label:(0,r.__)("Items sold","dokan-lite"),report:"products",key:"items_sold"},{label:(0,r.__)("Returns","dokan-lite"),report:"revenue",key:"refunds"},{label:(0,r.__)("Discounted orders","dokan-lite"),report:"coupons",key:"orders_count"},{label:(0,r.__)("Gross discounted","dokan-lite"),report:"coupons",key:"amount"},{label:(0,r.__)("Total tax","dokan-lite"),report:"taxes",key:"total_tax"},{label:(0,r.__)("Order tax","dokan-lite"),report:"taxes",key:"order_tax"},{label:(0,r.__)("Shipping tax","dokan-lite"),report:"taxes",key:"shipping_tax"},{label:(0,r.__)("Shipping","dokan-lite"),report:"revenue",key:"shipping"},{label:(0,r.__)("Downloads","dokan-lite"),report:"downloads",key:"download_count"}],pe=(0,h.applyFilters)("dokan_analytics_dashboard_charts_filter",_e.filter(e=>"downloads"!==e.report&&"download_count"!==e.key).map(e=>({...ue[e.report].find(t=>t.key===e.key),label:e.label,endpoint:e.report}))),me=({hiddenBlocks:e,onToggleHiddenBlock:t})=>pe.map(a=>{const r=a.endpoint+"_"+a.key,l=!e.includes(r);return(0,F.jsx)(u.MenuItem,{checked:l,isCheckbox:!0,isClickable:!0,onInvoke:()=>{t(r)(),(0,m.recordEvent)("dash_charts_chart_toggle",{status:l?"off":"on",key:r})},children:a.label},a.endpoint+"_"+a.key)}),he=({chartInterval:e,setInterval:t,query:a,defaultDateRange:l})=>{const o=(0,p.getAllowedIntervalsForQuery)(a,l);if(!o||o.length<1)return null;const n={hour:(0,r.__)("By hour","dokan-lite"),day:(0,r.__)("By day","dokan-lite"),week:(0,r.__)("By week","dokan-lite"),month:(0,r.__)("By month","dokan-lite"),quarter:(0,r.__)("By quarter","dokan-lite"),year:(0,r.__)("By year","dokan-lite")};return(0,F.jsx)(c.SelectControl,{className:"woocommerce-chart__interval-select",value:e,options:o.map(e=>({value:e,label:n[e]})),"aria-label":"Chart period",onChange:t})},ye=({hiddenBlocks:e,path:t,query:a,filters:r})=>{const l=pe.reduce((e,t)=>(void 0===e[t.endpoint]&&(e[t.endpoint]=[]),e[t.endpoint].push(t),e),{});return(0,F.jsx)("div",{className:"woocommerce-dashboard__columns",children:pe.map(o=>e.includes(o.endpoint+"_"+o.key)?null:(0,F.jsx)(O,{charts:l[o.endpoint],endpoint:o.endpoint,path:t,query:a,selectedChart:o,filters:r},o.endpoint+"_"+o.key))})},ke=e=>{const{controls:t,hiddenBlocks:a,isFirst:i,isLast:d,onMove:p,onRemove:h,onTitleBlur:y,onTitleChange:k,onToggleHiddenBlock:b,path:f,title:v,titleInput:g,filters:x,defaultDateRange:C}=e,{updateUserPreferences:S,...w}=(0,_.useUserPreferences)(),[T,R]=(0,o.useState)(w.dashboard_chart_type||"line"),[j,P]=(0,o.useState)(w.dashboard_chart_interval||"day"),D={...e.query,chartType:T,interval:j},O=e=>()=>{R(e),S({dashboard_chart_type:e}),(0,m.recordEvent)("dash_charts_type_toggle",{chart_type:e})};return(0,F.jsxs)("div",{className:"woocommerce-dashboard__dashboard-charts",children:[(0,F.jsxs)(u.SectionHeader,{title:v||(0,r.__)("Charts","dokan-lite"),menu:(0,F.jsx)(u.EllipsisMenu,{label:(0,r.__)("Choose which charts to display","dokan-lite"),renderContent:({onToggle:e})=>(0,F.jsxs)(o.Fragment,{children:[(0,F.jsx)(u.MenuTitle,{children:(0,r.__)("Charts","dokan-lite")}),me({hiddenBlocks:a,onToggleHiddenBlock:b}),(0,F.jsx)(t,{onToggle:e,onMove:p,onRemove:h,isFirst:i,isLast:d,onTitleBlur:y,onTitleChange:k,titleInput:g})]})}),className:"has-interval-select",children:[he({chartInterval:j,setInterval:e=>{P(e),S({dashboard_chart_interval:e}),(0,m.recordEvent)("dash_charts_interval",{interval:e})},query:D,defaultDateRange:C}),(0,F.jsxs)(c.NavigableMenu,{className:"woocommerce-chart__types",orientation:"horizontal",role:"menubar",children:[(0,F.jsx)(c.Button,{className:(0,l.A)("woocommerce-chart__type-button",{"woocommerce-chart__type-button-selected":!D.chartType||"line"===D.chartType}),title:(0,r.__)("Line chart","dokan-lite"),"aria-checked":"line"===D.chartType,role:"menuitemradio",tabIndex:"line"===D.chartType?0:-1,onClick:O("line"),children:(0,F.jsx)(n.A,{})}),(0,F.jsx)(c.Button,{className:(0,l.A)("woocommerce-chart__type-button",{"woocommerce-chart__type-button-selected":"bar"===D.chartType}),title:(0,r.__)("Bar chart","dokan-lite"),"aria-checked":"bar"===D.chartType,role:"menuitemradio",tabIndex:"bar"===D.chartType?0:-1,onClick:O("bar"),children:(0,F.jsx)(s.A,{})})]})]}),ye({hiddenBlocks:a,path:f,query:D,filters:x})]})};ke.propTypes={path:d().string.isRequired,query:d().object.isRequired,defaultDateRange:d().string.isRequired};const be=ke},5331:(e,t,a)=>{t.A=function(e){var t=e.size,a=void 0===t?24:t,r=e.onClick,s=(e.icon,e.className),i=function(e,t){if(null==e)return{};var a,r,l=function(e,t){if(null==e)return{};var a,r,l={},o=Object.keys(e);for(r=0;r<o.length;r++)a=o[r],0<=t.indexOf(a)||(l[a]=e[a]);return l}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)a=o[r],0<=t.indexOf(a)||Object.prototype.propertyIsEnumerable.call(e,a)&&(l[a]=e[a])}return l}(e,o),d=["gridicon","gridicons-line-graph",s,!1,!1,!1].filter(Boolean).join(" ");return l.default.createElement("svg",n({className:d,height:a,width:a,onClick:r},i,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),l.default.createElement("g",null,l.default.createElement("path",{d:"M3 19h18v2H3zm3-3c1.1 0 2-.9 2-2 0-.5-.2-1-.5-1.3L8.8 10H9c.5 0 1-.2 1.3-.5l2.7 1.4v.1c0 1.1.9 2 2 2s2-.9 2-2c0-.5-.2-.9-.5-1.3L17.8 7h.2c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2c0 .5.2 1 .5 1.3L15.2 9H15c-.5 0-1 .2-1.3.5L11 8.2V8c0-1.1-.9-2-2-2s-2 .9-2 2c0 .5.2 1 .5 1.3L6.2 12H6c-1.1 0-2 .9-2 2s.9 2 2 2z"})))};var r,l=(r=a(1609))&&r.__esModule?r:{default:r},o=["size","onClick","icon","className"];function n(){return n=Object.assign?Object.assign.bind():function(e){for(var t,a=1;a<arguments.length;a++)for(var r in t=arguments[a])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},n.apply(this,arguments)}},6283:(e,t,a)=>{t.A=function(e){var t=e.size,a=void 0===t?24:t,r=e.onClick,s=(e.icon,e.className),i=function(e,t){if(null==e)return{};var a,r,l=function(e,t){if(null==e)return{};var a,r,l={},o=Object.keys(e);for(r=0;r<o.length;r++)a=o[r],0<=t.indexOf(a)||(l[a]=e[a]);return l}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)a=o[r],0<=t.indexOf(a)||Object.prototype.propertyIsEnumerable.call(e,a)&&(l[a]=e[a])}return l}(e,o),d=["gridicon","gridicons-stats-alt",s,!1,!1,!!function(e){return 0==e%18}(a)&&"needs-offset-y"].filter(Boolean).join(" ");return l.default.createElement("svg",n({className:d,height:a,width:a,onClick:r},i,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),l.default.createElement("g",null,l.default.createElement("path",{d:"M21 21H3v-2h18v2zM8 10H4v7h4v-7zm6-7h-4v14h4V3zm6 3h-4v11h4V6z"})))};var r,l=(r=a(1609))&&r.__esModule?r:{default:r},o=["size","onClick","icon","className"];function n(){return n=Object.assign?Object.assign.bind():function(e){for(var t,a=1;a<arguments.length;a++)for(var r in t=arguments[a])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},n.apply(this,arguments)}}}]);