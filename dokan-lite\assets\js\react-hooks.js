(()=>{"use strict";var e={1455:e=>{e.exports=window.wp.apiFetch},3832:e=>{e.exports=window.wp.url},6087:e=>{e.exports=window.wp.element},7143:e=>{e.exports=window.wp.data}},t={};function r(o){var n=t[o];if(void 0!==n)return n.exports;var a=t[o]={exports:{}};return e[o](a,a.exports,r),a.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};r.r(o),r.d(o,{useCategories:()=>h,useCurrentUser:()=>u,useCustomerById:()=>y,useCustomerSearch:()=>g,useMutationObserver:()=>i,usePermission:()=>l,useProducts:()=>m,useWindowDimensions:()=>a});var n=r(6087);function a(){const e=(0,n.useCallback)(()=>({width:"undefined"!=typeof window?window.innerWidth:null,height:"undefined"!=typeof window?window.innerHeight:null}),[]),[t,r]=(0,n.useState)(e());return(0,n.useEffect)(()=>{if("undefined"==typeof window)return;const t=()=>{window.requestAnimationFrame(()=>{r(e())})};return window.addEventListener("resize",t),t(),()=>{window.removeEventListener("resize",t)}},[e]),t}var s=r(7143);const u=()=>(0,s.useSelect)(e=>e("dokan/core").getCurrentUser(),[]),i=(e,t,r)=>{const o=new MutationObserver(t);return o.observe(e,r),o},l=(e="dokandar")=>(0,s.useSelect)(t=>"string"==typeof e?t("dokan/core").hasCap(e):!!e?.length&&e.every(e=>t("dokan/core").hasCap(e)),[e]);var c=r(1455),d=r.n(c),w=r(3832);const g=()=>{const[e,t]=(0,n.useState)([]),[r,o]=(0,n.useState)(!1),[a,s]=(0,n.useState)(null),[u,i]=(0,n.useState)(""),l=(0,n.useCallback)(async e=>{o(!0),s(null);try{const r=await d()({path:(0,w.addQueryArgs)("/dokan/v1/customers/search",{search:e})}),o=Array.isArray(r)?r:[];return t(o),o}catch(e){const r=e instanceof Error?e:new Error("Failed to fetch customers");throw s(r),t([]),r}finally{o(!1)}},[]);return{customers:e,isLoading:r,error:a,searchCustomers:(0,n.useCallback)(async e=>(i(e),await l(e)),[l]),refresh:(0,n.useCallback)(()=>{l(u)},[l,u])}},y=()=>{const[e,t]=(0,n.useState)(null),[r,o]=(0,n.useState)(!1),[a,s]=(0,n.useState)(null),[u,i]=(0,n.useState)(null),l=(0,n.useRef)(null),c=(0,n.useCallback)(async e=>{o(!0),s(null),l.current=new AbortController;const{signal:r}=l.current;try{const o=await d()({path:`/dokan/v1/customers/${e}`,signal:r});return t(o),o}catch(e){if(!r.aborted){const r=e instanceof Error?e:new Error("Failed to fetch customer");throw s(r),t(null),r}console.log("Fetch aborted")}finally{o(!1)}},[]);return{customer:e,isLoading:r,error:a,fetchCustomerById:(0,n.useCallback)(async e=>(i(e),await c(e)),[c]),refresh:(0,n.useCallback)(()=>{null!==u&&c(u)},[c,u]),abort:(0,n.useCallback)(()=>{l.current&&l.current.abort()},[])}},f=window.dokan.productCategoriesStore;var p=r.n(f);function h(e={}){return(0,s.useSelect)(t=>({categories:t(p()).getItems(e),totalItems:t(p()).getCategoryQueryTotalCount(e),totalPages:t(p()).getCategoryQueryTotalPages(e),isLoading:t(p()).isCategoriesLoading(),error:t(p()).getCategoryError()}),[JSON.stringify(e)])}const C=window.dokan.productsStore;var b=r.n(C);function m(e={}){return(0,s.useSelect)(t=>({products:t(b()).getItems(e),totalItems:t(b()).getQueryTotalCount(e),totalPages:t(b()).getQueryTotalPages(e),isLoading:t(b()).isLoading(),error:t(b()).getError()}),[JSON.stringify(e)])}(window.dokan=window.dokan||{}).reactHooks=o})();