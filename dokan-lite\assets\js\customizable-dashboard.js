"use strict";(self.webpackChunkdokan=self.webpackChunkdokan||[]).push([[3404],{2326:(e,t,o)=>{o.d(t,{A:()=>x});var s=o(7723),n=o(6427),r=o(6087),i=o(9491),a=o(8846),l=o(7143),d=o(5556),c=o.n(d),p=o(6476),u=o(314),h=o(4908),m=o(3317),_=o(9418);const g=["a","b","em","i","strong","p","br"],k=["target","href","rel","name","download"];var v=o(9411),y=o(790);class b extends r.Component{getFormattedHeaders(){return this.props.headers.map((e,t)=>({isLeftAligned:0===t,hiddenByDefault:!1,isSortable:!1,key:e.label,label:e.label}))}getFormattedRows(){return this.props.rows.map(e=>e.map(e=>{return{display:(0,y.jsx)("div",{dangerouslySetInnerHTML:(t=(0,v.Tv)(e.display),{__html:_.A.sanitize(t,{ALLOWED_TAGS:g,ALLOWED_ATTR:k})})}),value:e.value};var t}))}render(){const{isRequesting:e,isError:t,totalRows:o,title:r}=this.props,i="woocommerce-leaderboard";if(t)return(0,y.jsx)(m.A,{className:i});const l=this.getFormattedRows();return e||0!==l.length?(0,y.jsx)(a.TableCard,{className:i,headers:this.getFormattedHeaders(),isLoading:e,rows:l,rowsPerPage:o,showMenu:!1,title:r,totalRows:o}):(0,y.jsxs)(n.Card,{className:i,children:[(0,y.jsx)(n.CardHeader,{children:(0,y.jsx)(h.Text,{size:16,weight:600,as:"h3",color:"#23282d",children:r})}),(0,y.jsx)(n.CardBody,{size:null,children:(0,y.jsx)(a.EmptyTable,{children:(0,s.__)("No data recorded for the selected time period.","dokan-lite")})})]})}}b.propTypes={headers:c().arrayOf(c().shape({label:c().string})),id:c().string.isRequired,query:c().object,rows:c().arrayOf(c().arrayOf(c().shape({display:c().node,value:c().oneOfType([c().string,c().number,c().bool])}))).isRequired,title:c().string.isRequired,totalRows:c().number.isRequired},b.defaultProps={rows:[],isError:!1,isRequesting:!1};const x=(0,i.compose)((0,l.withSelect)((e,t)=>{const{id:o,query:s,totalRows:n,filters:r}=t,{woocommerce_default_date_range:i}=e(u.SETTINGS_STORE_NAME).getSetting("wc_admin","wcAdminSettings"),a=(0,u.getFilterQuery)({filters:r,query:s}),l={id:o,per_page:n,persisted_query:(0,p.getPersistedQuery)(s),query:s,select:e,defaultDateRange:i||"period=month&compare=previous_year",filterQuery:a};return(0,u.getLeaderboard)(l)}))(b)},3317:(e,t,o)=>{o.d(t,{A:()=>d});var s=o(7723),n=o(5556),r=o.n(n),i=o(8846),a=o(790);function l({className:e}){const t=(0,s.__)("There was an error getting your stats. Please try again.","dokan-lite"),o=(0,s.__)("Reload","dokan-lite");return(0,a.jsx)(i.EmptyContent,{className:e,title:t,actionLabel:o,actionCallback:()=>{window.location.reload()}})}l.propTypes={className:r().string};const d=l},7295:(e,t,o)=>{o.r(t),o.d(t,{default:()=>M});var s=o(7723),n=o(6087),r=o(9491),i=o(8468),a=o(6427),l=o(2619),d=o(7677),c=o(4253),p=o(7143),u=o(8846),h=o(314),m=o(6476),_=o(7374),g=o(3306),k=o(4111),v=o(2809),y=o(6447),b=o(790);const x=(0,n.lazy)(()=>o.e(1133).then(o.bind(o,2480))),f=((0,n.lazy)(()=>o.e(823).then(o.bind(o,6751))),(0,n.lazy)(()=>o.e(6115).then(o.bind(o,1461)))),w=(0,l.applyFilters)("woocommerce_dashboard_default_sections",[{key:"store-performance",component:e=>(0,b.jsx)(n.Suspense,{fallback:(0,b.jsx)(u.Spinner,{}),children:(0,b.jsx)(f,{...e})}),title:(0,s.__)("Performance","dokan-lite"),isVisible:!0,icon:v.A,hiddenBlocks:["coupons/amount","coupons/orders_count","downloads/download_count","taxes/order_tax","taxes/total_tax","taxes/shipping_tax","revenue/shipping","orders/avg_order_value","revenue/refunds","revenue/gross_sales"]},{key:"charts",component:e=>(0,b.jsx)(n.Suspense,{fallback:(0,b.jsx)(u.Spinner,{}),children:(0,b.jsx)(x,{...e})}),title:(0,s.__)("Charts","dokan-lite"),isVisible:!0,icon:y.A,hiddenBlocks:["orders_avg_order_value","avg_items_per_order","products_items_sold","revenue_total_sales","revenue_refunds","coupons_amount","coupons_orders_count","revenue_shipping","taxes_total_tax","taxes_order_tax","taxes_shipping_tax","downloads_download_count"]}]);var j=o(3349),T=o(9671),C=o(3526);class A extends n.Component{constructor(e){super(e),this.onMoveUp=this.onMoveUp.bind(this),this.onMoveDown=this.onMoveDown.bind(this)}onMoveUp(){const{onMove:e,onToggle:t}=this.props;e(-1),t()}onMoveDown(){const{onMove:e,onToggle:t}=this.props;e(1),t()}render(){const{onRemove:e,isFirst:t,isLast:o,onTitleBlur:r,onTitleChange:i,titleInput:l}=this.props;return(0,b.jsxs)(n.Fragment,{children:[(0,b.jsx)("div",{className:"woocommerce-ellipsis-menu__item",children:(0,b.jsx)(a.TextControl,{label:(0,s.__)("Section title","dokan-lite"),onBlur:r,onChange:i,required:!0,value:l})}),(0,b.jsxs)("div",{className:"woocommerce-dashboard-section-controls",children:[!t&&(0,b.jsxs)(u.MenuItem,{isClickable:!0,onInvoke:this.onMoveUp,children:[(0,b.jsx)(d.A,{icon:(0,b.jsx)(T.A,{}),label:(0,s.__)("Move up","dokan-lite"),size:20,className:"icon-control"}),(0,s.__)("Move up","dokan-lite")]}),!o&&(0,b.jsxs)(u.MenuItem,{isClickable:!0,onInvoke:this.onMoveDown,children:[(0,b.jsx)(d.A,{icon:(0,b.jsx)(C.A,{}),size:20,label:(0,s.__)("Move down","dokan-lite"),className:"icon-control"}),(0,s.__)("Move down","dokan-lite")]}),(0,b.jsxs)(u.MenuItem,{isClickable:!0,onInvoke:e,children:[(0,b.jsx)(d.A,{icon:j.A,size:20,label:(0,s.__)("Remove block","dokan-lite"),className:"icon-control"}),(0,s.__)("Remove section","dokan-lite")]})]})]})}}const R=A;o(2326);class S extends n.Component{constructor(e){super(e);const{title:t}=e;this.state={titleInput:t},this.onToggleHiddenBlock=this.onToggleHiddenBlock.bind(this),this.onTitleChange=this.onTitleChange.bind(this),this.onTitleBlur=this.onTitleBlur.bind(this)}onTitleChange(e){this.setState({titleInput:e})}onTitleBlur(){const{onTitleUpdate:e,title:t}=this.props,{titleInput:o}=this.state;""===o?this.setState({titleInput:t}):e&&e(o)}onToggleHiddenBlock(e){return()=>{const t=(0,i.xor)(this.props.hiddenBlocks,[e]);this.props.onChangeHiddenBlocks(t)}}render(){const{component:e,...t}=this.props,{titleInput:o}=this.state;return(0,b.jsx)("div",{className:"woocommerce-dashboard-section",children:(0,b.jsx)(e,{onTitleChange:this.onTitleChange,onTitleBlur:this.onTitleBlur,onToggleHiddenBlock:this.onToggleHiddenBlock,titleInput:o,controls:R,...t})})}}var B=o(4171);const E=(0,l.applyFilters)("dokan_analytics_dashboard_report_filters",[]),M=(0,r.compose)((0,p.withSelect)(e=>{const{woocommerce_default_date_range:t}=e(h.SETTINGS_STORE_NAME).getSetting("wc_admin","wcAdminSettings");return{defaultDateRange:t||"period=month&compare=previous_year"}}))(({defaultDateRange:e,path:t,query:o})=>{const{updateUserPreferences:r,...p}=(0,h.useUserPreferences)(),v=(0,n.useMemo)(()=>(e=>{if(!e||0===e.length)return w.reduce((e,t)=>[...e,{...t}],[]);const t=w.map(e=>e.key),o=e.map(e=>e.key),s=new Set([...o,...t]),n=[];return s.forEach(t=>{const o=w.find(e=>e.key===t);if(!o)return;const s=e.find(e=>e.key===t);s&&delete s.icon,n.push({...o,...s})}),n})(p.dashboard_sections),[p.dashboard_sections]),y=e=>{r({dashboard_sections:e})},x=(e,t)=>{const o=v.map(o=>(delete o.icon,o.key===e?{...o,...t}:o));y(o)},f=e=>t=>{(0,g.recordEvent)("dash_section_rename",{key:e}),x(e,{title:t})},j=(e,t)=>()=>{t&&t();const o=v.findIndex(t=>e===t.key),s=v.splice(o,1).shift();s.isVisible=!s.isVisible,v.push(s),s.isVisible?(0,g.recordEvent)("dash_section_add",{key:s.key}):(0,g.recordEvent)("dash_section_remove",{key:s.key}),y(v)},T=(e,t)=>{const o=v.splice(e,1).shift(),s=e+t;if(v[t<0?s:s-1].isVisible||0===e||e===v.length-1){v.splice(s,0,o),y(v);const e={key:o.key,direction:t>0?"down":"up"};(0,g.recordEvent)("dash_section_order_change",e)}else T(e,t+t)},C=()=>{const e=v.filter(e=>!1===e.isVisible);return 0===e.length?null:(0,b.jsx)(a.Dropdown,{popoverProps:{placement:"top"},className:"woocommerce-dashboard-section__add-more",renderToggle:({onToggle:e,isOpen:t})=>(0,b.jsx)(a.Button,{onClick:e,title:(0,s.__)("Add more sections","dokan-lite"),"aria-expanded":t,children:(0,b.jsx)(d.A,{icon:c.A})}),renderContent:({onToggle:t})=>(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(u.H,{children:(0,s.__)("Dashboard Sections","dokan-lite")}),(0,b.jsx)("div",{className:"woocommerce-dashboard-section__add-more-choices",children:e.map(e=>(0,b.jsxs)(a.Button,{onClick:j(e.key,t),className:"woocommerce-dashboard-section__add-more-btn",title:(0,s.sprintf)((0,s.__)("Add %s section","dokan-lite"),e.title),children:[(0,b.jsx)(d.A,{className:e.key+"__icon",icon:e.icon,size:30}),(0,b.jsx)("span",{className:"woocommerce-dashboard-section__add-more-btn-title",children:e.title})]},e.key))})]})})};return(0,l.applyFilters)("dokan_should_convert_anchors",!0)&&(0,n.useEffect)(()=>{const e=document.querySelector(".customizable-dashboard");if(!e)return;const t=e=>{const t=e.target.closest("a");t&&!t.closest(".woocommerce-filters")&&(e.preventDefault(),e.stopPropagation())},o=e=>{const t=document.activeElement;t&&t.closest(".customizable-dashboard")&&!t.closest(".woocommerce-filters")&&(e.preventDefault(),e.returnValue="")};return e.addEventListener("click",t,!0),window.addEventListener("beforeunload",o),()=>{e.removeEventListener("click",t,!0),window.removeEventListener("beforeunload",o)}},[]),(0,b.jsx)(k.CurrencyContext.Provider,{value:(0,k.getFilteredCurrencyInstance)((0,m.getQuery)()),children:(0,b.jsx)("div",{className:"customizable-dashboard",children:(()=>{const{period:s,compare:r,before:a,after:l}=(0,_.getDateParamsFromQuery)(o,e),{primary:d,secondary:c}=(0,_.getCurrentDates)(o,e),p={period:s,compare:r,before:a,after:l,primaryDate:d,secondaryDate:c},h=v.filter(e=>e.isVisible).map(e=>e.key);return(0,b.jsxs)(n.Fragment,{children:[(0,b.jsx)(u.ReportFilters,{report:"dashboard",query:{...o,seller_id:B.w?.seller_id||"0"},path:t,dateQuery:p,isoDateFormat:_.isoDateFormat,filters:E}),v.map((s,n)=>{return s.isVisible?(0,b.jsx)(S,{component:s.component,hiddenBlocks:s.hiddenBlocks,onChangeHiddenBlocks:(r=s.key,e=>{x(r,{hiddenBlocks:e})}),onTitleUpdate:f(s.key),path:t,defaultDateRange:e,query:{...o,seller_id:B.w?.seller_id||"0"},title:s.title,onMove:(0,i.partial)(T,n),onRemove:j(s.key),isFirst:s.key===h[0],isLast:s.key===h[h.length-1],filters:E},s.key):null;var r}),C()]})})()})})})}}]);