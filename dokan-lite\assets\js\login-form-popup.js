(()=>{var o;o=jQuery,dokan.login_form_popup={form_html:"",form_title:"",init:function(){o("body").on("dokan:login_form_popup:show",this.get_form),o("body").on("submit","#dokan-login-form-popup-form",this.submit_form),o("body").on("dokan:login_form_popup:working",this.working),o("body").on("dokan:login_form_popup:done_working",this.done_working)},get_form:function(n,a){dokan.login_form_popup.form_html?dokan.login_form_popup.show_popup():(a=o.extend(!0,{nonce:dokan.nonce,action:"dokan_get_login_form"},a),o("body").trigger("dokan:login_form_popup:fetching_form"),o.ajax({url:dokan.ajaxurl,method:"get",dataType:"json",data:{_wpnonce:a.nonce,action:a.action}}).done(function(n){dokan.login_form_popup.form_html=n.data.html,dokan.login_form_popup.form_title=n.data.title,dokan.login_form_popup.show_popup(),o("body").trigger("dokan:login_form_popup:fetched_form")}))},show_popup:function(){o("body").append('<div id="dokan-modal-login-form-popup"></div>');const n=o("#dokan-modal-login-form-popup").iziModal({headerColor:dokan.modal_header_color,overlayColor:"rgba(0, 0, 0, 0.8)",width:690,onOpened:()=>{o("body").trigger("dokan:login_form_popup:opened")}});n.iziModal("setTitle",dokan.login_form_popup.form_title),n.iziModal("setContent",dokan.login_form_popup.form_html),n.iziModal("open")},submit_form:function(n){n.preventDefault();var a=o(this).serialize(),i=o(".dokan-login-form-error","#dokan-login-form-popup-form");i.removeClass("has-error").text(""),o("body").trigger("dokan:login_form_popup:working"),o.ajax({url:dokan.ajaxurl,method:"post",dataType:"json",data:{_wpnonce:dokan.nonce,action:"dokan_login_user",form_data:a}}).done(function(n){o("body").trigger("dokan:login_form_popup:logged_in",n),o("#dokan-modal-login-form-popup").iziModal("close")}).always(function(){o("body").trigger("dokan:login_form_popup:done_working")}).fail(function(o){o.responseJSON&&o.responseJSON.data&&o.responseJSON.data.message&&i.addClass("has-error").text(o.responseJSON.data.message)})},working:function(){o("fieldset","#dokan-login-form-popup-form").prop("disabled",!0),o("#dokan-login-form-submit-btn").addClass("dokan-hide"),o("#dokan-login-form-working-btn").removeClass("dokan-hide")},done_working:function(){o("fieldset","#dokan-login-form-popup-form").prop("disabled",!1),o("#dokan-login-form-submit-btn").removeClass("dokan-hide"),o("#dokan-login-form-working-btn").addClass("dokan-hide")}},dokan.login_form_popup.init()})();