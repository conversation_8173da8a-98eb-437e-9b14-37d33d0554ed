<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="372.768" height="236.339" viewBox="0 0 372.768 236.339"><defs><linearGradient id="a" x1="0.5" y1="0.327" x2="0.5" y2="1" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#fff"/><stop offset="1" stop-color="#fff"/></linearGradient><linearGradient id="b" x1="0.5" y1="-0.551" x2="0.5" y2="2.063" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#ff9e9e"/><stop offset="1" stop-color="red"/></linearGradient><linearGradient id="c" y1="-0.551" y2="2.063" xlink:href="#b"/><filter id="d" x="136.8" y="107.16" width="101.568" height="101.914" filterUnits="userSpaceOnUse"><feOffset dy="7" input="SourceAlpha"/><feGaussianBlur stdDeviation="8" result="e"/><feFlood flood-color="#ab0000" flood-opacity="0.129"/><feComposite operator="in" in2="e"/><feComposite in="SourceGraphic"/></filter><linearGradient id="f" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#72d0ff"/><stop offset="1" stop-color="#349efa"/></linearGradient><filter id="g" x="204" y="107.16" width="101.568" height="101.338" filterUnits="userSpaceOnUse"><feOffset dy="7" input="SourceAlpha"/><feGaussianBlur stdDeviation="8" result="h"/><feFlood flood-color="#ab0000" flood-opacity="0.129"/><feComposite operator="in" in2="h"/><feComposite in="SourceGraphic"/></filter><linearGradient id="i" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#ffa8e6"/><stop offset="1" stop-color="#e850ff"/></linearGradient><filter id="j" x="271.2" y="107.16" width="101.568" height="101.914" filterUnits="userSpaceOnUse"><feOffset dy="7" input="SourceAlpha"/><feGaussianBlur stdDeviation="8" result="k"/><feFlood flood-color="#ab0000" flood-opacity="0.129"/><feComposite operator="in" in2="k"/><feComposite in="SourceGraphic"/></filter><linearGradient id="l" x1="0.5" y1="-0.081" x2="0.5" y2="1" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#a3a8ff"/><stop offset="1" stop-color="#6870ff"/></linearGradient><linearGradient id="m" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#f482e6"/><stop offset="1" stop-color="#ad6fff"/></linearGradient></defs><g transform="translate(0)"><g transform="translate(76.96 52.48)"><rect width="185.6" height="146.56" transform="translate(14.72 15.36)" fill="#ff9f9f"/><rect width="176" height="161.92" transform="translate(14.72)" opacity="0.703" fill="url(#a)"/><rect width="41.6" height="146.56" transform="translate(14 15)" fill="url(#b)"/><rect width="19.169" height="1.659" rx="0.829" transform="translate(25 65.52)" fill="#fff" opacity="0.413"/><rect width="19.169" height="1.659" rx="0.829" transform="translate(25 79.519)" fill="#fff" opacity="0.413"/><rect width="19.169" height="1.659" rx="0.829" transform="translate(25 93.518)" fill="#fff" opacity="0.413"/><rect width="19.169" height="1.659" rx="0.829" transform="translate(25 107.517)" fill="#fff" opacity="0.413"/><rect width="19.169" height="1.659" rx="0.829" transform="translate(25 121.516)" fill="#fff" opacity="0.413"/><rect width="19.169" height="1.659" rx="0.829" transform="translate(25 135.514)" fill="#fff" opacity="0.413"/><path d="M0,0H177.28V16.64L169.6,44.8H0Z" transform="translate(14.08 15.36)" fill="#5d0000" opacity="0.114"/><g transform="translate(0 15.36)"><path d="M162.251,36.851,174.5,0h15.96L178.476,36.851Zm-32.45,0L142.585,0h15.96L146.026,36.851Zm-32.45,0L110.667,0h15.96L113.576,36.851Zm-32.45,0L78.747,0h15.96L81.125,36.851Zm-32.45,0L46.828,0h15.96L48.676,36.851ZM0,36.851,14.909,0h15.96L16.225,36.851Z" transform="translate(0 0)" fill="url(#c)"/><path d="M129.8,36.851,142.32,0h15.96L146.026,36.851Zm-32.45,0L110.4,0h15.96L113.576,36.851Zm-32.45,0L78.482,0h15.96L81.125,36.851Zm-32.45,0L46.562,0h15.96L48.676,36.851ZM0,36.851,14.644,0H30.6L16.225,36.851Z" transform="translate(16.225 0)" fill="#fff"/></g><rect width="185.6" height="15.36" transform="translate(14.72)" fill="#ffd5d5"/><path d="M16.64,2.56A2.56,2.56,0,1,1,19.2,5.12,2.56,2.56,0,0,1,16.64,2.56Zm-8.321,0A2.56,2.56,0,1,1,10.88,5.12,2.561,2.561,0,0,1,8.32,2.56ZM0,2.56A2.56,2.56,0,1,1,2.56,5.12,2.56,2.56,0,0,1,0,2.56Z" transform="translate(23.04 5.12)" fill="red"/><rect width="11.52" height="1.92" rx="0.96" transform="translate(132.48 7.04)" fill="#fff"/><rect width="11.52" height="1.92" rx="0.96" transform="translate(155.52 7.04)" fill="#fff"/><rect width="11.52" height="1.92" rx="0.96" transform="translate(178.56 7.04)" fill="#fff"/><g transform="translate(83.84 71.68)"><g transform="matrix(1, 0, 0, 1, -160.8, -124.16)" filter="url(#d)"><rect width="53.568" height="53.914" rx="1.659" transform="translate(160.8 124.16)" fill="#fff"/></g><path d="M3.61,17.05l.044-.629.033-.463.04-.551.032-.468.041-.546.032-.468.041-.55.032-.464L4.838,0H7.309a3.842,3.842,0,0,0,3.819,3.862A3.842,3.842,0,0,0,14.95,0h2.472l.055.761,4.181,5.032L18.049,8.714l.3,4.2.032.464.041.55.033.468.04.546.033.468.041.551.033.463.045.629ZM0,5.793,4.813,0,4.127,9.134Z" transform="translate(16.128 17.856)" fill="url(#f)"/><rect width="19.169" height="1.659" rx="0.829" transform="translate(0 64.466)" fill="red"/><rect width="47.278" height="1.659" rx="0.829" transform="translate(0 70.272)" fill="#d29795"/><rect width="32.348" height="1.659" rx="0.829" transform="translate(0 76.078)" fill="#d29795"/><g transform="translate(67.2)"><g transform="matrix(1, 0, 0, 1, -228, -124.16)" filter="url(#g)"><rect width="53.568" height="53.338" rx="1.659" transform="translate(228 124.16)" fill="#fff"/></g><path d="M24.135,6.065c-.8-.149-2.156-.15-3.772-.455l.222,1.349a.421.421,0,0,1-.342.486.4.4,0,0,1-.069.005.417.417,0,0,1-.41-.352l-.231-1.4a.47.47,0,0,0-.314-.348l-.252-.072c-.147-.043-.246.044-.221.2l.116.707a.422.422,0,0,1-.342.486.445.445,0,0,1-.069.006.418.418,0,0,1-.41-.353l-.192-1.165a.561.561,0,0,0-.3-.384q-.149-.062-.3-.131c-.14-.063-.231.006-.206.16l.1.6a.421.421,0,0,1-.342.486.447.447,0,0,1-.069.006.419.419,0,0,1-.41-.353l-.2-1.24a.7.7,0,0,0-.283-.422c-.124-.078-.249-.159-.373-.243s-.209-.034-.184.119l.143.869a.42.42,0,0,1-.342.485.36.36,0,0,1-.069.006.418.418,0,0,1-.41-.353l-.354-2.154c-.083-.1-.159-.19-.22-.272A7.119,7.119,0,0,0,11.641.083a1.344,1.344,0,0,0-1.747.931,2.442,2.442,0,0,0,.153,1.1,1.583,1.583,0,0,0,.294.549c.2.234.278.482.044.683A9.758,9.758,0,0,1,7.628,5.058c-2.314.869-2.719-.328-2.719-.328A5.467,5.467,0,0,0,3.577,2.441a1.529,1.529,0,0,0-1.059-.178,1.406,1.406,0,0,0-.3.113A7.532,7.532,0,0,0,6.714,7.791c4.549,2.228,13.607,2.672,15.3,2.737.154.006.155.032,0,.058a35.939,35.939,0,0,1-9.653.3C7.132,10.235,2.311,8.706,1.049,4.4c-.026.094-.051.182-.075.258a7.065,7.065,0,0,0-.431,2.5A3.975,3.975,0,0,1,.3,8.229a4.693,4.693,0,0,0-.285,1.5C0,10.359,0,10.9,0,11.275a1.173,1.173,0,0,0,.461.893,2.694,2.694,0,0,0,.7.3c.3.078.758.217,1.05.312a2.15,2.15,0,0,0,.438.093c.305.032.662-.01.8,0s.243.078.243.158.16.145.358.145a.654.654,0,0,0,.486-.188.439.439,0,0,1,.312-.188.187.187,0,0,1,.186.188c0,.1.15.188.335.188a.56.56,0,0,0,.451-.2.4.4,0,0,1,.29-.2.428.428,0,0,1,.3.2.556.556,0,0,0,.429.2.533.533,0,0,0,.416-.188c.064-.1.214-.1.335,0a1.122,1.122,0,0,0,.625.188c.223,0,.431-.068.463-.152a.293.293,0,0,1,.266-.153.453.453,0,0,1,.324.153.577.577,0,0,0,.5.07.784.784,0,0,0,.486-.294.156.156,0,0,1,.267-.048,1.133,1.133,0,0,0,.543.223.883.883,0,0,0,.567-.082.55.55,0,0,1,.37-.106.23.23,0,0,1,.185.211c0,.1.177.176.393.176a1.043,1.043,0,0,0,.533-.117.459.459,0,0,1,.335-.082c.109.019.2.088.2.152s.109.117.243.117a1.216,1.216,0,0,0,.44-.094.686.686,0,0,1,.335-.07.165.165,0,0,1,.139.153c0,.071.249.129.555.129H15.4c.307,0,.555-.084.555-.188a.2.2,0,0,1,.393,0c0,.1.217.188.486.188a1.681,1.681,0,0,0,.694-.129.365.365,0,0,1,.382,0,1.625,1.625,0,0,0,.717.129,1.038,1.038,0,0,0,.66-.164.69.69,0,0,1,.312-.223.26.26,0,0,1,.289.105c.051.091.336.115.638.057l.09-.018c.3-.06.576-.208.615-.332s.127-.233.2-.246a.352.352,0,0,1,.266.153.533.533,0,0,0,.509.07.63.63,0,0,0,.428-.434.556.556,0,0,1,.3-.41c.134-.046.243.023.243.152s.233.145.52.036.522-.305.522-.435a.342.342,0,0,1,.173-.305.277.277,0,0,1,.278.035c.057.058.228.017.382-.094a.907.907,0,0,0,.324-.387,2.25,2.25,0,0,1,.487-.529,2.624,2.624,0,0,0,.785-2.6C26.183,5.808,25.836,6.383,24.135,6.065Z" transform="translate(13.248 19.584)" fill="url(#i)"/><rect width="19.169" height="1.659" rx="0.829" transform="translate(0 64.466)" fill="red"/><rect width="47.278" height="1.659" rx="0.829" transform="translate(0 70.272)" fill="#d29795"/><rect width="32.348" height="1.659" rx="0.829" transform="translate(0 76.078)" fill="#d29795"/></g><g transform="translate(134.4)"><g transform="matrix(1, 0, 0, 1, -295.2, -124.16)" filter="url(#j)"><rect width="53.568" height="53.914" rx="1.659" transform="translate(295.2 124.16)" fill="#fff"/></g><path d="M6.2,21.573,5.057,5.825,3.907,21.573H0V4.549A2.469,2.469,0,0,0,2.211,2.022H4.634c0,1.326-.037,2.494.421,2.5h0c.457,0,.42-1.173.42-2.5H7.9a2.469,2.469,0,0,0,2.212,2.526V21.573ZM8.764,1.348h1.348V3.371C8.787,3.279,8.764,1.348,8.764,1.348ZM0,1.348H1.348S1.325,3.279,0,3.371Zm5.057,0v0H0V0H10.112V1.348Z" transform="translate(21.888 15.552)" fill="url(#l)"/><rect width="19.169" height="1.659" rx="0.829" transform="translate(0 64.466)" fill="red"/><rect width="47.278" height="1.659" rx="0.829" transform="translate(0 70.272)" fill="#d29795"/><rect width="32.348" height="1.659" rx="0.829" transform="translate(0 76.078)" fill="#d29795"/></g></g></g><path d="M11,0H67.72a11,11,0,0,1,11,11V34.56a0,0,0,0,1,0,0H0a0,0,0,0,1,0,0V11A11,11,0,0,1,11,0Z" transform="translate(41.12 197.12)" fill="#ffe2e2"/><g transform="translate(53.92 128)"><path d="M19.245.733a16.682,16.682,0,0,1-5.8,6.2H0S21.205-2.679,19.245.733" transform="translate(23.268 62.51)" fill="#233862"/><g transform="translate(31.789 86.614)"><path d="M.32,11.151A1.243,1.243,0,0,0,.1,12.481a.01.01,0,0,0,0,.007,8.262,8.262,0,0,0,2.611,2.864c.157.127.3.242.419.349.032.028.064.056.094.084l.072.065c1.148,1.058.716,1.3,2.777,3.446,2.351,2.446,3.969,2.49,4.43,2.267.389-.19-.4-1.487-1.279-2.945-.042-.07-.084-.142-.128-.213-.117-.194-.233-.391-.349-.585-1.028-1.755-.764-2.578-.52-5.189.2-2.122,3.3-11.855,3.3-11.855L7.026,0S5.669,7.75,4.175,8.944c-.8.636-1.694.644-2.462,1.116q-.081.049-.16.1a5.908,5.908,0,0,0-1.233.991" transform="translate(0.078 0.076)" fill="#ffb27d"/></g><g transform="translate(31.789 96.678)"><path d="M.32,1.092A1.243,1.243,0,0,0,.1,2.421a.01.01,0,0,0,0,.007A9.57,9.57,0,0,0,2.717,5.282c.155.129.293.248.415.358.032.028.064.056.094.084L3.3,5.79c1.148,1.058.716,1.3,2.777,3.446,2.351,2.446,3.969,2.49,4.43,2.267.389-.19-.4-1.487-1.279-2.945a10.018,10.018,0,0,1-4.19-3.088C3.62,3.665,2.676,1.344,1.712,0q-.081.049-.16.1A5.908,5.908,0,0,0,.32,1.092" transform="translate(0.078 0.072)" fill="#233862"/></g><g transform="translate(64.856 85.177)"><path d="M3.6,9.709c.278,1.39,2.26,1.158,4.475.617s1.688.1,4.84-.247,4.106-1.524,4.2-1.992c.086-.416-1.471-.562-3.142-.755-.21-.023-.421-.05-.632-.076C11.46,7.02,10.079,5.8,8.366,4.8,6.744,3.857,4.322,0,4.322,0L0,.342S2.268,3.787,3.145,5.285A5.223,5.223,0,0,1,3.57,8.23,5.546,5.546,0,0,0,3.6,9.709" transform="translate(0.052 0.143)" fill="#ffb27d"/></g><g transform="translate(68.37 92.525)"><path d="M4.541,2.994c2.214-.541,1.688.1,4.84-.247s4.106-1.524,4.2-1.993C13.669.339,12.112.192,10.441,0l-.016.032a8.885,8.885,0,0,1-4.513,1.4C3.774,1.487,1.509.874.033.9A5.553,5.553,0,0,0,.066,2.377c.278,1.39,2.26,1.158,4.475.617" transform="translate(0.074 0.127)" fill="#233862"/></g><path d="M14.828,6.928,10.788,17.8,5.513,32a8.547,8.547,0,0,1-2.8-.256A12.149,12.149,0,0,1,0,30.421S1.465,15.179,4.2,6.429A15.581,15.581,0,0,1,6.548,1.344c4.236-5.006,8.28,5.584,8.28,5.584" transform="translate(37.119 60.463)" fill="#233862"/><path d="M39.081,2.171c-2.647-.854-10.2.243-17.78,1.727L19.713,1.279,3.332,0S-2.074,11.987.886,19.625c.435,1.123,1.511,1.6,2.985,1.62l0,0s9.775.239,12.941.239c8.146,0,17.241-5.6,18.417-5.835,1.926-.385,2.7,3.466,4.621,7.51s18.1,17.908,18.1,17.908a14.237,14.237,0,0,0,3.513-.361,3.459,3.459,0,0,0,1.508-.952S45.051,4.1,39.081,2.171" transform="translate(8.758 47.954)" fill="#493df0"/><g transform="translate(0 0.033)"><path d="M26.67.971a5.809,5.809,0,0,0-3.15-.813,9.47,9.47,0,0,1-1.565-.1C15.749-.78,12.013,8.24,11.793,10.973c-.405,5.038-3.07,5.806-6.821,8.961-7.521,6.326-5.316,13.677-1.2,16.408,2.468,1.639,6.817,0,6.817,0,.062.043,11.9-6.615,17.728-9.827C37.059,25.271,30.184,3.062,26.67.971" transform="translate(0.06 0.127)"/></g><path d="M7.981,9.172c.026.017-2.87,1.005-4.806.148-1.822-.8-3.2-3.8-3.174-3.8C.829,5.478,1.739,4.754,2.439,0l.353.127L8.725,2.261s-.43,1.9-.708,3.716c-.234,1.522-.36,2.98-.036,3.195" transform="translate(15.504 14.768)" fill="#ffb27d"/><path d="M5.933,2.135s-.43,1.9-.708,3.716C1.817,5.237.48,1.842,0,0Z" transform="translate(18.297 14.895)" fill="#ed975d"/><path d="M7.41,14.278S-.706,13.188.05,8.144.268-.438,5.482.044s5.93,2.558,6.088,4.3S9.541,14.431,7.41,14.278" transform="translate(17.702 4.641)" fill="#ffb27d"/><path d="M13.6,2.028s-5.2,4.478-8.73,4.839S0,6.093,0,6.093A13.8,13.8,0,0,0,3.337,1.572,2.485,2.485,0,0,1,5.386.107C7.909-.111,12.694-.21,13.6,2.028" transform="translate(15.373 4.195)"/><path d="M0,.971A12.8,12.8,0,0,1,2.286,3.9a9.826,9.826,0,0,1,.266,5.223S5.26,3.855,3.727,1.3C2.1-1.417,0,.971,0,.971" transform="translate(25.728 4.851)"/><path d="M21.782,15.8c-.245,16.431-.067,13-1.865,14.556a6.9,6.9,0,0,1-3.253.7c-5.038.335-13.895-.5-14.232-1.427-1.162-3.222-.342-4.223-1.311-9.155-.1-.524-.226-1.092-.373-1.711-.808-3.388-1.6-4.54,1.273-9.983C4.622,3.86,7.324-.141,7.567,0c6.862,4.073,7.415.65,7.415.65S21.9,7.79,21.782,15.8" transform="translate(8.539 20.093)" fill="#fff"/><path d="M10.788,11.372l-5.275,14.2a8.548,8.548,0,0,1-2.8-.256A12.16,12.16,0,0,1,0,23.992S1.465,8.75,4.2,0Z" transform="translate(37.119 66.892)" fill="#493df0"/><path d="M0,0S7.061,1.2,7.89,4.237,13.6,25.149,13.6,25.149s-3.589.645-5.248-1.013S2.581,8.751,2.581,8.751Z" transform="translate(23.521 20.745)" fill="#fff"/><path d="M15.543,17.424C10.5,17.759,1.648,16.929,1.311,16,.149,12.776.969,11.775,0,6.843L2.43,0s-.4,7.62,1.845,9.624,9.2,3.869,10.293,5.543a6.126,6.126,0,0,1,.974,2.257" transform="translate(9.66 33.734)" fill="#ebf3ff"/><path d="M0,.769S4.971-.337,5.773.1A11.674,11.674,0,0,1,8.78,2.71c.246.559-1.785-.424-2.446-.6a5.881,5.881,0,0,0-2.111.462,7.016,7.016,0,0,1-3.783.38Z" transform="translate(19.43 47.184)" fill="#ffb27d"/><g transform="translate(0 20.001)"><path d="M16.1,0S9.427.411,7.117,3.634C3.865,8.17-2.587,27.5,1.11,30.08c4.164,2.9,18.952.76,18.952.76s.492-2.3-.192-3.413S8.058,24.517,7.545,24s6.419-10.014,6.419-11.554S16.1,0,16.1,0" transform="translate(0 0.092)" fill="#fff"/></g><path d="M0,0A9.616,9.616,0,0,0,1.383,1.5a1.018,1.018,0,0,0,.562.12L1.243.361Z" transform="translate(25.443 48.672)" fill="#ffb27d"/><path d="M0,0,3.028,1.47a.54.54,0,0,1-.5.194A7.209,7.209,0,0,1,1.213,1.3Z" transform="translate(25.203 47.288)" fill="#ffb27d"/><path d="M0,17.917H23.952L31.157,0H7.242Z" transform="translate(26.826 34.316)" fill="url(#m)"/><path d="M31.629,0H0V.7H31.348Z" transform="translate(19.43 51.535)" fill="#d3abff"/><path d="M2.327,1.14A.83.83,0,0,0,1.514,0,1.634,1.634,0,0,0,.046,1.14.83.83,0,0,0,.86,2.281,1.634,1.634,0,0,0,2.327,1.14" transform="translate(42.405 42.134)" fill="#fff"/></g><circle cx="21.44" cy="21.44" r="21.44" transform="translate(11 68)" fill="#fff"/><path d="M3.488,14,11.049,0h1.417L4.932,14Zm6.592-1.111a3.387,3.387,0,0,1-1.016-2.474,3.381,3.381,0,0,1,1.016-2.479,3.452,3.452,0,0,1,4.9,0A3.391,3.391,0,0,1,16,10.416a3.39,3.39,0,0,1-1.016,2.474,3.446,3.446,0,0,1-4.9,0ZM1.016,6.04A3.385,3.385,0,0,1,0,3.566,3.385,3.385,0,0,1,1.016,1.092,3.326,3.326,0,0,1,3.469.066,3.326,3.326,0,0,1,5.921,1.092,3.389,3.389,0,0,1,6.937,3.566,3.4,3.4,0,0,1,5.926,6.04,3.323,3.323,0,0,1,3.469,7.066,3.326,3.326,0,0,1,1.016,6.04Z" transform="translate(24 83)" fill="url(#m)"/><ellipse cx="16" cy="16" rx="16" ry="16" transform="translate(0 139)" fill="url(#c)"/><path d="M9.891,12a.351.351,0,0,1-.352-.351V3.188a.352.352,0,0,1,.352-.352h1.758A.351.351,0,0,1,12,3.188v8.461a.35.35,0,0,1-.351.351ZM6.7,12a.351.351,0,0,1-.352-.351V6A.352.352,0,0,1,6.7,5.648H8.461A.352.352,0,0,1,8.813,6v5.648A.351.351,0,0,1,8.461,12ZM3.539,12a.35.35,0,0,1-.351-.351V6.7a.351.351,0,0,1,.351-.352H5.3a.352.352,0,0,1,.352.352v4.946A.351.351,0,0,1,5.3,12ZM.352,12A.351.351,0,0,1,0,11.649V8.813a.352.352,0,0,1,.352-.352H2.11a.351.351,0,0,1,.351.352v2.836A.35.35,0,0,1,2.11,12ZM.2,6.68A1.056,1.056,0,0,1,1.254,5.625a1.043,1.043,0,0,1,.608.2l1.37-.967a1.05,1.05,0,0,1,1.908-.832l1.4-.3a1.053,1.053,0,0,1,1.04-.914,1.035,1.035,0,0,1,.506.135l1.683-1.5a1.041,1.041,0,0,1-.079-.4,1.069,1.069,0,1,1,.549.92L8.558,3.47a1.052,1.052,0,0,1-1.873.945l-1.4.3a1.047,1.047,0,0,1-1.648.719L2.265,6.4A1.053,1.053,0,1,1,.2,6.68Z" transform="translate(10 149)" fill="#fff"/><ellipse cx="16" cy="16" rx="16" ry="16" transform="translate(298 71)" fill="url(#b)"/><path d="M7.637,10.833a2.153,2.153,0,0,1,.062-.514l-4-2.264a2.167,2.167,0,1,1,0-3.111l4-2.264a2.152,2.152,0,0,1-.062-.514A2.182,2.182,0,1,1,9.818,4.333a2.183,2.183,0,0,1-1.6-.7L4.27,5.869a2.155,2.155,0,0,1,0,1.263L8.215,9.365a2.163,2.163,0,1,1-.579,1.468Z" transform="translate(308 81)" fill="#fff"/><circle cx="14" cy="14" r="14" transform="translate(220 3)" fill="url(#c)"/><path d="M7,9a1,1,0,1,1,1,1A1,1,0,0,1,7,9Zm-5,0A1,1,0,1,1,3,10,.995.995,0,0,1,2.005,9ZM3,7.5a1,1,0,0,1-1-1,1.018,1.018,0,0,1,.122-.482L2.8,4.793,1,1H0V0H1.633l.475,1H9.5a.5.5,0,0,1,.5.5.484.484,0,0,1-.062.239L8.15,4.985a1,1,0,0,1-.876.515H3.55L3.1,6.315a.116.116,0,0,0-.014.059.124.124,0,0,0,.124.125H9v1Z" transform="translate(229 12)" fill="#fff"/><circle cx="21" cy="21" r="21" transform="translate(282)" fill="#fff"/><path d="M.527,14a.538.538,0,0,1,0-1.076h1.09V11.846H1.571A1.589,1.589,0,0,1,0,10.242V9.154a.533.533,0,0,1,.527-.538h13.42l.382-.779a.523.523,0,0,1,.472-.3h2.672a.521.521,0,0,1,.439.241.545.545,0,0,1,.04.522l-.922,2.047a3.89,3.89,0,0,0-.215,2.575h.658a.538.538,0,0,1,0,1.076ZM14.1,10.446a.527.527,0,1,0,.527-.539A.533.533,0,0,0,14.1,10.446ZM13.043,7.538V5.959H14.8v.5a1.643,1.643,0,0,0-1.5,1.077Zm-2.812,0V5.959h1.758v1.58Zm-2.813,0V5.959H9.175v1.58Zm-6.328,0V4.883H6.363V7.538ZM11.636,4.883V3.159h1.407a.355.355,0,0,1,.351.359V4.883Zm-2.812,0V3.518a.356.356,0,0,1,.352-.359h1.406V4.883ZM3.2,3.805V3.231a.534.534,0,0,1,.527-.539h.492V.538a.528.528,0,1,1,1.055,0V2.692h.563a.533.533,0,0,1,.527.539v.574Z" transform="translate(294 14)" fill="url(#l)"/></g></svg>