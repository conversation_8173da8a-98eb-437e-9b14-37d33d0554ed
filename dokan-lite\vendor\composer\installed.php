<?php return array(
    'root' => array(
        'name' => 'wedevs/dokan',
        'pretty_version' => 'v4.0.3',
        'version' => '4.0.3.0',
        'reference' => '862fd98f72dcdcdfd030695d68ef58e56172d5c2',
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'appsero/client' => array(
            'pretty_version' => 'v2.0.4',
            'version' => '2.0.4.0',
            'reference' => '12ff65b9770286d21edf314e7acfcd26fdde3315',
            'type' => 'library',
            'install_path' => __DIR__ . '/../appsero/client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'appsero/updater' => array(
            'pretty_version' => 'v2.3.3',
            'version' => '2.3.3.0',
            'reference' => '0c899f2e6b5894b695751d738151a98461cdb198',
            'type' => 'library',
            'install_path' => __DIR__ . '/../appsero/updater',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'jakeasmith/http_build_url' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => '93c273e77cb1edead0cf8bcf8cd2003428e74e37',
            'type' => 'library',
            'install_path' => __DIR__ . '/../jakeasmith/http_build_url',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'wedevs/dokan' => array(
            'pretty_version' => 'v4.0.3',
            'version' => '4.0.3.0',
            'reference' => '862fd98f72dcdcdfd030695d68ef58e56172d5c2',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
