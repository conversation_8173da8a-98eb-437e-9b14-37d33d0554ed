<?xml version="1.0" encoding="UTF-8"?>
<svg width="90px" height="93px" viewBox="0 0 90 93" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 53 (72520) - https://sketchapp.com -->
    <title>Group 54</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-1">
            <stop stop-color="#FE5196" offset="0%"></stop>
            <stop stop-color="#F77062" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="-36.3522522%" y1="-38.1200007%" x2="211.980564%" y2="212.525742%" id="linearGradient-2">
            <stop stop-color="#FF954A" offset="0%"></stop>
            <stop stop-color="#FF4CC7" offset="100%"></stop>
        </linearGradient>
        <path d="M30.8108108,0 L59.1891892,0 C76.2055301,-3.12585112e-15 90,13.7944699 90,30.8108108 L90,62.1891892 C90,79.2055301 76.2055301,93 59.1891892,93 L30.8108108,93 C13.7944699,93 2.08390075e-15,79.2055301 0,62.1891892 L0,30.8108108 C-2.08390075e-15,13.7944699 13.7944699,1.02312785e-14 30.8108108,7.10542736e-15 Z" id="path-3"></path>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Home-Page-Design-for-Dokan" transform="translate(-448.000000, -7898.000000)">
            <g id="Footer" transform="translate(-172.000000, 7729.000000)">
                <g id="Dokan-CTA" transform="translate(560.000000, 127.000000)">
                    <g id="Group-17" transform="translate(60.000000, 42.000000)">
                        <g id="Group-54">
                            <g id="Rectangle-2-Copy-10">
                                <use fill="url(#linearGradient-1)" xlink:href="#path-3"></use>
                                <use fill-opacity="0.38" fill="url(#linearGradient-2)" xlink:href="#path-3"></use>
                            </g>
                            <path d="M30.8108108,26.8842894 C35.9540866,23.7565856 62.4324324,19.8690059 62.4324324,46.8853104 C62.4324324,73.9016149 41.6688375,68.7385291 41.6688375,68.7385291 C46.6610337,67.5240845 52.3560786,63.6704278 52.3560786,44.4564212 C52.3560786,25.2424145 30.8108108,26.8842894 30.8108108,26.8842894 Z M49.5410129,59.3295229 C49.5410129,59.3295229 47.3670005,66.4616695 40.7830453,67.3629135 C34.19909,68.2641575 32.974488,64.899081 28.3994303,65.1324967 C28.2940069,64.2081185 28.5874731,63.282621 29.2137731,62.5643224 C29.8400732,61.8460238 30.7465789,61.395283 31.7292468,61.3135564 C35.4374515,60.8402412 42.3860354,61.7609365 47.0023719,59.0118182 C47.0023719,59.0118182 49.7542863,57.6631941 50.2702703,57.0083333 L49.5410129,59.3295229 Z M30.8108108,28.7195694 C36.9103582,28.6324233 42.8774014,30.5095617 47.8378378,34.0760057 C46.3437707,33.2836717 44.5844884,33.167949 43.0003143,33.7578015 C41.0157765,34.5345156 39.6231969,36.351976 39.3837209,38.4778296 C39.1593338,41.9979628 39.3837209,45.5976471 39.3837209,49.1244096 L39.3837209,59.7312142 C37.9582024,59.8637993 36.5458831,59.9168333 35.2259585,59.9168333 C33.9716975,59.9129699 32.7181439,59.9771461 31.4707731,60.1090816 C31.2485461,60.1411709 31.0282303,60.1854319 30.8108108,60.2416667 L30.8108108,37.755241 L30.8108108,28.7195694 Z" id="Combined-Shape" fill="#FFFFFF" fill-rule="nonzero"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>