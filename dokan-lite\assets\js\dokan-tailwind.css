*, ::before, ::after {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x:  ;
    --tw-pan-y:  ;
    --tw-pinch-zoom:  ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position:  ;
    --tw-gradient-via-position:  ;
    --tw-gradient-to-position:  ;
    --tw-ordinal:  ;
    --tw-slashed-zero:  ;
    --tw-numeric-figure:  ;
    --tw-numeric-spacing:  ;
    --tw-numeric-fraction:  ;
    --tw-ring-inset:  ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur:  ;
    --tw-brightness:  ;
    --tw-contrast:  ;
    --tw-grayscale:  ;
    --tw-hue-rotate:  ;
    --tw-invert:  ;
    --tw-saturate:  ;
    --tw-sepia:  ;
    --tw-drop-shadow:  ;
    --tw-backdrop-blur:  ;
    --tw-backdrop-brightness:  ;
    --tw-backdrop-contrast:  ;
    --tw-backdrop-grayscale:  ;
    --tw-backdrop-hue-rotate:  ;
    --tw-backdrop-invert:  ;
    --tw-backdrop-opacity:  ;
    --tw-backdrop-saturate:  ;
    --tw-backdrop-sepia:  ;
    --tw-contain-size:  ;
    --tw-contain-layout:  ;
    --tw-contain-paint:  ;
    --tw-contain-style:  ;
}
::backdrop {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x:  ;
    --tw-pan-y:  ;
    --tw-pinch-zoom:  ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position:  ;
    --tw-gradient-via-position:  ;
    --tw-gradient-to-position:  ;
    --tw-ordinal:  ;
    --tw-slashed-zero:  ;
    --tw-numeric-figure:  ;
    --tw-numeric-spacing:  ;
    --tw-numeric-fraction:  ;
    --tw-ring-inset:  ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur:  ;
    --tw-brightness:  ;
    --tw-contrast:  ;
    --tw-grayscale:  ;
    --tw-hue-rotate:  ;
    --tw-invert:  ;
    --tw-saturate:  ;
    --tw-sepia:  ;
    --tw-drop-shadow:  ;
    --tw-backdrop-blur:  ;
    --tw-backdrop-brightness:  ;
    --tw-backdrop-contrast:  ;
    --tw-backdrop-grayscale:  ;
    --tw-backdrop-hue-rotate:  ;
    --tw-backdrop-invert:  ;
    --tw-backdrop-opacity:  ;
    --tw-backdrop-saturate:  ;
    --tw-backdrop-sepia:  ;
    --tw-contain-size:  ;
    --tw-contain-layout:  ;
    --tw-contain-paint:  ;
    --tw-contain-style:  ;
}
.container {
    width: 100%;
}
@media (min-width: 360px) {
    .container {
        max-width: 360px;
    }
}
@media (min-width: 640px) {
    .container {
        max-width: 640px;
    }
}
@media (min-width: 768px) {
    .container {
        max-width: 768px;
    }
}
@media (min-width: 1024px) {
    .container {
        max-width: 1024px;
    }
}
@media (min-width: 1280px) {
    .container {
        max-width: 1280px;
    }
}
@media (min-width: 1536px) {
    .container {
        max-width: 1536px;
    }
}
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}
.visible {
    visibility: visible;
}
.collapse {
    visibility: collapse;
}
.static {
    position: static;
}
.fixed {
    position: fixed;
}
.absolute {
    position: absolute;
}
.relative {
    position: relative;
}
.sticky {
    position: sticky;
}
.inset-0 {
    inset: 0px;
}
.-start-6 {
    inset-inline-start: -1.5rem;
}
.-top-2 {
    top: -0.5rem;
}
.left-0 {
    left: 0px;
}
.right-0 {
    right: 0px;
}
.right-0\.5 {
    right: 0.125rem;
}
.right-1 {
    right: 0.25rem;
}
.right-3 {
    right: 0.75rem;
}
.right-6 {
    right: 1.5rem;
}
.top-0 {
    top: 0px;
}
.top-0\.5 {
    top: 0.125rem;
}
.top-1 {
    top: 0.25rem;
}
.top-6 {
    top: 1.5rem;
}
.top-\[calc\(100\%\+4px\)\] {
    top: calc(100% + 4px);
}
.top-full {
    top: 100%;
}
.z-0 {
    z-index: 0;
}
.z-10 {
    z-index: 10;
}
.z-40 {
    z-index: 40;
}
.z-50 {
    z-index: 50;
}
.z-\[1000000\] {
    z-index: 1000000;
}
.z-\[1\] {
    z-index: 1;
}
.z-\[999999\] {
    z-index: 999999;
}
.col-span-12 {
    grid-column: span 12 / span 12;
}
.col-span-4 {
    grid-column: span 4 / span 4;
}
.\!m-0 {
    margin: 0px !important;
}
.-m-4 {
    margin: -1rem;
}
.m-0 {
    margin: 0px;
}
.m-\[1em\] {
    margin: 1em;
}
.mx-1 {
    margin-left: 0.25rem;
    margin-right: 0.25rem;
}
.mx-auto {
    margin-left: auto;
    margin-right: auto;
}
.my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
.my-6 {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
}
.\!mb-\[6px\] {
    margin-bottom: 6px !important;
}
.\!mt-\[13px\] {
    margin-top: 13px !important;
}
.\!mt-\[6px\] {
    margin-top: 6px !important;
}
.-mb-px {
    margin-bottom: -1px;
}
.-mt-10 {
    margin-top: -2.5rem;
}
.mb-0 {
    margin-bottom: 0px;
}
.mb-1 {
    margin-bottom: 0.25rem;
}
.mb-10 {
    margin-bottom: 2.5rem;
}
.mb-12 {
    margin-bottom: 3rem;
}
.mb-14 {
    margin-bottom: 3.5rem;
}
.mb-2 {
    margin-bottom: 0.5rem;
}
.mb-3 {
    margin-bottom: 0.75rem;
}
.mb-4 {
    margin-bottom: 1rem;
}
.mb-5 {
    margin-bottom: 1.25rem;
}
.mb-6 {
    margin-bottom: 1.5rem;
}
.mb-7 {
    margin-bottom: 1.75rem;
}
.mb-8 {
    margin-bottom: 2rem;
}
.mb-\[-0\.5rem\] {
    margin-bottom: -0.5rem;
}
.ml-0 {
    margin-left: 0px;
}
.ml-1 {
    margin-left: 0.25rem;
}
.ml-2 {
    margin-left: 0.5rem;
}
.ml-5 {
    margin-left: 1.25rem;
}
.ml-auto {
    margin-left: auto;
}
.mr-0 {
    margin-right: 0px;
}
.mr-2 {
    margin-right: 0.5rem;
}
.mr-20 {
    margin-right: 5rem;
}
.mr-3 {
    margin-right: 0.75rem;
}
.mr-4 {
    margin-right: 1rem;
}
.mr-5 {
    margin-right: 1.25rem;
}
.mr-\[20px\] {
    margin-right: 20px;
}
.mr-auto {
    margin-right: auto;
}
.ms-10 {
    margin-inline-start: 2.5rem;
}
.mt-0 {
    margin-top: 0px;
}
.mt-0\.5 {
    margin-top: 0.125rem;
}
.mt-1 {
    margin-top: 0.25rem;
}
.mt-1\.5 {
    margin-top: 0.375rem;
}
.mt-10 {
    margin-top: 2.5rem;
}
.mt-12 {
    margin-top: 3rem;
}
.mt-16 {
    margin-top: 4rem;
}
.mt-2 {
    margin-top: 0.5rem;
}
.mt-3 {
    margin-top: 0.75rem;
}
.mt-4 {
    margin-top: 1rem;
}
.mt-5 {
    margin-top: 1.25rem;
}
.mt-6 {
    margin-top: 1.5rem;
}
.mt-8 {
    margin-top: 2rem;
}
.mt-\[-20px\] {
    margin-top: -20px;
}
.mt-\[20px\] {
    margin-top: 20px;
}
.box-border {
    box-sizing: border-box;
}
.box-content {
    box-sizing: content-box;
}
.line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
}
.block {
    display: block;
}
.inline-block {
    display: inline-block;
}
.inline {
    display: inline;
}
.\!flex {
    display: flex !important;
}
.flex {
    display: flex;
}
.inline-flex {
    display: inline-flex;
}
.table {
    display: table;
}
.table-cell {
    display: table-cell;
}
.table-column {
    display: table-column;
}
.table-row {
    display: table-row;
}
.grid {
    display: grid;
}
.\!hidden {
    display: none !important;
}
.hidden {
    display: none;
}
.aspect-video {
    aspect-ratio: 16 / 9;
}
.size-5 {
    width: 1.25rem;
    height: 1.25rem;
}
.h-1\.5 {
    height: 0.375rem;
}
.h-1\/2 {
    height: 50%;
}
.h-10 {
    height: 2.5rem;
}
.h-12 {
    height: 3rem;
}
.h-14 {
    height: 3.5rem;
}
.h-16 {
    height: 4rem;
}
.h-3 {
    height: 0.75rem;
}
.h-3\.5 {
    height: 0.875rem;
}
.h-36 {
    height: 9rem;
}
.h-4 {
    height: 1rem;
}
.h-48 {
    height: 12rem;
}
.h-5 {
    height: 1.25rem;
}
.h-6 {
    height: 1.5rem;
}
.h-7 {
    height: 1.75rem;
}
.h-8 {
    height: 2rem;
}
.h-\[11px\] {
    height: 11px;
}
.h-\[21rem\] {
    height: 21rem;
}
.h-\[25px\] {
    height: 25px;
}
.h-\[32px\] {
    height: 32px;
}
.h-\[3rem\] {
    height: 3rem;
}
.h-\[48px\] {
    height: 48px;
}
.h-\[85px\] {
    height: 85px;
}
.h-full {
    height: 100%;
}
.max-h-\[280px\] {
    max-height: 280px;
}
.max-h-\[500px\] {
    max-height: 500px;
}
.\!min-h-\[max-content\] {
    min-height: -moz-max-content !important;
    min-height: max-content !important;
}
.\!min-h-full {
    min-height: 100% !important;
}
.min-h-32 {
    min-height: 8rem;
}
.min-h-48 {
    min-height: 12rem;
}
.min-h-\[194px\] {
    min-height: 194px;
}
.min-h-\[3rem\] {
    min-height: 3rem;
}
.min-h-screen {
    min-height: 100vh;
}
.\!w-\[100\%\] {
    width: 100% !important;
}
.w-1\.5 {
    width: 0.375rem;
}
.w-1\/2 {
    width: 50%;
}
.w-1\/3 {
    width: 33.333333%;
}
.w-1\/4 {
    width: 25%;
}
.w-10 {
    width: 2.5rem;
}
.w-12 {
    width: 3rem;
}
.w-14 {
    width: 3.5rem;
}
.w-16 {
    width: 4rem;
}
.w-2\/3 {
    width: 66.666667%;
}
.w-20 {
    width: 5rem;
}
.w-24 {
    width: 6rem;
}
.w-28 {
    width: 7rem;
}
.w-3\.5 {
    width: 0.875rem;
}
.w-3\/4 {
    width: 75%;
}
.w-3\/6 {
    width: 50%;
}
.w-32 {
    width: 8rem;
}
.w-36 {
    width: 9rem;
}
.w-4 {
    width: 1rem;
}
.w-4\/6 {
    width: 66.666667%;
}
.w-44 {
    width: 11rem;
}
.w-48 {
    width: 12rem;
}
.w-5 {
    width: 1.25rem;
}
.w-5\/6 {
    width: 83.333333%;
}
.w-6 {
    width: 1.5rem;
}
.w-64 {
    width: 16rem;
}
.w-7 {
    width: 1.75rem;
}
.w-8 {
    width: 2rem;
}
.w-\[10\.5rem\] {
    width: 10.5rem;
}
.w-\[110px\] {
    width: 110px;
}
.w-\[11rem\] {
    width: 11rem;
}
.w-\[137px\] {
    width: 137px;
}
.w-\[157px\] {
    width: 157px;
}
.w-\[169px\] {
    width: 169px;
}
.w-\[1px\] {
    width: 1px;
}
.w-\[250px\] {
    width: 250px;
}
.w-\[5px\] {
    width: 5px;
}
.w-\[85\%\] {
    width: 85%;
}
.w-auto {
    width: auto;
}
.w-fit {
    width: -moz-fit-content;
    width: fit-content;
}
.w-full {
    width: 100%;
}
.min-w-0 {
    min-width: 0px;
}
.min-w-72 {
    min-width: 18rem;
}
.min-w-\[25px\] {
    min-width: 25px;
}
.min-w-\[75px\] {
    min-width: 75px;
}
.min-w-full {
    min-width: 100%;
}
.max-w-2xl {
    max-width: 42rem;
}
.max-w-3xl {
    max-width: 48rem;
}
.max-w-7xl {
    max-width: 80rem;
}
.max-w-\[20rem\] {
    max-width: 20rem;
}
.max-w-\[23rem\] {
    max-width: 23rem;
}
.max-w-\[24rem\] {
    max-width: 24rem;
}
.max-w-\[320px\] {
    max-width: 320px;
}
.max-w-\[450px\] {
    max-width: 450px;
}
.max-w-\[46rem\] {
    max-width: 46rem;
}
.max-w-\[530px\] {
    max-width: 530px;
}
.max-w-\[720px\] {
    max-width: 720px;
}
.max-w-full {
    max-width: 100%;
}
.max-w-md {
    max-width: 28rem;
}
.max-w-sm {
    max-width: 24rem;
}
.max-w-xl {
    max-width: 36rem;
}
.flex-1 {
    flex: 1 1 0%;
}
.flex-auto {
    flex: 1 1 auto;
}
.flex-shrink {
    flex-shrink: 1;
}
.flex-shrink-0 {
    flex-shrink: 0;
}
.shrink {
    flex-shrink: 1;
}
.flex-grow {
    flex-grow: 1;
}
.border-collapse {
    border-collapse: collapse;
}
.-translate-x-full {
    --tw-translate-x: -100%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-45 {
    --tw-rotate: 45deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-95 {
    --tw-scale-x: .95;
    --tw-scale-y: .95;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes pulse {
    50% {
        opacity: .5;
    }
}
.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
.animate-spin {
    animation: spin 1s linear infinite;
}
.cursor-pointer {
    cursor: pointer;
}
.resize {
    resize: both;
}
.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
}
.grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
}
.flex-row {
    flex-direction: row;
}
.\!flex-col {
    flex-direction: column !important;
}
.flex-col {
    flex-direction: column;
}
.flex-wrap {
    flex-wrap: wrap;
}
.items-start {
    align-items: flex-start;
}
.items-end {
    align-items: flex-end;
}
.\!items-center {
    align-items: center !important;
}
.items-center {
    align-items: center;
}
.justify-start {
    justify-content: flex-start;
}
.justify-end {
    justify-content: flex-end;
}
.\!justify-center {
    justify-content: center !important;
}
.justify-center {
    justify-content: center;
}
.justify-between {
    justify-content: space-between;
}
.gap-1 {
    gap: 0.25rem;
}
.gap-1\.5 {
    gap: 0.375rem;
}
.gap-2 {
    gap: 0.5rem;
}
.gap-2\.5 {
    gap: 0.625rem;
}
.gap-3 {
    gap: 0.75rem;
}
.gap-4 {
    gap: 1rem;
}
.gap-5 {
    gap: 1.25rem;
}
.gap-6 {
    gap: 1.5rem;
}
.gap-7 {
    gap: 1.75rem;
}
.gap-x-1 {
    -moz-column-gap: 0.25rem;
         column-gap: 0.25rem;
}
.gap-x-2 {
    -moz-column-gap: 0.5rem;
         column-gap: 0.5rem;
}
.gap-y-4 {
    row-gap: 1rem;
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.25rem * var(--tw-space-x-reverse));
    margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-2\.5 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.625rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.625rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.divide-y > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-y-reverse: 0;
    border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-gray-100 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(243 244 246 / var(--tw-divide-opacity, 1));
}
.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}
.divide-gray-300 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-divide-opacity, 1));
}
.self-center {
    align-self: center;
}
.overflow-auto {
    overflow: auto;
}
.overflow-hidden {
    overflow: hidden;
}
.overflow-y-auto {
    overflow-y: auto;
}
.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.whitespace-nowrap {
    white-space: nowrap;
}
.rounded {
    border-radius: 0.25rem;
}
.rounded-2xl {
    border-radius: 1rem;
}
.rounded-\[20px\] {
    border-radius: 20px;
}
.rounded-\[5px\] {
    border-radius: 5px;
}
.rounded-full {
    border-radius: 9999px;
}
.rounded-lg {
    border-radius: 0.5rem;
}
.rounded-md {
    border-radius: 0.375rem;
}
.rounded-none {
    border-radius: 0px;
}
.rounded-sm {
    border-radius: 0.125rem;
}
.rounded-l-md {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}
.rounded-l-none {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
}
.rounded-r-md {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}
.rounded-r-none {
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
.\!border-0 {
    border-width: 0px !important;
}
.border {
    border-width: 1px;
}
.border-0 {
    border-width: 0px;
}
.border-2 {
    border-width: 2px;
}
.border-\[0\.957434px\] {
    border-width: 0.957434px;
}
.border-\[1px\] {
    border-width: 1px;
}
.\!border-b-2 {
    border-bottom-width: 2px !important;
}
.\!border-b-\[1px\] {
    border-bottom-width: 1px !important;
}
.\!border-r-\[1px\] {
    border-right-width: 1px !important;
}
.border-b {
    border-bottom-width: 1px;
}
.border-b-0 {
    border-bottom-width: 0px;
}
.border-b-2 {
    border-bottom-width: 2px;
}
.border-b-\[1px\] {
    border-bottom-width: 1px;
}
.border-l {
    border-left-width: 1px;
}
.border-l-0 {
    border-left-width: 0px;
}
.border-l-4 {
    border-left-width: 4px;
}
.border-l-\[1px\] {
    border-left-width: 1px;
}
.border-r {
    border-right-width: 1px;
}
.border-r-\[0\.957434px\] {
    border-right-width: 0.957434px;
}
.border-r-\[1px\] {
    border-right-width: 1px;
}
.border-s-2 {
    border-inline-start-width: 2px;
}
.border-t {
    border-top-width: 1px;
}
.border-solid {
    border-style: solid;
}
.border-dashed {
    border-style: dashed;
}
.\!border-none {
    border-style: none !important;
}
.border-none {
    border-style: none;
}
.border-\[\#1a9ed4\] {
    --tw-border-opacity: 1;
    border-color: rgb(26 158 212 / var(--tw-border-opacity, 1));
}
.border-\[\#5341C20F\] {
    border-color: #5341C20F;
}
.border-\[\#7047EB\] {
    --tw-border-opacity: 1;
    border-color: rgb(112 71 235 / var(--tw-border-opacity, 1));
}
.border-\[\#E5E0F2\] {
    --tw-border-opacity: 1;
    border-color: rgb(229 224 242 / var(--tw-border-opacity, 1));
}
.border-\[\#E9E9E9\] {
    --tw-border-opacity: 1;
    border-color: rgb(233 233 233 / var(--tw-border-opacity, 1));
}
.border-\[\#e9e9ea\] {
    --tw-border-opacity: 1;
    border-color: rgb(233 233 234 / var(--tw-border-opacity, 1));
}
.border-gray-100 {
    --tw-border-opacity: 1;
    border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}
.border-gray-200 {
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-300 {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-indigo-600 {
    --tw-border-opacity: 1;
    border-color: rgb(79 70 229 / var(--tw-border-opacity, 1));
}
.border-orange-500 {
    --tw-border-opacity: 1;
    border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}
.border-red-300 {
    --tw-border-opacity: 1;
    border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}
.border-red-50 {
    --tw-border-opacity: 1;
    border-color: rgb(254 242 242 / var(--tw-border-opacity, 1));
}
.border-transparent {
    border-color: transparent;
}
.border-l-blue-500 {
    --tw-border-opacity: 1;
    border-left-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.\!bg-transparent {
    background-color: transparent !important;
}
.bg-\[\#0C5F9A\] {
    --tw-bg-opacity: 1;
    background-color: rgb(12 95 154 / var(--tw-bg-opacity, 1));
}
.bg-\[\#6F4CEB\] {
    --tw-bg-opacity: 1;
    background-color: rgb(111 76 235 / var(--tw-bg-opacity, 1));
}
.bg-\[\#7047EB\] {
    --tw-bg-opacity: 1;
    background-color: rgb(112 71 235 / var(--tw-bg-opacity, 1));
}
.bg-\[\#D8D8FE\] {
    --tw-bg-opacity: 1;
    background-color: rgb(216 216 254 / var(--tw-bg-opacity, 1));
}
.bg-\[\#E4E6EB\] {
    --tw-bg-opacity: 1;
    background-color: rgb(228 230 235 / var(--tw-bg-opacity, 1));
}
.bg-\[\#EFEAFF\] {
    --tw-bg-opacity: 1;
    background-color: rgb(239 234 255 / var(--tw-bg-opacity, 1));
}
.bg-\[\#F1EDFD\] {
    --tw-bg-opacity: 1;
    background-color: rgb(241 237 253 / var(--tw-bg-opacity, 1));
}
.bg-\[\#F8F9F8\] {
    --tw-bg-opacity: 1;
    background-color: rgb(248 249 248 / var(--tw-bg-opacity, 1));
}
.bg-\[\#FF9B5366\] {
    background-color: #FF9B5366;
}
.bg-\[\#e4e6eb\] {
    --tw-bg-opacity: 1;
    background-color: rgb(228 230 235 / var(--tw-bg-opacity, 1));
}
.bg-amber-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}
.bg-black {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-blue-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200 {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-800 {
    --tw-bg-opacity: 1;
    background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.bg-green-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}
.bg-green-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-indigo-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));
}
.bg-indigo-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));
}
.bg-indigo-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}
.bg-orange-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}
.bg-pink-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(253 242 248 / var(--tw-bg-opacity, 1));
}
.bg-purple-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}
.bg-red-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-red-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-teal-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(240 253 250 / var(--tw-bg-opacity, 1));
}
.bg-transparent {
    background-color: transparent;
}
.bg-violet-900 {
    --tw-bg-opacity: 1;
    background-color: rgb(76 29 149 / var(--tw-bg-opacity, 1));
}
.bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-yellow-400 {
    --tw-bg-opacity: 1;
    background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));
}
.bg-yellow-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}
.bg-opacity-50 {
    --tw-bg-opacity: 0.5;
}
.bg-\[url\(\'\/assets\/images\/error-page-bg\.png\'\)\] {
    background-image: url(../images/error-page-bg.png);
}
.bg-none {
    background-image: none;
}
.bg-auto {
    background-size: auto;
}
.bg-cover {
    background-size: cover;
}
.bg-\[-20px_10px\] {
    background-position: -20px 10px;
}
.bg-\[right_-40px_bottom_-40px\] {
    background-position: right -40px bottom -40px;
}
.bg-center {
    background-position: center;
}
.bg-no-repeat {
    background-repeat: no-repeat;
}
.\!fill-\[\#7047EB\] {
    fill: #7047EB !important;
}
.fill-neutral-400 {
    fill: #a3a3a3;
}
.fill-red-500 {
    fill: #ef4444;
}
.fill-white {
    fill: #fff;
}
.stroke-red-500 {
    stroke: #ef4444;
}
.stroke-\[2\.5\] {
    stroke-width: 2.5;
}
.object-cover {
    -o-object-fit: cover;
       object-fit: cover;
}
.\!p-0 {
    padding: 0px !important;
}
.p-0 {
    padding: 0px;
}
.p-1 {
    padding: 0.25rem;
}
.p-2 {
    padding: 0.5rem;
}
.p-2\.5 {
    padding: 0.625rem;
}
.p-3 {
    padding: 0.75rem;
}
.p-4 {
    padding: 1rem;
}
.p-5 {
    padding: 1.25rem;
}
.p-6 {
    padding: 1.5rem;
}
.p-8 {
    padding: 2rem;
}
.px-1 {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
}
.px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}
.px-2\.5 {
    padding-left: 0.625rem;
    padding-right: 0.625rem;
}
.px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}
.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}
.px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
}
.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}
.px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
}
.px-\[18px\] {
    padding-left: 18px;
    padding-right: 18px;
}
.px-\[25px\] {
    padding-left: 25px;
    padding-right: 25px;
}
.py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}
.py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
}
.py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
}
.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}
.py-2\.5 {
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
}
.py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
}
.py-3\.5 {
    padding-top: 0.875rem;
    padding-bottom: 0.875rem;
}
.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
}
.py-5 {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem;
}
.py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
}
.py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
}
.\!pb-0 {
    padding-bottom: 0px !important;
}
.\!pl-2 {
    padding-left: 0.5rem !important;
}
.\!pl-\[5px\] {
    padding-left: 5px !important;
}
.\!pr-0 {
    padding-right: 0px !important;
}
.\!pt-0 {
    padding-top: 0px !important;
}
.pb-0 {
    padding-bottom: 0px;
}
.pb-10 {
    padding-bottom: 2.5rem;
}
.pb-4 {
    padding-bottom: 1rem;
}
.pb-5 {
    padding-bottom: 1.25rem;
}
.pl-1 {
    padding-left: 0.25rem;
}
.pl-2 {
    padding-left: 0.5rem;
}
.pl-3 {
    padding-left: 0.75rem;
}
.pl-4 {
    padding-left: 1rem;
}
.pl-5 {
    padding-left: 1.25rem;
}
.pl-\[5px\] {
    padding-left: 5px;
}
.pr-0 {
    padding-right: 0px;
}
.pr-1\.5 {
    padding-right: 0.375rem;
}
.pr-2 {
    padding-right: 0.5rem;
}
.pr-5 {
    padding-right: 1.25rem;
}
.pt-0 {
    padding-top: 0px;
}
.pt-4 {
    padding-top: 1rem;
}
.pt-8 {
    padding-top: 2rem;
}
.text-left {
    text-align: left;
}
.text-center {
    text-align: center;
}
.align-middle {
    vertical-align: middle;
}
.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
}
.text-2xl\/7 {
    font-size: 1.5rem;
    line-height: 1.75rem;
}
.text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
}
.text-\[1\.3em\] {
    font-size: 1.3em;
}
.text-\[10px\] {
    font-size: 10px;
}
.text-\[12px\] {
    font-size: 12px;
}
.text-\[14px\] {
    font-size: 14px;
}
.text-base {
    font-size: 1rem;
    line-height: 1.5rem;
}
.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
}
.text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}
.text-sm\/6 {
    font-size: 0.875rem;
    line-height: 1.5rem;
}
.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
}
.text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
}
.font-bold {
    font-weight: 700;
}
.font-light {
    font-weight: 300;
}
.font-medium {
    font-weight: 500;
}
.font-normal {
    font-weight: 400;
}
.font-semibold {
    font-weight: 600;
}
.uppercase {
    text-transform: uppercase;
}
.lowercase {
    text-transform: lowercase;
}
.capitalize {
    text-transform: capitalize;
}
.italic {
    font-style: italic;
}
.leading-3 {
    line-height: .75rem;
}
.leading-4 {
    line-height: 1rem;
}
.leading-5 {
    line-height: 1.25rem;
}
.leading-6 {
    line-height: 1.5rem;
}
.leading-\[1\.5em\] {
    line-height: 1.5em;
}
.leading-\[48px\] {
    line-height: 48px;
}
.tracking-wide {
    letter-spacing: 0.025em;
}
.\!text-gray-800 {
    --tw-text-opacity: 1 !important;
    color: rgb(31 41 55 / var(--tw-text-opacity, 1)) !important;
}
.\!text-gray-900 {
    --tw-text-opacity: 1 !important;
    color: rgb(17 24 39 / var(--tw-text-opacity, 1)) !important;
}
.\!text-white {
    --tw-text-opacity: 1 !important;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}
.text-\[\#1a9ed4\] {
    --tw-text-opacity: 1;
    color: rgb(26 158 212 / var(--tw-text-opacity, 1));
}
.text-\[\#25252D\] {
    --tw-text-opacity: 1;
    color: rgb(37 37 45 / var(--tw-text-opacity, 1));
}
.text-\[\#393939\] {
    --tw-text-opacity: 1;
    color: rgb(57 57 57 / var(--tw-text-opacity, 1));
}
.text-\[\#4C19E6\] {
    --tw-text-opacity: 1;
    color: rgb(76 25 230 / var(--tw-text-opacity, 1));
}
.text-\[\#575757\] {
    --tw-text-opacity: 1;
    color: rgb(87 87 87 / var(--tw-text-opacity, 1));
}
.text-\[\#637381\] {
    --tw-text-opacity: 1;
    color: rgb(99 115 129 / var(--tw-text-opacity, 1));
}
.text-\[\#7047EB\] {
    --tw-text-opacity: 1;
    color: rgb(112 71 235 / var(--tw-text-opacity, 1));
}
.text-\[\#788383\] {
    --tw-text-opacity: 1;
    color: rgb(120 131 131 / var(--tw-text-opacity, 1));
}
.text-\[\#7B4E2E\] {
    --tw-text-opacity: 1;
    color: rgb(123 78 46 / var(--tw-text-opacity, 1));
}
.text-\[\#828282\] {
    --tw-text-opacity: 1;
    color: rgb(130 130 130 / var(--tw-text-opacity, 1));
}
.text-\[\#bcbed5\] {
    --tw-text-opacity: 1;
    color: rgb(188 190 213 / var(--tw-text-opacity, 1));
}
.text-amber-800 {
    --tw-text-opacity: 1;
    color: rgb(146 64 14 / var(--tw-text-opacity, 1));
}
.text-black {
    --tw-text-opacity: 1;
    color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
.text-blue-600 {
    --tw-text-opacity: 1;
    color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-blue-800 {
    --tw-text-opacity: 1;
    color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-gray-200 {
    --tw-text-opacity: 1;
    color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.text-gray-300 {
    --tw-text-opacity: 1;
    color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400 {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-500 {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600 {
    --tw-text-opacity: 1;
    color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700 {
    --tw-text-opacity: 1;
    color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800 {
    --tw-text-opacity: 1;
    color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-gray-900 {
    --tw-text-opacity: 1;
    color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-green-800 {
    --tw-text-opacity: 1;
    color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-indigo-600 {
    --tw-text-opacity: 1;
    color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}
.text-indigo-800 {
    --tw-text-opacity: 1;
    color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}
.text-orange-600 {
    --tw-text-opacity: 1;
    color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}
.text-orange-800 {
    --tw-text-opacity: 1;
    color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}
.text-pink-800 {
    --tw-text-opacity: 1;
    color: rgb(157 23 77 / var(--tw-text-opacity, 1));
}
.text-purple-600 {
    --tw-text-opacity: 1;
    color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}
.text-purple-800 {
    --tw-text-opacity: 1;
    color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}
.text-red-300 {
    --tw-text-opacity: 1;
    color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.text-red-500 {
    --tw-text-opacity: 1;
    color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600 {
    --tw-text-opacity: 1;
    color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-800 {
    --tw-text-opacity: 1;
    color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-teal-800 {
    --tw-text-opacity: 1;
    color: rgb(17 94 89 / var(--tw-text-opacity, 1));
}
.text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-yellow-400 {
    --tw-text-opacity: 1;
    color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}
.text-yellow-800 {
    --tw-text-opacity: 1;
    color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}
.underline {
    text-decoration-line: underline;
}
.no-underline {
    text-decoration-line: none;
}
.antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.opacity-0 {
    opacity: 0;
}
.opacity-100 {
    opacity: 1;
}
.opacity-25 {
    opacity: 0.25;
}
.opacity-75 {
    opacity: 0.75;
}
.\!shadow-none {
    --tw-shadow: 0 0 #0000 !important;
    --tw-shadow-colored: 0 0 #0000 !important;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.shadow {
    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0px_2px_14px_1px_rgba\(50\2c 22\2c 56\2c 0\.06\)\] {
    --tw-shadow: 0px 2px 14px 1px rgba(50,22,56,0.06);
    --tw-shadow-colored: 0px 2px 14px 1px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0px_4px_20px_0px_rgba\(0\2c 0\2c 0\2c 0\.05\)\] {
    --tw-shadow: 0px 4px 20px 0px rgba(0,0,0,0.05);
    --tw-shadow-colored: 0px 4px 20px 0px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-none {
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
    --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
    --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none {
    outline: 2px solid transparent;
    outline-offset: 2px;
}
.outline {
    outline-style: solid;
}
.ring-1 {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-2 {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-inset {
    --tw-ring-inset: inset;
}
.ring-black\/5 {
    --tw-ring-color: rgb(0 0 0 / 0.05);
}
.ring-gray-900\/5 {
    --tw-ring-color: rgb(17 24 39 / 0.05);
}
.ring-white {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));
}
.blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow {
    --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.grayscale {
    --tw-grayscale: grayscale(100%);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.transition {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}
.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}
.transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}
.transition-opacity {
    transition-property: opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}
.transition-transform {
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}
.duration-150 {
    transition-duration: 150ms;
}
.duration-200 {
    transition-duration: 200ms;
}
.duration-300 {
    transition-duration: 300ms;
}
.duration-500 {
    transition-duration: 500ms;
}
.ease-in {
    transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out {
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.\@container\/after-header {
    container-type: inline-size;
    container-name: after-header;
}
.\@container\/before-header {
    container-type: inline-size;
    container-name: before-header;
}
.\@container\/combine {
    container-type: inline-size;
    container-name: combine;
}
.\@container\/currency {
    container-type: inline-size;
    container-name: currency;
}
.\@container\/header {
    container-type: inline-size;
    container-name: header;
}
.\@container\/header-title-section {
    container-type: inline-size;
    container-name: header-title-section;
}
.\@container\/main {
    container-type: inline-size;
    container-name: main;
}
.\@container\/radio {
    container-type: inline-size;
    container-name: radio;
}
.\@container\/step-body {
    container-type: inline-size;
    container-name: step-body;
}
.\@container\/switcher {
    container-type: inline-size;
    container-name: switcher;
}

input[type=checkbox] {
    margin-top: 0.25rem;
}

input[type=checkbox]::before {
    --tw-content: '' !important;
    content: var(--tw-content) !important;
}
a {
    color: #0073aa;
}
a:hover, a:active, a:focus {
    color: #0096dd;
}
table.wp-list-table.widefat.fixed {
    position: static;
}

.tablenav.top {
    margin-bottom: 10px;
}

.before\:absolute::before {
    content: var(--tw-content);
    position: absolute;
}

.before\:bottom-full::before {
    content: var(--tw-content);
    bottom: 100%;
}

.before\:left-0::before {
    content: var(--tw-content);
    left: 0px;
}

.before\:box-content::before {
    content: var(--tw-content);
    box-sizing: content-box;
}

.before\:h-12::before {
    content: var(--tw-content);
    height: 3rem;
}

.before\:w-full::before {
    content: var(--tw-content);
    width: 100%;
}

.before\:content-\[\'\'\]::before {
    --tw-content: '';
    content: var(--tw-content);
}

.\*\:first\:\*\:border-transparent > *:first-child > * {
    border-color: transparent;
}

.\*\:first\:border-gray-200:first-child > * {
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.last\:mb-0:last-child {
    margin-bottom: 0px;
}

.last\:mr-0:last-child {
    margin-right: 0px;
}

.last\:border-b-0:last-child {
    border-bottom-width: 0px;
}

.hover\:border-gray-300:hover {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.hover\:bg-blue-600:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-500:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-600:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}

.hover\:bg-transparent:hover {
    background-color: transparent;
}

.hover\:bg-white:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-500:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}

.hover\:bg-opacity-85:hover {
    --tw-bg-opacity: 0.85;
}

.hover\:\!text-\[\#7047EB\]:hover {
    --tw-text-opacity: 1 !important;
    color: rgb(112 71 235 / var(--tw-text-opacity, 1)) !important;
}

.hover\:text-\[\#7047EB\]:hover {
    --tw-text-opacity: 1;
    color: rgb(112 71 235 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-600:hover {
    --tw-text-opacity: 1;
    color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-700:hover {
    --tw-text-opacity: 1;
    color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-900:hover {
    --tw-text-opacity: 1;
    color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
    text-decoration-line: underline;
}

.focus\:border-none:focus {
    border-style: none;
}

.focus\:border-gray-300:focus {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.focus\:border-gray-900:focus {
    --tw-border-opacity: 1;
    border-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}

.focus\:border-orange-500:focus {
    --tw-border-opacity: 1;
    border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}

.focus\:border-transparent:focus {
    border-color: transparent;
}

.focus\:\!shadow-none:focus {
    --tw-shadow: 0 0 #0000 !important;
    --tw-shadow-colored: 0 0 #0000 !important;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.focus\:\!outline-none:focus {
    outline: 2px solid transparent !important;
    outline-offset: 2px !important;
}

.focus\:outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

.focus\:\!ring-0:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
}

.focus\:ring-0:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-gray-900:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(17 24 39 / var(--tw-ring-opacity, 1));
}

.focus\:ring-indigo-500:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));
}

.focus\:ring-orange-500:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity, 1));
}

.focus-visible\:outline:focus-visible {
    outline-style: solid;
}

.focus-visible\:outline-2:focus-visible {
    outline-width: 2px;
}

.focus-visible\:outline-offset-2:focus-visible {
    outline-offset: 2px;
}

.focus-visible\:outline-indigo-600:focus-visible {
    outline-color: #4f46e5;
}

.disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
}

.group:hover .group-hover\:scale-110 {
    --tw-scale-x: 1.1;
    --tw-scale-y: 1.1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:\!bg-\[\#EFEAFF\] {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(239 234 255 / var(--tw-bg-opacity, 1)) !important;
}

.group:hover .group-hover\:fill-\[\#7047EB\] {
    fill: #7047EB;
}

.group:hover .group-hover\:stroke-\[\#1b4fd1\] {
    stroke: #1b4fd1;
}

.data-\[closed\]\:translate-y-1[data-closed] {
    --tw-translate-y: 0.25rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[closed\]\:opacity-0[data-closed] {
    opacity: 0;
}

.data-\[enter\]\:duration-200[data-enter] {
    transition-duration: 200ms;
}

.data-\[leave\]\:duration-150[data-leave] {
    transition-duration: 150ms;
}

.data-\[enter\]\:ease-out[data-enter] {
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.data-\[leave\]\:ease-in[data-leave] {
    transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

@container main (min-width: 20rem) {
    .\@xs\/main\:py-4 {
        padding-top: 1rem;
        padding-bottom: 1rem;
    }
}

@container currency (min-width: 24rem) {
    .\@sm\/currency\:col-span-4 {
        grid-column: span 4 / span 4;
    }
    .\@sm\/currency\:col-span-8 {
        grid-column: span 8 / span 8;
    }
    .\@sm\/currency\:justify-end {
        justify-content: flex-end;
    }
    .\@sm\/currency\:text-base {
        font-size: 1rem;
        line-height: 1.5rem;
    }
    .\@sm\/currency\:text-sm {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }
}

@container (min-width: 24rem) {
    .\@sm\:text-sm {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }
}

@container combine (min-width: 28rem) {
    .\@md\/combine\:text-base {
        font-size: 1rem;
        line-height: 1.5rem;
    }
    .\@md\/combine\:text-sm {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }
}

@container radio (min-width: 28rem) {
    .\@md\/radio\:col-span-4 {
        grid-column: span 4 / span 4;
    }
    .\@md\/radio\:col-span-8 {
        grid-column: span 8 / span 8;
    }
    .\@md\/radio\:justify-end {
        justify-content: flex-end;
    }
}

@container step-body (min-width: 28rem) {
    .\@md\/step-body\:p-12 {
        padding: 3rem;
    }
}

@container switcher (min-width: 28rem) {
    .\@md\/switcher\:col-span-4 {
        grid-column: span 4 / span 4;
    }
    .\@md\/switcher\:col-span-8 {
        grid-column: span 8 / span 8;
    }
    .\@md\/switcher\:justify-end {
        justify-content: flex-end;
    }
}

@container (min-width: 28rem) {
    .\@md\:gap-6 {
        gap: 1.5rem;
    }
    .\@md\:text-base {
        font-size: 1rem;
        line-height: 1.5rem;
    }
}

@container combine (min-width: 36rem) {
    .\@xl\/combine\:col-span-5 {
        grid-column: span 5 / span 5;
    }
    .\@xl\/combine\:col-span-7 {
        grid-column: span 7 / span 7;
    }
}

@container main (min-width: 36rem) {
    .\@xl\/main\:col-span-4 {
        grid-column: span 4 / span 4;
    }
    .\@xl\/main\:col-span-8 {
        grid-column: span 8 / span 8;
    }
    .\@xl\/main\:my-0 {
        margin-top: 0px;
        margin-bottom: 0px;
    }
}

@container main (min-width: 42rem) {
    .\@2xl\/main\:mb-10 {
        margin-bottom: 2.5rem;
    }
}

@container main (min-width: 48rem) {
    .\@3xl\/main\:col-span-3 {
        grid-column: span 3 / span 3;
    }
    .\@3xl\/main\:col-span-9 {
        grid-column: span 9 / span 9;
    }
    .\@3xl\/main\:py-10 {
        padding-top: 2.5rem;
        padding-bottom: 2.5rem;
    }
}

@container main (min-width: 64rem) {
    .\@5xl\/main\:pl-12 {
        padding-left: 3rem;
    }
}

@container step-body (min-width: 64rem) {
    .\@5xl\/step-body\:px-28 {
        padding-left: 7rem;
        padding-right: 7rem;
    }
    .\@5xl\/step-body\:py-16 {
        padding-top: 4rem;
        padding-bottom: 4rem;
    }
}

@media (min-width: 360px) {
    .d-xs\:absolute {
        position: absolute;
    }
    .d-xs\:ml-1 {
        margin-left: 0.25rem;
    }
    .d-xs\:flex {
        display: flex;
    }
    .d-xs\:hidden {
        display: none;
    }
    .d-xs\:w-fit {
        width: -moz-fit-content;
        width: fit-content;
    }
    .d-xs\:w-full {
        width: 100%;
    }
    .d-xs\:flex-col {
        flex-direction: column;
    }
    .d-xs\:\!rounded-l-none {
        border-top-left-radius: 0px !important;
        border-bottom-left-radius: 0px !important;
    }
    .d-xs\:\!rounded-r-none {
        border-top-right-radius: 0px !important;
        border-bottom-right-radius: 0px !important;
    }
    .d-xs\:border-\[0\.957434px\] {
        border-width: 0.957434px;
    }
    .d-xs\:\!border-l-0 {
        border-left-width: 0px !important;
    }
    .d-xs\:\!border-r-0 {
        border-right-width: 0px !important;
    }
    .d-xs\:border-l-0 {
        border-left-width: 0px;
    }
    .d-xs\:border-r-0 {
        border-right-width: 0px;
    }
    .d-xs\:\!bg-transparent {
        background-color: transparent !important;
    }
    .d-xs\:bg-\[\#e5e7eb\] {
        --tw-bg-opacity: 1;
        background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
    }
    .d-xs\:bg-gray-100 {
        --tw-bg-opacity: 1;
        background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
    }
    .d-xs\:p-1 {
        padding: 0.25rem;
    }
    .d-xs\:\!pb-5 {
        padding-bottom: 1.25rem !important;
    }
    .d-xs\:\!pl-0 {
        padding-left: 0px !important;
    }
    .d-xs\:\!pr-\[5px\] {
        padding-right: 5px !important;
    }
    .d-xs\:\!pt-5 {
        padding-top: 1.25rem !important;
    }
    .d-xs\:pl-1 {
        padding-left: 0.25rem;
    }
    .d-xs\:pr-1 {
        padding-right: 0.25rem;
    }
    .d-xs\:text-right {
        text-align: right;
    }
    .d-xs\:text-\[6px\] {
        font-size: 6px;
    }
    .d-xs\:text-\[8px\] {
        font-size: 8px;
    }
    .d-xs\:shadow-md {
        --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
        box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
    }
}

@media (min-width: 640px) {
    .sm\:mb-0 {
        margin-bottom: 0px;
    }
    .sm\:ml-4 {
        margin-left: 1rem;
    }
    .sm\:mt-0 {
        margin-top: 0px;
    }
    .sm\:block {
        display: block;
    }
    .sm\:flex {
        display: flex;
    }
    .sm\:hidden {
        display: none;
    }
    .sm\:w-\[50rem\] {
        width: 50rem;
    }
    .sm\:w-\[54rem\] {
        width: 54rem;
    }
    .sm\:w-\[70\%\] {
        width: 70%;
    }
    .sm\:w-auto {
        width: auto;
    }
    .sm\:w-fit {
        width: -moz-fit-content;
        width: fit-content;
    }
    .sm\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    .sm\:flex-col {
        flex-direction: column;
    }
    .sm\:items-start {
        align-items: flex-start;
    }
    .sm\:\!items-center {
        align-items: center !important;
    }
    .sm\:truncate {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .sm\:rounded-lg {
        border-radius: 0.5rem;
    }
    .sm\:rounded-md {
        border-radius: 0.375rem;
    }
    .sm\:p-6 {
        padding: 1.5rem;
    }
    .sm\:px-6 {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
    .sm\:text-left {
        text-align: left;
    }
    .sm\:text-2xl {
        font-size: 1.5rem;
        line-height: 2rem;
    }
    .sm\:text-3xl {
        font-size: 1.875rem;
        line-height: 2.25rem;
    }
    .sm\:text-\[12px\] {
        font-size: 12px;
    }
    .sm\:text-\[14px\] {
        font-size: 14px;
    }
    .sm\:text-base {
        font-size: 1rem;
        line-height: 1.5rem;
    }
    .sm\:text-sm {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }
    .sm\:tracking-tight {
        letter-spacing: -0.025em;
    }
}

@media (min-width: 768px) {
    .md\:mb-4 {
        margin-bottom: 1rem;
    }
    .md\:mb-6 {
        margin-bottom: 1.5rem;
    }
    .md\:mb-8 {
        margin-bottom: 2rem;
    }
    .md\:ml-4 {
        margin-left: 1rem;
    }
    .md\:mt-0 {
        margin-top: 0px;
    }
    .md\:flex {
        display: flex;
    }
    .md\:h-\[33\.5rem\] {
        height: 33.5rem;
    }
    .md\:\!w-1\/2 {
        width: 50% !important;
    }
    .md\:w-1\/2 {
        width: 50%;
    }
    .md\:w-\[1px\] {
        width: 1px;
    }
    .md\:w-auto {
        width: auto;
    }
    .md\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    .md\:\!flex-row {
        flex-direction: row !important;
    }
    .md\:flex-row {
        flex-direction: row;
    }
    .md\:items-center {
        align-items: center;
    }
    .md\:\!justify-end {
        justify-content: flex-end !important;
    }
    .md\:justify-between {
        justify-content: space-between;
    }
    .md\:\!rounded-l-\[5px\] {
        border-top-left-radius: 5px !important;
        border-bottom-left-radius: 5px !important;
    }
    .md\:\!rounded-r-\[5px\] {
        border-top-right-radius: 5px !important;
        border-bottom-right-radius: 5px !important;
    }
    .md\:border-0 {
        border-width: 0px;
    }
    .md\:\!border-l-\[0\.957434px\] {
        border-left-width: 0.957434px !important;
    }
    .md\:\!border-r-\[0\.957434px\] {
        border-right-width: 0.957434px !important;
    }
    .md\:\!border-r-\[1px\] {
        border-right-width: 1px !important;
    }
    .md\:border-l-\[0\.957434px\] {
        border-left-width: 0.957434px;
    }
    .md\:border-l-\[1px\] {
        border-left-width: 1px;
    }
    .md\:border-r-\[0\.957434px\] {
        border-right-width: 0.957434px;
    }
    .md\:\!bg-gray-100 {
        --tw-bg-opacity: 1 !important;
        background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1)) !important;
    }
    .md\:bg-transparent {
        background-color: transparent;
    }
    .md\:bg-\[0px_20px\] {
        background-position: 0px 20px;
    }
    .md\:bg-\[right_0px_bottom_-10px\] {
        background-position: right 0px bottom -10px;
    }
    .md\:p-10 {
        padding: 2.5rem;
    }
    .md\:p-2 {
        padding: 0.5rem;
    }
    .md\:\!pl-\[5px\] {
        padding-left: 5px !important;
    }
    .md\:pl-2 {
        padding-left: 0.5rem;
    }
    .md\:pl-4 {
        padding-left: 1rem;
    }
    .md\:pl-6 {
        padding-left: 1.5rem;
    }
    .md\:pr-2 {
        padding-right: 0.5rem;
    }
    .md\:pt-4 {
        padding-top: 1rem;
    }
    .md\:text-left {
        text-align: left;
    }
    .md\:text-3xl {
        font-size: 1.875rem;
        line-height: 2.25rem;
    }
    .md\:text-4xl {
        font-size: 2.25rem;
        line-height: 2.5rem;
    }
    .md\:text-\[14px\] {
        font-size: 14px;
    }
    .md\:text-sm {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }
    .md\:leading-\[1\.3\] {
        line-height: 1.3;
    }
    .md\:shadow-md {
        --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
        box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
    }
    .md\:shadow-none {
        --tw-shadow: 0 0 #0000;
        --tw-shadow-colored: 0 0 #0000;
        box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
    }
}

@media (min-width: 1024px) {
    .lg\:col-span-12 {
        grid-column: span 12 / span 12;
    }
    .lg\:col-span-3 {
        grid-column: span 3 / span 3;
    }
    .lg\:col-span-9 {
        grid-column: span 9 / span 9;
    }
    .lg\:block {
        display: block;
    }
    .lg\:grid {
        display: grid;
    }
    .lg\:hidden {
        display: none;
    }
    .lg\:w-\[340px\] {
        width: 340px;
    }
    .lg\:grid-cols-12 {
        grid-template-columns: repeat(12, minmax(0, 1fr));
    }
    .lg\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
    .lg\:flex-row {
        flex-direction: row;
    }
    .lg\:gap-6 {
        gap: 1.5rem;
    }
    .lg\:gap-x-5 {
        -moz-column-gap: 1.25rem;
             column-gap: 1.25rem;
    }
    .lg\:overflow-hidden {
        overflow: hidden;
    }
    .lg\:px-0 {
        padding-left: 0px;
        padding-right: 0px;
    }
    .lg\:py-0 {
        padding-top: 0px;
        padding-bottom: 0px;
    }
    .lg\:py-5 {
        padding-top: 1.25rem;
        padding-bottom: 1.25rem;
    }
    .lg\:text-3xl {
        font-size: 1.875rem;
        line-height: 2.25rem;
    }
}

@media (min-width: 1280px) {
    .xl\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
}

@media (prefers-color-scheme: dark) {
    .dark\:border-gray-700 {
        --tw-border-opacity: 1;
        border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
    }
    .dark\:text-black {
        --tw-text-opacity: 1;
        color: rgb(0 0 0 / var(--tw-text-opacity, 1));
    }
    .dark\:text-blue-500 {
        --tw-text-opacity: 1;
        color: rgb(59 130 246 / var(--tw-text-opacity, 1));
    }
    .dark\:text-gray-400 {
        --tw-text-opacity: 1;
        color: rgb(156 163 175 / var(--tw-text-opacity, 1));
    }
    .dark\:text-gray-600 {
        --tw-text-opacity: 1;
        color: rgb(75 85 99 / var(--tw-text-opacity, 1));
    }
}

.\[\&\:\:-webkit-scrollbar-thumb\]\:rounded-full::-webkit-scrollbar-thumb {
    border-radius: 9999px;
}

.\[\&\:\:-webkit-scrollbar\]\:h-1::-webkit-scrollbar {
    height: 0.25rem;
}

.\[\&\:\:-webkit-scrollbar\]\:w-2::-webkit-scrollbar {
    width: 0.5rem;
}

.\*\:\[\&\:not\(\:last-child\)\]\:\*\:border-b-2 > *:not(:last-child) > * {
    border-bottom-width: 2px;
}

.\[\&\:not\(\:last-child\)\]\:\*\:border-b > *:not(:last-child) {
    border-bottom-width: 1px;
}

.focus\:\*\:\[\&\:not\(\:last-child\)\]\:\*\:outline-transparent > *:not(:last-child) > *:focus {
    outline-color: transparent;
}

.\[\&_\*_p\]\:m-0 * p {
    margin: 0px;
}

