# Copyright (c) 2025 Dokan Inc. All Rights Reserved.
msgid ""
msgstr ""
"Project-Id-Version: Dokan 4.0.3\n"
"Report-Msgid-Bugs-To: https://dokan.co/contact/\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-07-01T05:22:27+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: dokan-lite\n"

#. Plugin Name of the plugin
#: dokan.php
#: includes/Admin/AdminBar.php:46
#: includes/Admin/Menu.php:45
#: includes/Admin/Menu.php:46
#: includes/Customizer.php:56
#: includes/Privacy.php:30
msgid "Dokan"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
#: dokan.php
msgid "https://dokan.co/wordpress/"
msgstr ""

#. Description of the plugin
#: dokan.php
msgid "An e-commerce marketplace plugin for WordPress. Powered by WooCommerce and weDevs."
msgstr ""

#. Author of the plugin
#: dokan.php
msgid "Dokan Inc."
msgstr ""

#. translators: 1: Required PHP Version 2: Running php version
#: dokan-class.php:164
msgid "The Minimum PHP Version Requirement for <b>Dokan</b> is %1$s. You are Running PHP %2$s"
msgstr ""

#: dokan-class.php:411
msgid "Get Pro"
msgstr ""

#: dokan-class.php:414
#: includes/Admin/AdminBar.php:83
#: includes/Admin/Menu.php:73
#: includes/Dashboard/Templates/Settings.php:60
#: includes/Dashboard/Templates/Settings.php:67
#: includes/functions-dashboard-navigation.php:63
#: assets/js/vue-admin.js:2
msgid "Settings"
msgstr ""

#: dokan-class.php:415
#: includes/Admin/Dashboard/Dashboard.php:164
#: templates/admin-header.php:119
#: assets/js/vue-admin.js:2
msgid "Documentation"
msgstr ""

#. translators: 1) interval period
#: includes/Abstracts/DokanBackgroundProcesses.php:87
msgid "Every %d Minutes"
msgstr ""

#: includes/Abstracts/DokanPromotion.php:82
msgid "Learn More &rarr;"
msgstr ""

#: includes/Abstracts/DokanPromotion.php:185
#: includes/Admin/Notices/LimitedTimePromotion.php:50
#: includes/Admin/Notices/Manager.php:186
#: includes/Admin/Notices/PluginReview.php:119
msgid "You have no permission to do that"
msgstr ""

#: includes/Abstracts/DokanPromotion.php:189
#: includes/Admin/Notices/LimitedTimePromotion.php:46
#: includes/Admin/Notices/Manager.php:181
#: includes/Admin/Notices/PluginReview.php:115
#: includes/Admin/Notices/WhatsNew.php:74
#: includes/Admin/Settings.php:121
#: includes/Admin/Settings.php:156
#: includes/Ajax.php:321
#: includes/ReverseWithdrawal/Ajax.php:37
msgid "Invalid nonce"
msgstr ""

#. translators: 1) rest api method name string
#: includes/Abstracts/DokanRESTController.php:30
#: includes/Abstracts/DokanRESTController.php:210
#: includes/Abstracts/DokanRESTController.php:285
msgid "Method '%s' not implemented. Must be overridden in subclass."
msgstr ""

#: includes/Abstracts/DokanRESTController.php:71
#: includes/REST/ProductController.php:935
msgid "To manipulate product variations you should use the /products/&lt;product_id&gt;/variations/&lt;id&gt; endpoint."
msgstr ""

#: includes/Abstracts/DokanRESTController.php:73
#: includes/REST/OrderController.php:238
#: includes/REST/ProductController.php:395
#: includes/REST/ProductController.php:420
msgid "Invalid ID."
msgstr ""

#. translators: 1) post type name
#: includes/Abstracts/DokanRESTController.php:193
msgid "The %s cannot be deleted."
msgstr ""

#: includes/Abstracts/DokanShortcode.php:11
msgid "$shortcode property is empty."
msgstr ""

#: includes/Abstracts/Settings.php:112
msgid "Settings values must be valid."
msgstr ""

#. translators: %s is Settings element type.
#: includes/Abstracts/SettingsElement.php:327
#: includes/Abstracts/SettingsElement.php:401
msgid "Settings %s Does not support adding any children."
msgstr ""

#. translators: %s is Settings element type.
#: includes/Abstracts/SettingsElement.php:420
msgid "Settings %s Does not support removing any children."
msgstr ""

#: includes/Abstracts/StatusElement.php:162
#: includes/Abstracts/StatusElement.php:210
msgid "This element does not support child element."
msgstr ""

#. translators: %s is Status element type.
#: includes/Abstracts/StatusElement.php:223
msgid "Status %s Does not support adding any children."
msgstr ""

#: includes/Admin/AdminBar.php:56
#: includes/Admin/Menu.php:55
#: includes/Admin/Settings.php:773
#: includes/functions-dashboard-navigation.php:34
#: includes/Install/Installer.php:233
#: assets/js/vue-admin.js:2
msgid "Dashboard"
msgstr ""

#: includes/Admin/AdminBar.php:65
#: includes/Admin/Menu.php:34
#: includes/Admin/Menu.php:56
#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:104
#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:107
#: includes/Admin/SetupWizard.php:277
#: includes/functions-dashboard-navigation.php:55
#: includes/functions.php:2783
#: templates/withdraw/header.php:11
#: assets/js/dokan-admin-dashboard.js:172
#: assets/js/frontend.js:172
msgid "Withdraw"
msgstr ""

#: includes/Admin/AdminBar.php:74
#: includes/Admin/Menu.php:65
msgid "PRO Features"
msgstr ""

#: includes/Admin/AdminBar.php:138
msgid "Visit Shop"
msgstr ""

#: includes/Admin/AdminBar.php:147
msgid "Visit Stores"
msgstr ""

#: includes/Admin/AdminBar.php:156
msgid "Visit Vendor Dashboard"
msgstr ""

#: includes/Admin/Dashboard/Dashboard.php:58
msgid "The page must be an instance of Pageable."
msgstr ""

#: includes/Admin/Dashboard/Dashboard.php:120
#: assets/js/dokan-admin-notice.js:2
#: assets/js/dokan-promo-notice.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Loading..."
msgstr ""

#: includes/Admin/Dashboard/Dashboard.php:142
msgid "What's New"
msgstr ""

#: includes/Admin/Dashboard/Dashboard.php:150
#: templates/admin-header.php:103
msgid "Get Support"
msgstr ""

#: includes/Admin/Dashboard/Dashboard.php:157
#: templates/admin-header.php:111
msgid "Community"
msgstr ""

#: includes/Admin/Dashboard/Dashboard.php:171
#: templates/admin-header.php:128
msgid "FAQ"
msgstr ""

#: includes/Admin/Dashboard/Dashboard.php:178
#: templates/admin-header.php:136
msgid "Basic & Fundamental"
msgstr ""

#: includes/Admin/Dashboard/Dashboard.php:185
#: templates/admin-header.php:145
msgid "Request a Feature"
msgstr ""

#: includes/Admin/Dashboard/Dashboard.php:192
#: templates/admin-header.php:153
#: assets/js/vue-admin.js:2
msgid "Import dummy data"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:29
msgid "Dokan Modules"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:30
#: includes/Privacy.php:91
#: assets/js/dokan-admin-dashboard.js:172
msgid "Modules"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:46
msgid "WooCommerce Booking Integration"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:48
msgid "Integrates WooCommerce Booking with Dokan."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:59
msgid "WooCommerce Bookings plugin"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:63
msgid "Color Scheme Customizer"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:65
msgid "A Dokan plugin Add-on to Customize Colors of Dokan Dashboard"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:76
msgid "Delivery Time"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:78
msgid "Let customer choose their order delivery date & time"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:89
msgid "Elementor"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:91
msgid "Elementor Page Builder widgets for Dokan"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:102
msgid "Elementor Free and Elementor Pro"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:106
msgid "Vendor Product Importer and Exporter"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:108
msgid "This is simple product import and export plugin for vendor"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:119
msgid "Follow Store"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:121
msgid "Send emails to customers when their favorite store updates."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:132
msgid "Geolocation"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:134
msgid "Search Products and Vendors by geolocation."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:146
msgid "EU Compliance Fields"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:148
msgid "EU Compliance Fields Support for Vendors."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:159
msgid "Live Chat"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:161
msgid "Live Chat Between Vendor & Customer."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:172
msgid "Live Search"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:174
msgid "Live product search for WooCommerce store."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:185
msgid "Wirecard"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:187
msgid "Wirecard payment gateway for Dokan."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:198
msgid "PayPal Marketplace"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:200
msgid "Enable Split payments, Multi-seller payments and all PayPal Commerce Platform (PCP) features."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:211
msgid "Product Addon"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:213
msgid "WooCommerce Product Addon support"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:224
msgid "WooCommerce Product Addon extension"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:228
msgid "Product Enquiry"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:230
msgid "Enquiry for a specific product to a seller."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:241
msgid "Product Q&A"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:243
msgid "Enquiry for a specific product to a seller by asking question publicly."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:254
msgid "Printful"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:256
msgid "Enable this module to allow vendors to create & sell custom on-demand products with no inventory via PRINTFUL."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:267
msgid "Report Abuse"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:269
msgid "Let customers report fraudulent or fake products."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:280
msgid "Return and Warranty Request"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:282
msgid "Manage return and warranty from vendor end."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:293
msgid "Seller Vacation"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:295
msgid "Using this plugin seller can go to vacation by closing their stores."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:306
msgid "ShipStation Integration"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:308
msgid "Adds ShipStation label printing support to Dokan. Requires server DomDocument support."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:319
msgid "Auction Integration"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:321
msgid "A plugin that combined WooCommerce simple auction and Dokan plugin."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:332
msgid "WooCommerce Simple Auctions"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:336
msgid "Single Product Multiple Vendor"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:338
msgid "A module that offers multiple vendor to sell a single product."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:349
msgid "Store Reviews"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:351
msgid "A plugin that allows customers to rate the sellers."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:362
msgid "Store Support"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:364
msgid "Enable vendors to provide support to customers from store page."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:375
msgid "Stripe Connect"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:377
msgid "Accept credit card payments and allow your sellers to get automatic split payment in Dokan via Stripe."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:388
msgid "Product Advertising"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:390
msgid "Admin can earn more by allowing vendors to advertise their products and give them the right exposure."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:399
msgid "Vendor Subscription"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:401
msgid "Subscription pack add-on for Dokan vendors."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:412
msgid "Vendor Analytics"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:414
msgid "A plugin for store and product analytics for vendor."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:423
msgid "Vendor Staff Manager"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:425
msgid "A plugin for manage store via vendor staffs."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:436
msgid "Product Subscription"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:438
msgid "WooCommerce Subscription integration for Dokan"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:449
msgid "WooCommerce Subscription Module"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:453
msgid "Vendor Verification"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:455
msgid "Dokan add-on to verify sellers."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:464
msgid "Wholesale"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:466
msgid "Offer any customer to buy product as a wholesale price from any vendors."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:477
msgid "Rank Math SEO"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:479
msgid "Manage SEO for products with Rank Math"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:490
msgid "Rank Math SEO (v1.0.80 or Later)"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:494
msgid "Table Rate Shipping"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:496
msgid "Deliver Products at the Right Time, With the Right Pay."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:507
msgid "MangoPay"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:509
msgid "Enable split payments, multi-seller payments, and other marketplace features given by MangoPay."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:520
msgid "Min Max Quantities"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:522
msgid "Set a minimum or maximum purchase quantity or amount for the products of your marketplace. "
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:534
msgid "Razorpay"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:536
msgid "Accept credit card payments and allow your sellers to get automatic split payment in Dokan via Razorpay."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:547
msgid "Seller Badge"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:549
msgid "Offer vendors varieties of badges by their performance in your marketplace."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:560
#: assets/js/vue-admin.js:2
msgid "Stripe Express"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:562
msgid "Enable split payments, multi-seller payments, Apple Pay, Google Pay, iDEAL and other marketplace features available in Stripe Express."
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:573
msgid "Request for Quotation"
msgstr ""

#: includes/Admin/Dashboard/Pages/Modules.php:575
msgid "Facilitate wholesale orders between merchants and customers with the option for quoted prices."
msgstr ""

#: includes/Admin/Dashboard/Pages/SetupGuide.php:35
#: assets/js/dokan-admin-dashboard.js:172
msgid "Setup Guide"
msgstr ""

#: includes/Admin/Dashboard/Pages/SetupGuide.php:36
#: templates/withdraw/withdraw-dashboard.php:112
#: assets/js/frontend.js:172
msgid "Setup"
msgstr ""

#: includes/Admin/Dashboard/Pages/Status.php:23
msgid "Dokan Status"
msgstr ""

#: includes/Admin/Dashboard/Pages/Status.php:24
#: includes/Admin/WithdrawLogExporter.php:114
#: includes/Product/functions.php:215
#: templates/my-orders.php:27
#: templates/orders/listing.php:33
#: templates/orders/listing.php:93
#: templates/orders/sub-order-related-order-meta-box-html.php:39
#: templates/products/products-listing-row.php:140
#: templates/products/products-listing.php:121
#: templates/sub-orders.php:47
#: templates/withdraw/pending-request-listing-dashboard.php:25
#: templates/withdraw/pending-request-listing.php:20
#: templates/withdraw/pending-request-listing.php:64
#: assets/js/frontend.js:172
#: assets/js/vue-admin.js:2
msgid "Status"
msgstr ""

#: includes/Admin/functions.php:208
#: includes/Admin/functions.php:475
#: includes/REST/AdminReportController.php:141
msgid "Total Sales"
msgstr ""

#: includes/Admin/functions.php:215
#: includes/Admin/functions.php:482
msgid "Total: "
msgstr ""

#: includes/Admin/functions.php:218
#: includes/Admin/functions.php:485
#: includes/reports.php:367
#: includes/REST/AdminReportController.php:149
msgid "Number of orders"
msgstr ""

#: includes/Admin/functions.php:225
#: includes/Admin/functions.php:492
msgid "sales"
msgstr ""

#: includes/Admin/functions.php:228
#: includes/Admin/functions.php:495
#: includes/REST/AdminReportController.php:156
msgid "Commision"
msgstr ""

#: includes/Admin/functions.php:235
#: includes/Admin/functions.php:502
msgid "Commision: "
msgstr ""

#: includes/Admin/Hooks.php:70
#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:110
#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:118
#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:127
#: includes/Admin/Settings.php:613
#: includes/Admin/Settings.php:624
#: includes/Admin/Settings.php:635
#: includes/Admin/SetupWizard.php:481
#: includes/Install/Installer.php:170
#: includes/Order/Admin/Hooks.php:97
#: includes/Order/Admin/Hooks.php:403
#: includes/wc-template.php:21
#: includes/wc-template.php:174
#: includes/wc-template.php:339
#: templates/my-orders.php:29
#: templates/orders/sub-order-related-order-meta-box-html.php:41
#: assets/js/vue-admin.js:2
msgid "Vendor"
msgstr ""

#: includes/Admin/Hooks.php:97
#: assets/js/vue-admin.js:2
msgid "Select vendor"
msgstr ""

#: includes/Admin/Hooks.php:103
msgid "You can search vendors and assign them."
msgstr ""

#: includes/Admin/Hooks.php:116
msgid "Unauthorized operation"
msgstr ""

#. translators: %s: withdraw count
#: includes/Admin/Menu.php:40
msgid "Withdraw %s"
msgstr ""

#: includes/Admin/Menu.php:57
#: includes/Admin/Settings.php:358
#: includes/ReverseWithdrawal/Hooks.php:252
#: templates/reverse-withdrawal/header.php:9
#: assets/js/vue-admin.js:2
msgid "Reverse Withdrawal"
msgstr ""

#: includes/Admin/Menu.php:61
#: assets/js/vue-admin.js:2
msgid "Vendors"
msgstr ""

#: includes/Admin/Menu.php:72
msgid "<span style=\"color:#f18500\">Help</span>"
msgstr ""

#. translators: %s permalink settings url
#: includes/Admin/Notices/Manager.php:116
msgid "The <strong>Plain</strong> permalink structure is not working for the Dokan plugin. Please change your permalink structure from <a href=\"%s\">Settings > Permalinks</a>"
msgstr ""

#: includes/Admin/Notices/Manager.php:121
msgid "Go to Settings"
msgstr ""

#: includes/Admin/Notices/Manager.php:146
msgid "Dokan came up with a new look!"
msgstr ""

#: includes/Admin/Notices/Manager.php:147
msgid "A new rebranded look is introduced in the entire platform. Check the updated visuals in different places."
msgstr ""

#: includes/Admin/Notices/Manager.php:214
msgid "Dokan Update Required"
msgstr ""

#: includes/Admin/Notices/Manager.php:215
msgid "To ensure all the feature compatibility and accessibility, Dokan Pro minimum v3.14.0 is required."
msgstr ""

#: includes/Admin/Notices/Manager.php:219
msgid "Update Now"
msgstr ""

#: includes/Admin/Notices/PluginReview.php:66
msgid "Enjoying Dokan Multivendor Marketplace?"
msgstr ""

#: includes/Admin/Notices/PluginReview.php:67
msgid "If our plugin is performing well for you, it would be great if you could kindly write a review about <strong>Dokan</strong> on <strong>WordPress.org</strong>. It would give us insights to grow and improve this plugin."
msgstr ""

#: includes/Admin/Notices/PluginReview.php:78
msgid "Yes, You Deserve It"
msgstr ""

#: includes/Admin/Notices/PluginReview.php:84
msgid "Maybe Later"
msgstr ""

#: includes/Admin/Notices/PluginReview.php:93
msgid "I've Added My Review"
msgstr ""

#: includes/Admin/Notices/PluginReview.php:125
#: includes/REST/AdminOnboardingController.php:151
msgid "Invalid request"
msgstr ""

#: includes/Admin/Notices/PluginReview.php:147
msgid "Request failed"
msgstr ""

#: includes/Admin/Notices/UpgradeToV4.php:39
msgid "Dokan Pro Upgrade Required!"
msgstr ""

#: includes/Admin/Notices/UpgradeToV4.php:40
msgid "A major update happened in Dokan Pro which is available in v4.0.0. You are using a previous version which may run into some issues if you don't update so requesting you to update into the latest version."
msgstr ""

#. translators: %s: plugin version
#: includes/Admin/Notices/WhatsNew.php:45
msgid "Check What's new in Dokan Version %s"
msgstr ""

#: includes/Admin/Notices/WhatsNew.php:56
msgid "View Details"
msgstr ""

#: includes/Admin/OnboardingSetup/AdminSetupGuide.php:55
msgid "The admin onboarding step must be an instance of AbstractStep."
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:96
#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:99
#: includes/Admin/Settings.php:376
msgid "Appearance"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:102
msgid "Store Info"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:105
msgid "Contact Form on Store Page"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:106
msgid "Display a contact form on vendor store pages for customer inquiries"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:107
#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:116
#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:128
#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:136
#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:144
msgid "Hide"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:108
#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:117
#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:129
#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:137
#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:145
#: assets/js/dashboard-charts.js:1
msgid "Show"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:114
msgid "Store Sidebar From Theme"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:115
msgid "Show/hide the sidebar on vendor store pages"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:124
msgid "Vendor Info on Product Page"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:127
#: includes/Admin/Settings.php:938
msgid "Email Address"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:135
#: includes/Admin/Settings.php:939
#: includes/Admin/UserProfile.php:216
#: templates/account/update-customer-to-vendor.php:48
#: templates/account/vendor-registration.php:45
#: templates/global/seller-registration-form.php:54
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Phone Number"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/AppearanceStep.php:143
#: includes/Admin/Settings.php:940
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:249
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:250
msgid "Store Address"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:101
#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:104
msgid "Basic"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:107
#: templates/admin-setup-wizard/step-store.php:14
msgid "Shipping Fee Recipient"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:108
msgid "Choose who receives shipping charges - Admin keeps all shipping fees or Vendors receive fees for their products"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:109
#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:117
#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:126
#: includes/Admin/Settings.php:614
#: includes/Admin/Settings.php:625
#: includes/Admin/Settings.php:636
#: includes/Admin/SetupWizard.php:482
msgid "Admin"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:115
#: templates/admin-setup-wizard/step-store.php:27
msgid "Product Tax Fee Recipient"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:116
msgid "Determine who collects product taxes - Admin manages all tax collection or Vendors handle their product taxes"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:124
#: templates/admin-setup-wizard/step-store.php:41
msgid "Shipping Tax Fee Recipient"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:125
msgid "Select who receives shipping tax - Admin centralizes all shipping tax or Vendors collect shipping tax for their orders"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:133
msgid "Vendors can change order status"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:134
msgid "Allow vendors to update order statuses (processing, completed, etc.) for their products"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:137
#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:146
#: includes/Admin/OnboardingSetup/Steps/CommissionStep.php:139
#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:112
#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:121
#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:130
#: assets/js/vue-admin.js:2
msgid "Enabled"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:138
#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:147
#: includes/Admin/OnboardingSetup/Steps/CommissionStep.php:140
#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:113
#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:122
#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:131
#: assets/js/vue-admin.js:2
msgid "Disabled"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:142
msgid "New Vendor Selling Directly"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/BasicStep.php:143
msgid "Automatically enable selling capabilities for newly registered vendors"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/CommissionStep.php:105
#: includes/Admin/OnboardingSetup/Steps/CommissionStep.php:108
#: includes/Admin/Settings.php:518
#: includes/Admin/SetupWizard.php:272
#: includes/functions.php:997
#: includes/Order/Admin/Hooks.php:96
#: includes/ReverseWithdrawal/Helper.php:65
#: templates/orders/commission-meta-box-html.php:48
#: templates/products/dokan-products-edit-bulk-commission.php:17
msgid "Commission"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/CommissionStep.php:111
msgid "Commission Type"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/CommissionStep.php:112
msgid "Select a commission type for your marketplace"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/CommissionStep.php:113
#: includes/functions.php:3274
#: templates/products/dokan-products-edit-bulk-commission.php:28
msgid "Fixed"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/CommissionStep.php:114
#: includes/functions.php:3275
msgid "Category Based"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/CommissionStep.php:120
#: includes/Admin/OnboardingSetup/Steps/CommissionStep.php:146
#: includes/Admin/Settings.php:533
#: includes/Admin/Settings.php:586
#: includes/Product/Hooks.php:484
#: assets/js/dokan-setup-wizard-commission.js:1
#: assets/js/vue-admin.js:2
msgid "Admin Commission"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/CommissionStep.php:121
#: includes/Admin/Settings.php:560
msgid "Amount you will get from sales in both percentage and fixed fee"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/CommissionStep.php:135
#: includes/Admin/Settings.php:571
msgid "Apply Parent Category Commission to All Subcategories"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/CommissionStep.php:136
#: includes/Admin/Settings.php:572
msgid "Important: 'All Categories' commission serves as your marketplace's default rate and cannot be empty. If 0 is given in value, then the marketplace will deduct no commission from vendors"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/CommissionStep.php:147
#: includes/Admin/Settings.php:587
msgid "Amount you will get from each sale"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:110
#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/PayPal.php:39
#: includes/Withdraw/functions.php:15
#: assets/js/vue-admin.js:2
msgid "PayPal"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:111
msgid "Enable PayPal for your vendor as a withdraw method"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:119
#: includes/Withdraw/functions.php:20
msgid "Bank Transfer"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:120
msgid "Enable Bank Transfer for your vendor as a withdraw method"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:128
#: assets/js/vue-admin.js:2
msgid "Skrill"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:129
msgid "Enable Skrill for your vendor as a withdraw method"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:137
msgid "Minimum Withdraw Limits"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:138
msgid "Set the minimum balance required before vendors can request withdrawals"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:145
#: includes/Admin/Settings.php:753
#: includes/Admin/SetupWizard.php:726
msgid "Order Status for Withdraw"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:146
msgid "Define which order status makes funds eligible for withdrawal"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:147
#: includes/Admin/Settings.php:410
#: includes/Admin/SetupWizard.php:733
#: includes/Dashboard/Templates/Dashboard.php:109
#: includes/Order/functions.php:430
#: templates/dashboard/orders-widget.php:33
msgid "Completed"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:148
#: includes/Admin/Settings.php:411
#: includes/Admin/SetupWizard.php:740
#: includes/Dashboard/Templates/Dashboard.php:119
#: includes/Order/functions.php:445
#: templates/dashboard/orders-widget.php:43
#: templates/orders/listing.php:142
msgid "Processing"
msgstr ""

#: includes/Admin/OnboardingSetup/Steps/WithdrawStep.php:149
#: includes/Dashboard/Templates/Dashboard.php:134
msgid "On Hold"
msgstr ""

#: includes/Admin/Pointers.php:107
msgid "Important Details At a Glance"
msgstr ""

#: includes/Admin/Pointers.php:108
msgid "View the status of your marketplace including vendors and withdraw from here."
msgstr ""

#: includes/Admin/Pointers.php:114
#: includes/Admin/Pointers.php:131
#: includes/Admin/Pointers.php:181
#: includes/Admin/Pointers.php:198
#: includes/Admin/Pointers.php:215
#: assets/js/dokan-admin-dashboard.js:172
#: assets/js/dokan-admin-onboard.js:172
#: assets/js/vue-admin.js:2
msgid "Next"
msgstr ""

#: includes/Admin/Pointers.php:124
msgid "Your Sales Overview"
msgstr ""

#: includes/Admin/Pointers.php:125
msgid "Get a complete overview of your sales, orders and commissions."
msgstr ""

#: includes/Admin/Pointers.php:138
msgid "News & Updates"
msgstr ""

#: includes/Admin/Pointers.php:139
msgid "Get all the latest news and updates of Dokan from here."
msgstr ""

#: includes/Admin/Pointers.php:174
#: includes/Admin/Settings.php:335
msgid "General Settings"
msgstr ""

#: includes/Admin/Pointers.php:175
msgid "Configure all general settings for your marketplace from this tab."
msgstr ""

#: includes/Admin/Pointers.php:191
#: includes/Admin/Settings.php:340
msgid "Selling Options"
msgstr ""

#: includes/Admin/Pointers.php:192
msgid "You can configure different selling options for your vendors"
msgstr ""

#: includes/Admin/Pointers.php:208
#: includes/Admin/Settings.php:349
msgid "Withdraw Options"
msgstr ""

#: includes/Admin/Pointers.php:209
msgid "Configure your vendor's balance withdrawal options"
msgstr ""

#: includes/Admin/Pointers.php:221
msgid "Dokan Pages"
msgstr ""

#: includes/Admin/Pointers.php:222
msgid "Dokan requires some pages to be configured and you can set them up here"
msgstr ""

#: includes/Admin/Promotion.php:39
msgid "Create Customizable Paid Subscription Packs For Your Vendors"
msgstr ""

#: includes/Admin/Promotion.php:40
msgid "By using this module, make it mandatory for your vendors to buy a subscription pack to sell products in your marketplace."
msgstr ""

#: includes/Admin/Promotion.php:47
msgid "Increase conversions 270% by displaying reviews"
msgstr ""

#: includes/Admin/Promotion.php:48
msgid "This extension enables your customers to post a review for each store available on your site."
msgstr ""

#: includes/Admin/Promotion.php:55
msgid "Verify Your Sellers To Earn Customer Reliability"
msgstr ""

#: includes/Admin/Promotion.php:56
msgid "Allow your vendors to verify their stores using social profiles, phone, photo ID etc. using this advanced and handy module."
msgstr ""

#: includes/Admin/Promotion.php:63
msgid "Earn more from multiple commission type"
msgstr ""

#: includes/Admin/Promotion.php:64
msgid "With this feature, you will be able to set different type of commission for each vendor according to the products they are selling. Meaning more earning for you."
msgstr ""

#: includes/Admin/Promotion.php:71
msgid "Get priority support 24/7/365"
msgstr ""

#: includes/Admin/Promotion.php:72
msgid "Need to solve a conflict with your plugins, have a theme layout issues with Dokan or any kind of problem you might face, we are here for you"
msgstr ""

#: includes/Admin/Promotion.php:79
msgid "97% of consumers look for deals when they shop"
msgstr ""

#: includes/Admin/Promotion.php:80
msgid "No matter what type of coupon you want to create you can do that easily using the pro version of Dokan. Set the coupon title, fix discount types, restrict email addresses and much more."
msgstr ""

#: includes/Admin/Promotion.php:87
msgid "Exciting premium features to take your marketplace"
msgstr ""

#: includes/Admin/Promotion.php:88
msgid "With the simplest configuration options available, only by enabling a single toggle button you will be able to do everything your competitors are doing and even more."
msgstr ""

#: includes/Admin/RecommendedPlugins.php:37
msgid "weMail"
msgstr ""

#: includes/Admin/RecommendedPlugins.php:38
msgid "Simplified Email Marketing Solution for WordPress!"
msgstr ""

#: includes/Admin/RecommendedPlugins.php:40
msgid "weMail logo"
msgstr ""

#: includes/Admin/RecommendedPlugins.php:46
msgid "WooCommerce Conversion Tracking"
msgstr ""

#: includes/Admin/RecommendedPlugins.php:47
msgid "Track conversions on your WooCommerce store like a pro!"
msgstr ""

#: includes/Admin/RecommendedPlugins.php:49
msgid "WooCommerce Conversion Tracking logo"
msgstr ""

#: includes/Admin/RecommendedPlugins.php:55
msgid "Texty"
msgstr ""

#: includes/Admin/RecommendedPlugins.php:56
msgid "SMS Notification for WordPress, WooCommerce, Dokan and more!"
msgstr ""

#: includes/Admin/RecommendedPlugins.php:58
msgid "Texty logo"
msgstr ""

#: includes/Admin/Settings.php:117
msgid "You have no permission to get settings value"
msgstr ""

#: includes/Admin/Settings.php:152
#: includes/Admin/Settings.php:1033
msgid "You are not authorized to perform this action."
msgstr ""

#: includes/Admin/Settings.php:160
msgid "`section` parameter is required."
msgstr ""

#: includes/Admin/Settings.php:192
msgid "Setting has been saved successfully."
msgstr ""

#: includes/Admin/Settings.php:331
#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Payments.php:60
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:48
msgid "General"
msgstr ""

#: includes/Admin/Settings.php:333
msgid "Site Settings and Store Options"
msgstr ""

#: includes/Admin/Settings.php:336
msgid "You can configure your general site settings and vendor store options from this settings menu. Dokan offers countless custom options when setting up your store to provide you with the ultimate flexibility."
msgstr ""

#: includes/Admin/Settings.php:342
msgid "Store Settings, Commissions"
msgstr ""

#: includes/Admin/Settings.php:344
msgid "Selling Option Settings"
msgstr ""

#: includes/Admin/Settings.php:345
msgid "You can configure commissions scales and vendor capabilities from this menu."
msgstr ""

#: includes/Admin/Settings.php:351
msgid "Withdraw Settings, Threshold"
msgstr ""

#: includes/Admin/Settings.php:353
msgid "Withdraw Settings"
msgstr ""

#: includes/Admin/Settings.php:354
msgid "You can configure your store's withdrawal methods, charges, limits, order status and more."
msgstr ""

#: includes/Admin/Settings.php:360
msgid "Admin commission config (on COD)"
msgstr ""

#: includes/Admin/Settings.php:362
msgid "Reverse Withdrawal Settings"
msgstr ""

#: includes/Admin/Settings.php:363
msgid "Configure commission from vendors on Cash on Delivery orders, method and threshold for reverse balance, restrictive actions on vendors and more."
msgstr ""

#: includes/Admin/Settings.php:367
msgid "Page Settings"
msgstr ""

#: includes/Admin/Settings.php:369
msgid "Store Page Settings Manage"
msgstr ""

#: includes/Admin/Settings.php:371
msgid "Site and Store Page Settings"
msgstr ""

#: includes/Admin/Settings.php:372
msgid "You can configure and setup your necessary page settings from this menu."
msgstr ""

#: includes/Admin/Settings.php:378
msgid "Custom Store Appearance"
msgstr ""

#: includes/Admin/Settings.php:380
msgid "Appearance Settings"
msgstr ""

#: includes/Admin/Settings.php:381
msgid "You can configure your store appearance settings, configure map API, Google reCaptcha and more. Dokan offers various store header templates to choose from."
msgstr ""

#: includes/Admin/Settings.php:385
#: includes/Admin/Settings.php:969
msgid "Privacy Policy"
msgstr ""

#: includes/Admin/Settings.php:387
msgid "Update Store Privacy Policies"
msgstr ""

#: includes/Admin/Settings.php:388
msgid "Privacy Settings"
msgstr ""

#: includes/Admin/Settings.php:389
msgid "You can configure your site's privacy settings and policy."
msgstr ""

#: includes/Admin/Settings.php:412
#: includes/Order/functions.php:440
msgid "On-hold"
msgstr ""

#: includes/Admin/Settings.php:421
msgid "Site Settings"
msgstr ""

#: includes/Admin/Settings.php:422
msgid "Configure your site settings and control access to your site."
msgstr ""

#: includes/Admin/Settings.php:426
msgid "Admin Area Access"
msgstr ""

#: includes/Admin/Settings.php:427
msgid "Prevent vendors from accessing the wp-admin dashboard area. If HPOS feature is enabled, admin access will be blocked regardless of this setting."
msgstr ""

#: includes/Admin/Settings.php:433
#: templates/admin-setup-wizard/step-store.php:7
msgid "Vendor Store URL"
msgstr ""

#. translators: %s: store url
#: includes/Admin/Settings.php:435
msgid "Define the vendor store URL (%s<strong>[this-text]</strong>/[vendor-name])"
msgstr ""

#: includes/Admin/Settings.php:441
msgid "Vendor Setup Wizard Logo"
msgstr ""

#: includes/Admin/Settings.php:443
msgid "Recommended logo size ( 270px X 90px ). If no logo is uploaded, site title is shown by default."
msgstr ""

#: includes/Admin/Settings.php:447
msgid "Vendor Setup Wizard Message"
msgstr ""

#: includes/Admin/Settings.php:449
msgid "Thank you for choosing The Marketplace to power your online store! This quick setup wizard will help you configure the basic settings. <strong>It’s completely optional and shouldn’t take longer than two minutes.</strong>"
msgstr ""

#: includes/Admin/Settings.php:453
msgid "Disable Welcome Wizard"
msgstr ""

#: includes/Admin/Settings.php:454
msgid "Disable welcome wizard for newly registered vendors"
msgstr ""

#: includes/Admin/Settings.php:457
msgid "If checked, vendors will not be prompted through a guided setup process but redirected straight to the vendor dashboard."
msgstr ""

#: includes/Admin/Settings.php:467
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:31
msgid "Vendor Store Settings"
msgstr ""

#: includes/Admin/Settings.php:468
msgid "Configure your vendor store settings and setup your store policy for vendor."
msgstr ""

#: includes/Admin/Settings.php:473
msgid "Store Terms and Conditions"
msgstr ""

#: includes/Admin/Settings.php:474
msgid "Enable terms and conditions for vendor stores"
msgstr ""

#: includes/Admin/Settings.php:480
msgid "Store Products Per Page"
msgstr ""

#: includes/Admin/Settings.php:481
msgid "Set how many products to display per page on the vendor store page."
msgstr ""

#: includes/Admin/Settings.php:487
msgid "Enable Address Fields"
msgstr ""

#: includes/Admin/Settings.php:488
msgid "Add Address Fields on the Vendor Registration form"
msgstr ""

#: includes/Admin/Settings.php:500
msgid "Product Page Settings"
msgstr ""

#: includes/Admin/Settings.php:501
msgid "Configure single product page for vendors."
msgstr ""

#: includes/Admin/Settings.php:506
msgid "Enable More Products Tab"
msgstr ""

#: includes/Admin/Settings.php:507
msgid "Enable \"More Products\" tab on the single product page."
msgstr ""

#: includes/Admin/Settings.php:520
msgid "Define commission types, admin commissions, shipping and tax recipients, and more."
msgstr ""

#: includes/Admin/Settings.php:524
msgid "Commission Type "
msgstr ""

#: includes/Admin/Settings.php:525
msgid "Select a commission type for vendor"
msgstr ""

#: includes/Admin/Settings.php:529
msgid "Select a commission type"
msgstr ""

#: includes/Admin/Settings.php:538
msgid "Percent Fee"
msgstr ""

#: includes/Admin/Settings.php:541
msgid "Amount you will get from sales in percentage (10%)"
msgstr ""

#: includes/Admin/Settings.php:548
msgid "Fixed Fee"
msgstr ""

#: includes/Admin/Settings.php:551
msgid "Amount you will get from sales in flat rate(+5)"
msgstr ""

#: includes/Admin/Settings.php:575
#: assets/js/vue-admin.js:2
msgid "When enabled, changing a parent category's commission rate will automatically update all its subcategories. Disable this option to maintain independent commission rates for subcategories"
msgstr ""

#: includes/Admin/Settings.php:602
msgid "Fee Recipients"
msgstr ""

#: includes/Admin/Settings.php:604
msgid "Define the fees that admin or vendor will recive"
msgstr ""

#: includes/Admin/Settings.php:609
msgid "Shipping Fee"
msgstr ""

#: includes/Admin/Settings.php:610
msgid "Who will be receiving the shipping fees? Note that, tax fees for corresponding shipping method will not be included with shipping fees."
msgstr ""

#: includes/Admin/Settings.php:620
msgid "Product Tax Fee"
msgstr ""

#: includes/Admin/Settings.php:621
msgid "Who will be receiving the tax fees for products? Note that, shipping tax fees will not be included with product tax."
msgstr ""

#: includes/Admin/Settings.php:631
msgid "Shipping Tax Fee"
msgstr ""

#: includes/Admin/Settings.php:632
msgid "Who will be receiving the tax fees for shipping?"
msgstr ""

#: includes/Admin/Settings.php:647
#: assets/js/vue-admin.js:2
msgid "Vendor Capabilities"
msgstr ""

#: includes/Admin/Settings.php:649
msgid "Configure your multivendor site settings and vendor selling capabilities."
msgstr ""

#: includes/Admin/Settings.php:654
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Enable Selling"
msgstr ""

#: includes/Admin/Settings.php:655
msgid "Immediately enable selling for newly registered vendors"
msgstr ""

#: includes/Admin/Settings.php:659
msgid "If checked, vendors will have permission to sell immediately after registration. If unchecked, newly registered vendors cannot add products until selling capability is activated manually from admin dashboard."
msgstr ""

#: includes/Admin/Settings.php:663
msgid "One Page Product Creation"
msgstr ""

#: includes/Admin/Settings.php:664
msgid "Add new product in single page view"
msgstr ""

#: includes/Admin/Settings.php:667
msgid "If disabled, instead of a single add product page it will open a pop up window or vendor will redirect to product page when adding new product."
msgstr ""

#: includes/Admin/Settings.php:671
msgid "Disable Product Popup"
msgstr ""

#: includes/Admin/Settings.php:672
msgid "Disable add new product in popup view"
msgstr ""

#: includes/Admin/Settings.php:678
msgid "If disabled, instead of a pop up window vendor will redirect to product page when adding new product."
msgstr ""

#: includes/Admin/Settings.php:682
#: templates/admin-setup-wizard/step-selling.php:20
#: assets/js/vue-admin.js:2
msgid "Order Status Change"
msgstr ""

#: includes/Admin/Settings.php:683
msgid "Allow vendor to update order status"
msgstr ""

#: includes/Admin/Settings.php:686
msgid "Checking this will enable sellers to change the order status. If unchecked, only admin can change the order status."
msgstr ""

#: includes/Admin/Settings.php:690
msgid "Select any category"
msgstr ""

#: includes/Admin/Settings.php:691
msgid "Allow vendors to select any category while creating/editing products."
msgstr ""

#: includes/Admin/Settings.php:715
#: includes/Admin/SetupWizard.php:676
msgid "Withdraw Methods"
msgstr ""

#: includes/Admin/Settings.php:716
msgid "Select suitable withdraw methods for vendors"
msgstr ""

#: includes/Admin/Settings.php:720
msgid "Check to add available payment methods for vendors to withdraw money."
msgstr ""

#: includes/Admin/Settings.php:724
msgid "Withdraw Charges"
msgstr ""

#: includes/Admin/Settings.php:725
msgid "Select suitable withdraw charges for vendors"
msgstr ""

#: includes/Admin/Settings.php:743
#: includes/Admin/SetupWizard.php:719
msgid "Minimum Withdraw Limit"
msgstr ""

#: includes/Admin/Settings.php:744
msgid "Minimum balance required to make a withdraw request. Leave blank to set no minimum limits."
msgstr ""

#: includes/Admin/Settings.php:754
#: includes/Admin/SetupWizard.php:745
msgid "Order status for which vendor can make a withdraw request."
msgstr ""

#: includes/Admin/Settings.php:760
msgid "Select the order status that will allow vendors to make withdraw request. We prefer you select \"completed\", \"processing\"."
msgstr ""

#: includes/Admin/Settings.php:764
msgid "Exclude COD Payments"
msgstr ""

#: includes/Admin/Settings.php:765
msgid "If an order is paid with Cash on Delivery (COD), then exclude that payment from vendor balance."
msgstr ""

#: includes/Admin/Settings.php:774
msgid "Select a page to show vendor dashboard"
msgstr ""

#: includes/Admin/Settings.php:777
#: includes/Admin/Settings.php:784
#: includes/Admin/Settings.php:792
#: includes/Admin/Settings.php:802
#: includes/Admin/Settings.php:964
msgid "Select page"
msgstr ""

#: includes/Admin/Settings.php:781
#: includes/Install/Installer.php:245
#: templates/global/header-menu.php:52
msgid "My Orders"
msgstr ""

#: includes/Admin/Settings.php:782
msgid "Select a page to show my orders"
msgstr ""

#: includes/Admin/Settings.php:789
msgid "Store Listing"
msgstr ""

#: includes/Admin/Settings.php:790
msgid "Select a page to show all stores"
msgstr ""

#: includes/Admin/Settings.php:798
msgid "Select where you want to add Dokan pages."
msgstr ""

#: includes/Admin/Settings.php:799
msgid "Terms and Conditions Page"
msgstr ""

#: includes/Admin/Settings.php:801
msgid "Select a page to display the Terms and Conditions of your store for Vendors."
msgstr ""

#: includes/Admin/Settings.php:809
msgid "Store Appearance"
msgstr ""

#: includes/Admin/Settings.php:810
msgid "Configure your site appearances."
msgstr ""

#: includes/Admin/Settings.php:814
msgid "Show map on Store Page"
msgstr ""

#: includes/Admin/Settings.php:815
msgid "Enable map of the store location in the store sidebar"
msgstr ""

#: includes/Admin/Settings.php:821
#: templates/admin-setup-wizard/step-store.php:55
msgid "Map API Source"
msgstr ""

#: includes/Admin/Settings.php:822
msgid "Which map API source you want to use in your site?"
msgstr ""

#: includes/Admin/Settings.php:827
#: includes/Admin/SetupWizard.php:496
#: assets/js/vue-admin.js:2
msgid "Google Maps"
msgstr ""

#: includes/Admin/Settings.php:828
#: includes/Admin/SetupWizard.php:497
msgid "Mapbox"
msgstr ""

#: includes/Admin/Settings.php:833
#: templates/admin-setup-wizard/step-store.php:68
msgid "Google Map API Key"
msgstr ""

#: includes/Admin/Settings.php:834
msgid "<a href=\"https://developers.google.com/maps/documentation/javascript/\" target=\"_blank\" rel=\"noopener noreferrer\">API Key</a> is needed to display map on store page"
msgstr ""

#: includes/Admin/Settings.php:837
msgid "Insert Google API Key (with hyperlink) to display store map."
msgstr ""

#: includes/Admin/Settings.php:846
#: templates/admin-setup-wizard/step-store.php:84
msgid "Mapbox Access Token"
msgstr ""

#: includes/Admin/Settings.php:847
msgid "<a href=\"https://docs.mapbox.com/help/how-mapbox-works/access-tokens/\" target=\"_blank\" rel=\"noopener noreferrer\">Access Token</a> is needed to display map on store page"
msgstr ""

#: includes/Admin/Settings.php:850
msgid "Insert Mapbox Access Token (with hyperlink) to display store map."
msgstr ""

#. translators: 1) Opening anchor tag, 2) Closing anchor tag, 3) Opening anchor tag, 4) Closing anchor tag
#: includes/Admin/Settings.php:862
msgid "%1$sreCAPTCHA_v3%2$s credentials required to enable invisible captcha for contact forms. %3$sGet Help%4$s"
msgstr ""

#: includes/Admin/Settings.php:868
msgid "Google reCAPTCHA Validation"
msgstr ""

#: includes/Admin/Settings.php:870
msgid "You can successfully connect to your Google reCaptcha account from here."
msgstr ""

#: includes/Admin/Settings.php:878
msgid "Site Key"
msgstr ""

#: includes/Admin/Settings.php:879
msgid "Insert Google reCAPTCHA v3 site key."
msgstr ""

#: includes/Admin/Settings.php:885
msgid "Secret Key"
msgstr ""

#: includes/Admin/Settings.php:887
msgid "Insert Google reCAPTCHA v3 secret key."
msgstr ""

#: includes/Admin/Settings.php:895
msgid "Show Contact Form on Store Page"
msgstr ""

#: includes/Admin/Settings.php:896
msgid "Display a vendor contact form in the store sidebar"
msgstr ""

#: includes/Admin/Settings.php:903
msgid "Select a store header for your store."
msgstr ""

#: includes/Admin/Settings.php:904
msgid "Store Header Template"
msgstr ""

#: includes/Admin/Settings.php:915
msgid "Store Opening Closing Time Widget"
msgstr ""

#: includes/Admin/Settings.php:916
msgid "Enable store opening & closing time widget in the store sidebar"
msgstr ""

#: includes/Admin/Settings.php:922
msgid "Enable Store Sidebar From Theme"
msgstr ""

#: includes/Admin/Settings.php:923
msgid "Enable showing store sidebar from your theme."
msgstr ""

#: includes/Admin/Settings.php:929
msgid "Hide Vendor Info"
msgstr ""

#: includes/Admin/Settings.php:930
msgid "Hide vendor contact info from single store page."
msgstr ""

#: includes/Admin/Settings.php:945
msgid "Disable Dokan FontAwesome"
msgstr ""

#: includes/Admin/Settings.php:946
msgid "If disabled then dokan fontawesome library won't be loaded in frontend"
msgstr ""

#: includes/Admin/Settings.php:954
msgid "Enable Privacy Policy"
msgstr ""

#: includes/Admin/Settings.php:956
msgid "Enable privacy policy for vendor store contact form"
msgstr ""

#: includes/Admin/Settings.php:961
msgid "Privacy Page"
msgstr ""

#: includes/Admin/Settings.php:963
msgid "Select a page to show your privacy policy"
msgstr ""

#: includes/Admin/Settings.php:971
#: includes/functions.php:3249
msgid "Your personal data will be used to support your experience throughout this website, to manage access to your account, and for other purposes described in our [dokan_privacy_policy]"
msgstr ""

#: includes/Admin/Settings.php:972
msgid "Customize the Privacy Policy text that will be displayed on your store."
msgstr ""

#: includes/Admin/Settings.php:1044
msgid "Both section and field params are required."
msgstr ""

#: includes/Admin/Settings.php:1053
msgid "No filter found to refresh the setting options"
msgstr ""

#: includes/Admin/Settings.php:1085
msgid "Minimum Withdraw Limit can't be negative value."
msgstr ""

#: includes/Admin/Settings.php:1096
#: includes/ReverseWithdrawal/Admin/Settings.php:223
msgid "Validation error"
msgstr ""

#: includes/Admin/Settings.php:1114
msgid "Data Clear"
msgstr ""

#: includes/Admin/Settings.php:1115
msgid "Delete all data and tables related to Dokan and Dokan Pro plugin while deleting the Dokan plugin."
msgstr ""

#: includes/Admin/Settings.php:1118
msgid "Check this to remove Dokan related data and table from the database upon deleting the plugin. When you delete the Dokan lite version, it will also delete all the data related to Dokan Pro as well. This won't happen when the plugins are deactivated.."
msgstr ""

#: includes/Admin/SetupWizard.php:93
msgctxt "enhanced select"
msgid "No matches found"
msgstr ""

#: includes/Admin/SetupWizard.php:94
msgctxt "enhanced select"
msgid "Loading failed"
msgstr ""

#: includes/Admin/SetupWizard.php:95
msgctxt "enhanced select"
msgid "Please enter 1 or more characters"
msgstr ""

#: includes/Admin/SetupWizard.php:96
msgctxt "enhanced select"
msgid "Please enter %qty% or more characters"
msgstr ""

#: includes/Admin/SetupWizard.php:97
msgctxt "enhanced select"
msgid "Please delete 1 character"
msgstr ""

#: includes/Admin/SetupWizard.php:98
msgctxt "enhanced select"
msgid "Please delete %qty% characters"
msgstr ""

#: includes/Admin/SetupWizard.php:99
msgctxt "enhanced select"
msgid "You can only select 1 item"
msgstr ""

#: includes/Admin/SetupWizard.php:100
msgctxt "enhanced select"
msgid "You can only select %qty% items"
msgstr ""

#: includes/Admin/SetupWizard.php:101
msgctxt "enhanced select"
msgid "Loading more results&hellip;"
msgstr ""

#: includes/Admin/SetupWizard.php:102
msgctxt "enhanced select"
msgid "Searching&hellip;"
msgstr ""

#: includes/Admin/SetupWizard.php:258
#: includes/Vendor/SetupWizard.php:700
msgid "Introduction"
msgstr ""

#: includes/Admin/SetupWizard.php:262
#: includes/functions-dashboard-navigation.php:72
#: includes/Vendor/SetupWizard.php:705
#: assets/js/vue-admin.js:2
msgid "Store"
msgstr ""

#: includes/Admin/SetupWizard.php:267
#: includes/Admin/UserProfile.php:306
msgid "Selling"
msgstr ""

#: includes/Admin/SetupWizard.php:282
msgid "Recommended"
msgstr ""

#: includes/Admin/SetupWizard.php:287
#: includes/Vendor/SetupWizard.php:722
msgid "Ready!"
msgstr ""

#: includes/Admin/SetupWizard.php:386
msgid "Dokan &rsaquo; Setup Wizard"
msgstr ""

#: includes/Admin/SetupWizard.php:406
#: includes/Admin/SetupWizardNoWC.php:95
msgid "Return to the WordPress Dashboard"
msgstr ""

#: includes/Admin/SetupWizard.php:455
#: templates/admin-setup-wizard/step-no-wc-introduction.php:2
msgid "Welcome to the world of Dokan!"
msgstr ""

#: includes/Admin/SetupWizard.php:456
msgid "Thank you for choosing Dokan to power your online marketplace! This quick setup wizard will help you configure the basic settings. <strong>It’s completely optional and shouldn’t take longer than three minutes.</strong>"
msgstr ""

#: includes/Admin/SetupWizard.php:457
msgid "No time right now? If you don’t want to go through the wizard, you can skip and return to the WordPress dashboard. Come back anytime if you change your mind!"
msgstr ""

#: includes/Admin/SetupWizard.php:459
#: includes/Vendor/SetupWizard.php:171
#: templates/admin-setup-wizard/step-no-wc-introduction.php:74
msgid "Let's Go!"
msgstr ""

#: includes/Admin/SetupWizard.php:460
#: includes/Vendor/SetupWizard.php:172
msgid "Not right now"
msgstr ""

#: includes/Admin/SetupWizard.php:672
msgid "Withdraw Setup"
msgstr ""

#. translators: %s: withdraw method name
#: includes/Admin/SetupWizard.php:694
msgid "Enable %s for your vendor as a withdraw method"
msgstr ""

#: includes/Admin/SetupWizard.php:722
msgid "Minimum balance required to make a withdraw request ( Leave it blank to set no limits )"
msgstr ""

#: includes/Admin/SetupWizard.php:750
#: includes/Admin/SetupWizard.php:783
#: includes/Admin/SetupWizardWCAdmin.php:232
#: includes/Vendor/SetupWizard.php:351
#: includes/Vendor/SetupWizard.php:560
#: templates/admin-setup-wizard/step-commission.php:12
#: templates/admin-setup-wizard/step-selling.php:34
#: templates/admin-setup-wizard/step-store.php:153
msgid "Continue"
msgstr ""

#: includes/Admin/SetupWizard.php:751
#: includes/Vendor/SetupWizard.php:353
#: includes/Vendor/SetupWizard.php:562
#: templates/admin-setup-wizard/step-commission.php:13
#: templates/admin-setup-wizard/step-selling.php:35
#: templates/admin-setup-wizard/step-store.php:154
#: assets/js/dokan-admin-onboard.js:172
msgid "Skip this step"
msgstr ""

#: includes/Admin/SetupWizard.php:767
msgid "Recommended for All Dokan Marketplaces"
msgstr ""

#: includes/Admin/SetupWizard.php:769
msgid "Enhance your store with these recommended features."
msgstr ""

#: includes/Admin/SetupWizard.php:898
msgid "Your Marketplace is Ready!"
msgstr ""

#: includes/Admin/SetupWizard.php:903
#: assets/js/dokan-admin-dashboard.js:172
msgid "Visit Dokan Dashboard"
msgstr ""

#: includes/Admin/SetupWizard.php:904
msgid "More Settings"
msgstr ""

#: includes/Admin/SetupWizard.php:989
msgid "The following plugins will be installed and activated for you:"
msgstr ""

#: includes/Admin/SetupWizardNoWC.php:19
msgid "Welcome to Dokan"
msgstr ""

#: includes/Admin/SetupWizardNoWC.php:132
msgid "Error installing WooCommerce plugin"
msgstr ""

#: includes/Admin/SetupWizardNoWC.php:176
#: includes/functions-dashboard-navigation.php:79
#: includes/ReverseWithdrawal/Helper.php:70
#: includes/Vendor/SetupWizard.php:715
msgid "Payment"
msgstr ""

#: includes/Admin/SetupWizardNoWC.php:182
#: includes/Admin/SetupWizardWCAdmin.php:84
#: includes/Product/functions.php:233
#: assets/js/dashboard-charts.js:1
#: assets/js/vue-admin.js:2
msgid "Shipping"
msgstr ""

#. translators: %s: country name including the 'the' prefix if needed
#: includes/Admin/SetupWizardWCAdmin.php:75
msgid "We've created two Shipping Zones - for %s and for the rest of the world. Below you can set Flat Rate shipping costs for these Zones or offer Free Shipping."
msgstr ""

#: includes/Admin/SetupWizardWCAdmin.php:96
#: includes/Admin/SetupWizardWCAdmin.php:107
msgid "Did you know you can print shipping labels at home?"
msgstr ""

#: includes/Admin/SetupWizardWCAdmin.php:97
msgid "Use WooCommerce Shipping (powered by WooCommerce Services & Jetpack) to save time at the post office by printing your shipping labels at home."
msgstr ""

#: includes/Admin/SetupWizardWCAdmin.php:99
msgid "WooCommerce Services icon"
msgstr ""

#: includes/Admin/SetupWizardWCAdmin.php:108
msgid "We recommend using ShipStation to save time at the post office by printing your shipping labels at home. Try ShipStation free for 30 days."
msgstr ""

#: includes/Admin/SetupWizardWCAdmin.php:110
msgid "ShipStation icon"
msgstr ""

#: includes/Admin/SetupWizardWCAdmin.php:113
msgid "ShipStation"
msgstr ""

#: includes/Admin/SetupWizardWCAdmin.php:128
msgid "Shipping Zone"
msgstr ""

#: includes/Admin/SetupWizardWCAdmin.php:131
msgid "Shipping Method"
msgstr ""

#: includes/Admin/SetupWizardWCAdmin.php:156
msgid "Locations not covered by your other zones"
msgstr ""

#. translators: %1$s: live rates tooltip text, %2$s: shipping extensions URL
#: includes/Admin/SetupWizardWCAdmin.php:180
msgid "If you'd like to offer <span class=\"help_tip\" data-tip=\"%1$s\">live rates</span> from a specific carrier (e.g. UPS) you can find a variety of extensions available for WooCommerce <a href=\"%2$s\" target=\"_blank\">here</a>."
msgstr ""

#: includes/Admin/SetupWizardWCAdmin.php:192
msgid "A live rate is the exact cost to ship an order, quoted directly from the shipping carrier."
msgstr ""

#. translators: %1$s: weight unit dropdown, %2$s: dimension unit dropdown
#: includes/Admin/SetupWizardWCAdmin.php:207
msgid "We'll use %1$s for product weight and %2$s for product dimensions."
msgstr ""

#: includes/Admin/UserProfile.php:38
#: includes/Ajax.php:143
#: includes/Assets.php:712
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Available"
msgstr ""

#: includes/Admin/UserProfile.php:39
#: includes/Assets.php:713
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Not Available"
msgstr ""

#: includes/Admin/UserProfile.php:90
#: includes/Privacy.php:226
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:218
#: includes/Vendor/SetupWizard.php:277
#: templates/admin-setup-wizard/step-store-wc-fields.php:24
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Country"
msgstr ""

#: includes/Admin/UserProfile.php:94
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:222
msgid "Select a country&hellip;"
msgstr ""

#: includes/Admin/UserProfile.php:97
msgid "State/County"
msgstr ""

#: includes/Admin/UserProfile.php:98
msgid "State/County or state code"
msgstr ""

#: includes/Admin/UserProfile.php:103
msgid "Dokan Options"
msgstr ""

#: includes/Admin/UserProfile.php:108
msgid "Banner"
msgstr ""

#: includes/Admin/UserProfile.php:120
#: templates/settings/store-form.php:72
msgid "Upload banner"
msgstr ""

#. translators: %1$s: banner width, %2$s: banner height in integers
#. translators: 1) store banner width 2) store banner height
#: includes/Admin/UserProfile.php:125
#: templates/settings/store-form.php:86
msgid "Upload a banner for your store. Banner size is (%1$sx%2$s) pixels."
msgstr ""

#: includes/Admin/UserProfile.php:137
#: includes/REST/ReverseWithdrawalController.php:681
msgid "Store name"
msgstr ""

#: includes/Admin/UserProfile.php:144
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Store URL"
msgstr ""

#: includes/Admin/UserProfile.php:154
#: includes/Privacy.php:222
msgid "Address 1"
msgstr ""

#: includes/Admin/UserProfile.php:161
#: includes/Privacy.php:223
msgid "Address 2"
msgstr ""

#: includes/Admin/UserProfile.php:168
msgid "Town/City"
msgstr ""

#: includes/Admin/UserProfile.php:175
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:210
msgid "Zip Code"
msgstr ""

#: includes/Admin/UserProfile.php:241
msgid "Payment Options : "
msgstr ""

#: includes/Admin/UserProfile.php:246
msgid "Paypal Email "
msgstr ""

#: includes/Admin/UserProfile.php:254
msgid "Skrill Email "
msgstr ""

#: includes/Admin/UserProfile.php:263
msgid "Bank name "
msgstr ""

#: includes/Admin/UserProfile.php:269
msgid "Account Name "
msgstr ""

#: includes/Admin/UserProfile.php:275
msgid "Account Number "
msgstr ""

#: includes/Admin/UserProfile.php:281
msgid "Bank Address "
msgstr ""

#: includes/Admin/UserProfile.php:287
#: includes/Privacy.php:278
#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/Bank.php:83
#: includes/Withdraw/functions.php:297
#: includes/Withdraw/functions.php:298
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Routing Number"
msgstr ""

#: includes/Admin/UserProfile.php:293
msgid "Bank IBAN "
msgstr ""

#: includes/Admin/UserProfile.php:299
msgid "Bank Swift "
msgstr ""

#: includes/Admin/UserProfile.php:311
msgid "Enable Adding Products"
msgstr ""

#: includes/Admin/UserProfile.php:314
msgid "Enable or disable product adding capability"
msgstr ""

#: includes/Admin/UserProfile.php:319
msgid "Publishing"
msgstr ""

#: includes/Admin/UserProfile.php:324
msgid "Publish product directly"
msgstr ""

#: includes/Admin/UserProfile.php:327
msgid "Bypass pending, publish products directly"
msgstr ""

#: includes/Admin/UserProfile.php:332
msgid "Featured vendor"
msgstr ""

#: includes/Admin/UserProfile.php:337
msgid "Mark as featured vendor"
msgstr ""

#: includes/Admin/UserProfile.php:340
msgid "This vendor will be marked as a featured vendor."
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:110
msgid "ID"
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:111
msgid "Vendor ID"
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:112
#: includes/Privacy.php:178
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:117
#: templates/settings/store-form.php:115
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Store Name"
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:113
msgid "Withdraw Total"
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:115
#: templates/withdraw/approved-request-listing.php:17
#: templates/withdraw/cancelled-request-listing.php:17
#: templates/withdraw/pending-request-listing-dashboard.php:20
#: templates/withdraw/pending-request-listing.php:17
#: templates/withdraw/pending-request-listing.php:61
#: assets/js/frontend.js:172
#: assets/js/vue-admin.js:2
msgid "Method"
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:116
#: templates/my-orders.php:26
#: templates/orders/listing.php:35
#: templates/orders/listing.php:103
#: templates/orders/sub-order-related-order-meta-box-html.php:38
#: templates/products/products-listing-row.php:290
#: templates/products/products-listing.php:131
#: templates/reverse-withdrawal/transaction-listing.php:19
#: templates/sub-orders.php:46
#: templates/withdraw/approved-request-listing.php:20
#: templates/withdraw/cancelled-request-listing.php:20
#: templates/withdraw/pending-request-listing-dashboard.php:21
#: templates/withdraw/pending-request-listing.php:18
#: templates/withdraw/pending-request-listing.php:62
#: assets/js/frontend.js:172
#: assets/js/vue-admin.js:2
msgid "Date"
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:117
msgid "Bank Acc Name"
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:118
msgid "Bank Acc No."
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:119
msgid "Bank Acc Type"
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:120
#: includes/Privacy.php:276
#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/Bank.php:67
#: includes/Withdraw/functions.php:301
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Bank Name"
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:121
#: includes/Privacy.php:277
#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/Bank.php:75
#: includes/Withdraw/functions.php:305
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Bank Address"
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:122
msgid "Bank Declaration"
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:123
#: includes/Withdraw/functions.php:309
msgid "Bank IBAN"
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:124
msgid "Bank Routing No."
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:125
msgid "Bank SWIFT Code"
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:126
msgid "Paypal Email"
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:127
#: includes/Privacy.php:296
msgid "Skrill Email"
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:128
msgid "Custom Payment Method"
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:129
msgid "Custom Payment Value"
msgstr ""

#: includes/Admin/WithdrawLogExporter.php:130
#: templates/reverse-withdrawal/transaction-listing.php:21
#: templates/withdraw/cancelled-request-listing.php:21
#: assets/js/frontend.js:172
#: assets/js/vue-admin.js:2
msgid "Note"
msgstr ""

#: includes/Ajax.php:72
#: includes/Ajax.php:1058
#: includes/Ajax.php:1086
#: includes/Dashboard/Templates/Withdraw.php:121
#: includes/Withdraw/Hooks.php:163
#: includes/Withdraw/Hooks.php:238
msgid "You have no permission to do this action"
msgstr ""

#: includes/Ajax.php:94
msgid "Something wrong, please try again later"
msgstr ""

#: includes/Ajax.php:108
#: includes/Dashboard/Templates/Settings.php:310
#: includes/Dashboard/Templates/Settings.php:315
#: includes/Dashboard/Templates/Settings.php:325
#: includes/Dashboard/Templates/Settings.php:337
#: includes/Dashboard/Templates/Settings.php:349
#: includes/Dashboard/Templates/Settings.php:387
#: includes/Dashboard/Templates/Settings.php:424
#: includes/Dashboard/Templates/Settings.php:469
#: includes/Dashboard/Templates/Withdraw.php:116
#: includes/Withdraw/Hooks.php:159
#: includes/Withdraw/Hooks.php:234
msgid "Are you cheating?"
msgstr ""

#: includes/Ajax.php:161
#: includes/Ajax.php:196
msgid "You do not have sufficient permissions to access this page."
msgstr ""

#: includes/Ajax.php:165
#: includes/Ajax.php:200
msgid "You have taken too long. Please go back and retry."
msgstr ""

#: includes/Ajax.php:175
#: includes/Ajax.php:210
msgid "You do not have permission to change this order"
msgstr ""

#: includes/Ajax.php:288
msgid "You have no permission to manage this order"
msgstr ""

#: includes/Ajax.php:331
msgid "Please provide your name."
msgstr ""

#: includes/Ajax.php:336
msgid "Please provide your email."
msgstr ""

#: includes/Ajax.php:343
#: includes/template-tags.php:176
msgid "Something went wrong!"
msgstr ""

#: includes/Ajax.php:353
msgid "reCAPTCHA verification failed!"
msgstr ""

#: includes/Ajax.php:360
#: includes/REST/StoreController.php:838
msgid "Email sent successfully!"
msgstr ""

#: includes/Ajax.php:419
#: templates/orders/details.php:283
msgid "Delete note"
msgstr ""

#: includes/Ajax.php:449
msgid "Shipping provider: "
msgstr ""

#: includes/Ajax.php:449
msgid "Shipping number: "
msgstr ""

#: includes/Ajax.php:449
msgid "Shipped date: "
msgstr ""

#: includes/Ajax.php:489
#: includes/woo-views/html-product-download.php:14
#: assets/js/vue-admin.js:2
msgid "Delete"
msgstr ""

#: includes/Ajax.php:530
#: includes/Product/Hooks.php:59
msgid "Error: Nonce verification failed"
msgstr ""

#: includes/Ajax.php:618
msgid "Image could not be processed. Please go back and try again."
msgstr ""

#: includes/Ajax.php:978
msgid "Please Login to Continue"
msgstr ""

#: includes/Ajax.php:1000
msgid "Invalid username or password."
msgstr ""

#: includes/Ajax.php:1011
msgid "Wrong username or password."
msgstr ""

#: includes/Ajax.php:1040
msgid "User logged in successfully."
msgstr ""

#: includes/Ajax.php:1062
msgid "id param is required"
msgstr ""

#: includes/Analytics/VendorDashboardManager.php:85
msgid "Seller Totals."
msgstr ""

#: includes/Analytics/VendorDashboardManager.php:96
#: assets/js/vue-admin.js:2
msgid "Total Earning"
msgstr ""

#: includes/Analytics/VendorDashboardManager.php:97
msgid "Total Vendor Earning"
msgstr ""

#: includes/Analytics/VendorDashboardManager.php:106
msgid "Marketplace Discount"
msgstr ""

#: includes/Analytics/VendorDashboardManager.php:116
msgid "Store Discount"
msgstr ""

#: includes/Analytics/VendorDashboardManager.php:117
msgid "Total Vendor Discount"
msgstr ""

#: includes/Analytics/VendorDashboardManager.php:127
msgid "Marketplace Commission"
msgstr ""

#: includes/Analytics/VendorDashboardManager.php:128
msgid "Total Commission"
msgstr ""

#: includes/Assets.php:154
msgid "Could not find any vendor."
msgstr ""

#: includes/Assets.php:155
msgid "Searching vendors"
msgstr ""

#: includes/Assets.php:156
msgid "Search vendors"
msgstr ""

#: includes/Assets.php:157
msgid "Are you sure ?"
msgstr ""

#: includes/Assets.php:715
#: includes/Product/functions.php:504
#: templates/products/products-listing.php:109
#: assets/js/dokan-admin-notice.js:2
#: assets/js/dokan-promo-notice.js:2
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Are you sure?"
msgstr ""

#: includes/Assets.php:716
msgid "Something went wrong. Please try again."
msgstr ""

#: includes/Assets.php:730
msgid "Are you sure you want to revoke access to this download?"
msgstr ""

#: includes/Assets.php:731
msgid "Could not grant access - the user may already have permission for this file or billing email is not set. Ensure the billing email is set, and the order has been saved."
msgstr ""

#: includes/Assets.php:863
msgctxt "time constant"
msgid "am"
msgstr ""

#: includes/Assets.php:864
msgctxt "time constant"
msgid "pm"
msgstr ""

#: includes/Assets.php:865
msgctxt "time constant"
msgid "AM"
msgstr ""

#: includes/Assets.php:866
msgctxt "time constant"
msgid "PM"
msgstr ""

#: includes/Assets.php:867
msgctxt "time constant"
msgid "hr"
msgstr ""

#: includes/Assets.php:868
msgctxt "time constant"
msgid "hrs"
msgstr ""

#: includes/Assets.php:869
msgctxt "time constant"
msgid "mins"
msgstr ""

#: includes/Assets.php:872
#: templates/products/edit-product-single.php:381
#: templates/products/new-product.php:252
#: templates/products/tmpl-add-product-popup.php:87
#: assets/js/vue-admin.js:2
msgid "To"
msgstr ""

#: includes/Assets.php:874
#: templates/products/edit-product-single.php:374
#: templates/products/new-product.php:245
#: templates/products/tmpl-add-product-popup.php:80
msgid "From"
msgstr ""

#: includes/Assets.php:875
msgid " - "
msgstr ""

#: includes/Assets.php:876
msgid "W"
msgstr ""

#: includes/Assets.php:877
#: templates/orders/listing.php:20
#: templates/products/products-listing.php:108
#: templates/store-lists-filter.php:84
#: assets/js/vue-admin.js:2
msgid "Apply"
msgstr ""

#: includes/Assets.php:878
#: assets/js/components.js:172
#: assets/js/vue-admin.js:2
msgid "Clear"
msgstr ""

#: includes/Assets.php:879
#: includes/Withdraw/Hooks.php:84
msgid "Custom"
msgstr ""

#: includes/Assets.php:881
msgid "Su"
msgstr ""

#: includes/Assets.php:882
msgid "Mo"
msgstr ""

#: includes/Assets.php:883
msgid "Tu"
msgstr ""

#: includes/Assets.php:884
msgid "We"
msgstr ""

#: includes/Assets.php:885
msgid "Th"
msgstr ""

#: includes/Assets.php:886
msgid "Fr"
msgstr ""

#: includes/Assets.php:887
msgid "Sa"
msgstr ""

#: includes/Assets.php:890
msgid "January"
msgstr ""

#: includes/Assets.php:891
msgid "February"
msgstr ""

#: includes/Assets.php:892
msgid "March"
msgstr ""

#: includes/Assets.php:893
msgid "April"
msgstr ""

#: includes/Assets.php:894
msgid "May"
msgstr ""

#: includes/Assets.php:895
msgid "June"
msgstr ""

#: includes/Assets.php:896
msgid "July"
msgstr ""

#: includes/Assets.php:897
msgid "August"
msgstr ""

#: includes/Assets.php:898
msgid "September"
msgstr ""

#: includes/Assets.php:899
msgid "October"
msgstr ""

#: includes/Assets.php:900
msgid "November"
msgstr ""

#: includes/Assets.php:901
msgid "December"
msgstr ""

#: includes/Assets.php:905
#: includes/Assets.php:1189
#: includes/Product/functions.php:310
#: templates/my-orders.php:95
#: templates/orders/details.php:196
#: templates/products/edit-product-single.php:345
#: templates/products/new-product.php:232
#: templates/products/tmpl-add-product-popup.php:67
#: templates/settings/bank-payment-method-settings.php:203
#: templates/store-lists-filter.php:83
#: templates/withdraw/pending-request-listing-dashboard.php:24
#: templates/withdraw/pending-request-listing-dashboard.php:46
#: templates/withdraw/pending-request-listing.php:19
#: templates/withdraw/pending-request-listing.php:39
#: templates/withdraw/pending-request-listing.php:63
#: assets/js/components.js:172
#: assets/js/dokan-admin-notice.js:2
#: assets/js/dokan-intelligence.js:172
#: assets/js/dokan-promo-notice.js:2
#: assets/js/dokan-setup-wizard-commission.js:1
#: assets/js/frontend.js:172
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Cancel"
msgstr ""

#: includes/Assets.php:906
#: includes/Product/functions.php:321
#: templates/orders/details.php:347
#: templates/settings/store-form.php:38
#: assets/js/frontend.js:172
msgid "Close"
msgstr ""

#: includes/Assets.php:907
#: includes/Assets.php:1188
msgid "OK"
msgstr ""

#: includes/Assets.php:908
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:283
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:430
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:446
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:462
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:478
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:506
msgid "No"
msgstr ""

#: includes/Assets.php:909
msgid "Close this dialog"
msgstr ""

#: includes/Assets.php:935
msgid "This field is required"
msgstr ""

#: includes/Assets.php:936
msgid "Please fix this field."
msgstr ""

#: includes/Assets.php:937
msgid "Please enter a valid email address."
msgstr ""

#: includes/Assets.php:938
msgid "Please enter a valid URL."
msgstr ""

#: includes/Assets.php:939
msgid "Please enter a valid date."
msgstr ""

#: includes/Assets.php:940
msgid "Please enter a valid date (ISO)."
msgstr ""

#: includes/Assets.php:941
msgid "Please enter a valid number."
msgstr ""

#: includes/Assets.php:942
msgid "Please enter only digits."
msgstr ""

#: includes/Assets.php:943
msgid "Please enter a valid credit card number."
msgstr ""

#: includes/Assets.php:944
msgid "Please enter the same value again."
msgstr ""

#: includes/Assets.php:945
msgid "Please enter no more than {0} characters."
msgstr ""

#: includes/Assets.php:946
msgid "Please enter at least {0} characters."
msgstr ""

#: includes/Assets.php:947
msgid "Please enter a value between {0} and {1} characters long."
msgstr ""

#: includes/Assets.php:948
msgid "Please enter a value between {0} and {1}."
msgstr ""

#: includes/Assets.php:949
msgid "Please enter a value less than or equal to {0}."
msgstr ""

#: includes/Assets.php:950
msgid "Please enter a value greater than or equal to {0}."
msgstr ""

#: includes/Assets.php:1112
msgid "Upload featured image"
msgstr ""

#: includes/Assets.php:1113
msgid "Choose a file"
msgstr ""

#: includes/Assets.php:1114
msgid "Add Images to Product Gallery"
msgstr ""

#: includes/Assets.php:1115
msgid "Set featured image"
msgstr ""

#: includes/Assets.php:1116
#: includes/woo-views/html-product-download.php:8
msgid "Insert file URL"
msgstr ""

#: includes/Assets.php:1117
msgid "Add to gallery"
msgstr ""

#: includes/Assets.php:1118
msgid "Sorry, this attribute option already exists, Try a different one."
msgstr ""

#: includes/Assets.php:1119
msgid "Warning! This product will not have any variations if this option is not checked."
msgstr ""

#: includes/Assets.php:1120
msgid "Enter a name for the new attribute term:"
msgstr ""

#: includes/Assets.php:1121
msgid "Remove this attribute?"
msgstr ""

#. translators: %d: max linked variation.
#: includes/Assets.php:1130
msgid "Are you sure you want to link all variations? This will create a new variation for each and every possible combination of variation attributes (max %d per run)."
msgstr ""

#: includes/Assets.php:1131
msgid "Enter a value"
msgstr ""

#: includes/Assets.php:1132
msgid "Variation menu order (determines position in the list of variations)"
msgstr ""

#: includes/Assets.php:1133
msgid "Enter a value (fixed or %)"
msgstr ""

#: includes/Assets.php:1134
msgid "Are you sure you want to delete all variations? This cannot be undone."
msgstr ""

#: includes/Assets.php:1135
msgid "Last warning, are you sure?"
msgstr ""

#: includes/Assets.php:1136
msgid "Choose an image"
msgstr ""

#: includes/Assets.php:1137
msgid "Set variation image"
msgstr ""

#: includes/Assets.php:1138
msgid "variation added"
msgstr ""

#: includes/Assets.php:1139
msgid "variations added"
msgstr ""

#: includes/Assets.php:1140
msgid "No variations added"
msgstr ""

#: includes/Assets.php:1141
msgid "Are you sure you want to remove this variation?"
msgstr ""

#: includes/Assets.php:1142
msgid "Sale start date (YYYY-MM-DD format or leave blank)"
msgstr ""

#: includes/Assets.php:1143
msgid "Sale end date (YYYY-MM-DD format or leave blank)"
msgstr ""

#: includes/Assets.php:1144
msgid "Save changes before changing page?"
msgstr ""

#: includes/Assets.php:1145
msgid "%qty% variation"
msgstr ""

#: includes/Assets.php:1146
msgid "%qty% variations"
msgstr ""

#: includes/Assets.php:1147
msgid "No Result Found"
msgstr ""

#: includes/Assets.php:1148
msgid "Please insert value less than the regular price!"
msgstr ""

#. translators: %s: decimal
#: includes/Assets.php:1150
#: includes/Assets.php:1331
msgid "Please enter with one decimal point (%s) without thousand separators."
msgstr ""

#. translators: %s: price decimal separator
#: includes/Assets.php:1152
#: includes/Assets.php:1333
msgid "Please enter with one monetary decimal point (%s) without thousand separators and currency symbols."
msgstr ""

#: includes/Assets.php:1153
#: includes/Assets.php:1334
msgid "Please enter in country code with two capital letters."
msgstr ""

#: includes/Assets.php:1154
#: includes/Assets.php:1335
msgid "Please enter in a value less than the regular price."
msgstr ""

#: includes/Assets.php:1155
#: includes/Assets.php:1336
msgid "This product has produced sales and may be linked to existing orders. Are you sure you want to delete it?"
msgstr ""

#: includes/Assets.php:1156
#: includes/Assets.php:1337
msgid "This action cannot be reversed. Are you sure you wish to erase personal data from the selected orders?"
msgstr ""

#: includes/Assets.php:1166
msgid "Select and Crop"
msgstr ""

#: includes/Assets.php:1167
msgid "Choose Image"
msgstr ""

#: includes/Assets.php:1168
msgid "Product title is required"
msgstr ""

#: includes/Assets.php:1169
msgid "Product category is required"
msgstr ""

#: includes/Assets.php:1170
msgid "Product created successfully"
msgstr ""

#: includes/Assets.php:1175
msgid "One result is available, press enter to select it."
msgstr ""

#: includes/Assets.php:1176
msgid "%qty% results are available, use up and down arrow keys to navigate."
msgstr ""

#: includes/Assets.php:1177
msgid "No matches found"
msgstr ""

#: includes/Assets.php:1178
msgid "Loading failed"
msgstr ""

#: includes/Assets.php:1179
msgid "Please enter 1 or more characters"
msgstr ""

#: includes/Assets.php:1180
msgid "Please enter %qty% or more characters"
msgstr ""

#: includes/Assets.php:1181
msgid "Please delete 1 character"
msgstr ""

#: includes/Assets.php:1182
msgid "Please delete %qty% characters"
msgstr ""

#: includes/Assets.php:1183
msgid "You can only select 1 item"
msgstr ""

#: includes/Assets.php:1184
msgid "You can only select %qty% items"
msgstr ""

#: includes/Assets.php:1185
msgid "Loading more results&hellip;"
msgstr ""

#: includes/Assets.php:1186
msgid "Searching&hellip;"
msgstr ""

#: includes/Assets.php:1187
msgid "Calculating"
msgstr ""

#: includes/Assets.php:1190
msgid "Attribute Name"
msgstr ""

#: includes/Assets.php:1192
msgid "Are you sure? You have uploaded banner but didn't click the Update Settings button!"
msgstr ""

#: includes/Assets.php:1193
#: templates/settings/header.php:20
#: templates/settings/payment-manage.php:48
#: templates/settings/store-form.php:270
msgid "Update Settings"
msgstr ""

#: includes/Assets.php:1195
msgid "Please enter 3 or more characters"
msgstr ""

#: includes/BackgroundProcess/Manager.php:73
msgid "Dokan Variable Products Updated"
msgstr ""

#: includes/BackgroundProcess/Manager.php:74
msgid "Dokan variable products author IDs regenerated successfully!"
msgstr ""

#: includes/Blocks/ProductBlock.php:30
#: templates/products/inventory.php:32
msgid "In Stock"
msgstr ""

#: includes/Blocks/ProductBlock.php:31
#: templates/products/inventory.php:33
msgid "Out of Stock"
msgstr ""

#: includes/Blocks/ProductBlock.php:34
#: templates/products/inventory.php:74
msgid "Do not allow"
msgstr ""

#: includes/Blocks/ProductBlock.php:35
msgid "Allow, but notify customer"
msgstr ""

#: includes/Blocks/ProductBlock.php:36
#: templates/products/inventory.php:76
msgid "Allow"
msgstr ""

#: includes/Blocks/ProductBlock.php:40
#: templates/products/edit-product-single.php:411
#: templates/products/new-product.php:271
#: templates/products/tmpl-add-product-popup.php:18
msgid "Select tags/Add tags"
msgstr ""

#: includes/Blocks/ProductBlock.php:40
#: templates/products/edit-product-single.php:411
#: templates/products/new-product.php:271
#: templates/products/tmpl-add-product-popup.php:18
msgid "Select product tags"
msgstr ""

#: includes/CatalogMode/Admin/Settings.php:39
msgid "Product Catalog Mode"
msgstr ""

#: includes/CatalogMode/Admin/Settings.php:41
msgid "Control Catalog Mode Settings For Your Vendors"
msgstr ""

#: includes/CatalogMode/Admin/Settings.php:46
#: includes/CatalogMode/Dashboard/Settings.php:62
msgid "Remove Add to Cart Button"
msgstr ""

#: includes/CatalogMode/Admin/Settings.php:47
msgid "Check to remove Add to Cart option."
msgstr ""

#: includes/CatalogMode/Admin/Settings.php:53
#: includes/CatalogMode/Dashboard/Settings.php:74
msgid "Hide Product Price"
msgstr ""

#: includes/CatalogMode/Admin/Settings.php:54
msgid "Check to hide product price."
msgstr ""

#: includes/CatalogMode/Dashboard/ProductBulkEdit.php:47
msgid "Enable Catalog Mode"
msgstr ""

#: includes/CatalogMode/Dashboard/ProductBulkEdit.php:48
msgid "Disable Catalog Mode"
msgstr ""

#. translators: %d is product count.
#: includes/CatalogMode/Dashboard/ProductBulkEdit.php:133
msgid "%s product has been successfully updated."
msgid_plural "%s products have been successfully updated."
msgstr[0] ""
msgstr[1] ""

#: includes/CatalogMode/Dashboard/ProductBulkEdit.php:142
msgid "No product data has been updated."
msgstr ""

#: includes/CatalogMode/Dashboard/Settings.php:67
#: templates/products/catalog-mode-content.php:38
msgid "Check to remove Add to Cart option from your products."
msgstr ""

#: includes/CatalogMode/Dashboard/Settings.php:79
#: templates/products/catalog-mode-content.php:45
msgid "Check to hide product price from your products."
msgstr ""

#: includes/Commission.php:170
#: includes/REST/ProductBlockController.php:69
msgid "Product not found"
msgstr ""

#: includes/Commission.php:210
msgid "Order not found"
msgstr ""

#: includes/Commission.php:238
msgid "Commission calculation failed"
msgstr ""

#: includes/Commission.php:537
msgid "Combine"
msgstr ""

#: includes/Commission.php:538
#: assets/js/dokan-admin-dashboard.js:172
#: assets/js/dokan-setup-wizard-commission.js:1
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Percentage"
msgstr ""

#: includes/Commission.php:539
#: assets/js/dokan-admin-dashboard.js:172
#: assets/js/dokan-setup-wizard-commission.js:1
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Flat"
msgstr ""

#: includes/Commission/OrderCommission.php:66
msgid "Order is required for order commission calculation."
msgstr ""

#. translators: 1: Refund item ID, 2: Error message
#: includes/Commission/OrderCommission.php:150
msgid "Refund item ID %1$s error: %2$s"
msgstr ""

#: includes/Commission/OrderLineItemCommission.php:110
msgid "Order item is required for order item commission calculation."
msgstr ""

#: includes/Commission/OrderLineItemCommission.php:114
msgid "Order is required for order item commission calculation."
msgstr ""

#: includes/Commission/OrderLineItemCommission.php:240
msgid "Order item is required to get order item commission."
msgstr ""

#: includes/Commission/ProductCommission.php:61
msgid "Product ID or Total Amount with category ID is required."
msgstr ""

#: includes/Commission/ProductCommission.php:67
msgid "Total Amount must be greater than or equal to 0."
msgstr ""

#: includes/Commission/RecalculateCommissions.php:114
msgid "Order not editable"
msgstr ""

#: includes/Commission/RecalculateCommissions.php:187
#: includes/REST/OrderController.php:400
msgid "Invalid order"
msgstr ""

#: includes/Commission/RecalculateCommissions.php:192
msgid "Invalid coupon"
msgstr ""

#. translators: %s coupon code.
#: includes/Commission/RecalculateCommissions.php:198
msgid "Coupon removed: \"%s\"."
msgstr ""

#. translators: 1) page number
#: includes/Core.php:149
msgid "Page %1$s"
msgstr ""

#: includes/Customizer.php:72
msgid "Store Page"
msgstr ""

#: includes/Customizer.php:93
msgid "Page Layout"
msgstr ""

#: includes/Customizer.php:97
msgid "Left Sidebar"
msgstr ""

#: includes/Customizer.php:101
msgid "Right Sidebar"
msgstr ""

#: includes/Customizer.php:105
msgid "No Sidebar"
msgstr ""

#: includes/Customizer.php:127
msgid "Use theme sidebar"
msgstr ""

#: includes/Customizer.php:155
msgid "Header Template"
msgstr ""

#: includes/Customizer.php:159
msgid "Default Layout"
msgstr ""

#: includes/Customizer.php:163
msgid "Layout 2"
msgstr ""

#: includes/Customizer.php:167
msgid "Layout 3"
msgstr ""

#: includes/Customizer.php:171
msgid "Layout 4"
msgstr ""

#: includes/Customizer.php:185
#: includes/wc-template.php:40
msgid "Vendor Info"
msgstr ""

#: includes/Customizer.php:205
msgid "Hide email address"
msgstr ""

#: includes/Customizer.php:226
msgid "Hide phone number"
msgstr ""

#: includes/Customizer.php:247
msgid "Hide store address"
msgstr ""

#: includes/Customizer.php:278
msgid "Vendor Products"
msgstr ""

#: includes/Customizer.php:298
msgid "Hide product filter"
msgstr ""

#: includes/Customizer.php:313
msgid "Sidebar Widgets"
msgstr ""

#: includes/Customizer.php:335
msgid "Show store map"
msgstr ""

#: includes/Customizer.php:358
msgid "Show vendor contact form"
msgstr ""

#: includes/Customizer.php:380
msgid "Show store opening/closing Time"
msgstr ""

#: includes/Customizer.php:392
#: includes/Install/Installer.php:239
msgid "Store List"
msgstr ""

#: includes/Customizer.php:415
msgid "Store List Sorting"
msgstr ""

#: includes/Customizer.php:416
msgid "How should stores be sorted by default?"
msgstr ""

#: includes/Dashboard/Templates/Dashboard.php:114
#: templates/dashboard/orders-widget.php:38
#: assets/js/vue-admin.js:2
msgid "Pending"
msgstr ""

#: includes/Dashboard/Templates/Dashboard.php:124
#: includes/Order/functions.php:455
#: templates/dashboard/orders-widget.php:48
#: assets/js/frontend.js:172
#: assets/js/vue-admin.js:2
msgid "Cancelled"
msgstr ""

#: includes/Dashboard/Templates/Dashboard.php:129
#: includes/Order/functions.php:450
#: templates/dashboard/orders-widget.php:53
msgid "Refunded"
msgstr ""

#. translators: 1) Title 2) Notification Count
#. translators: 1: Order status label 2: Order count
#. translators: 1: currency name 2: currency code
#: includes/Dashboard/Templates/Main.php:72
#: includes/template-tags.php:277
#: templates/admin-setup-wizard/step-store-wc-fields.php:92
#: assets/js/dokan-admin-dashboard.js:172
msgid "%1$s (%2$s)"
msgstr ""

#: includes/Dashboard/Templates/Orders.php:75
msgid "No order data found with given order id."
msgstr ""

#: includes/Dashboard/Templates/Orders.php:92
msgid "You have no permission to view this order"
msgstr ""

#: includes/Dashboard/Templates/Orders.php:118
msgid "Nonce verification failed!"
msgstr ""

#: includes/Dashboard/Templates/Orders.php:141
#: includes/Dashboard/Templates/Products.php:267
#: assets/js/vue-admin.js:2
msgid "Bulk Actions"
msgstr ""

#: includes/Dashboard/Templates/Orders.php:142
msgid "Change status to on-hold"
msgstr ""

#: includes/Dashboard/Templates/Orders.php:143
msgid "Change status to processing"
msgstr ""

#: includes/Dashboard/Templates/Orders.php:144
msgid "Change status to completed"
msgstr ""

#: includes/Dashboard/Templates/Products.php:222
#: includes/Dashboard/Templates/Settings.php:138
#: includes/Shortcodes/Dashboard.php:30
#: templates/global/no-permission.php:54
msgid "You have no permission to view this page"
msgstr ""

#: includes/Dashboard/Templates/Products.php:268
#: includes/Product/functions.php:494
msgid "Delete Permanently"
msgstr ""

#: includes/Dashboard/Templates/Products.php:303
msgid "Details of your product ..."
msgstr ""

#: includes/Dashboard/Templates/Products.php:312
#: includes/Dashboard/Templates/Products.php:476
#: includes/Product/functions.php:33
msgid "Please enter product title"
msgstr ""

#: includes/Dashboard/Templates/Products.php:318
#: includes/Dashboard/Templates/Products.php:324
#: includes/Dashboard/Templates/Products.php:496
#: includes/Product/functions.php:39
#: includes/Product/functions.php:47
msgid "Please select a category"
msgstr ""

#: includes/Dashboard/Templates/Products.php:321
#: includes/Product/functions.php:43
msgid "Please select at least one category"
msgstr ""

#: includes/Dashboard/Templates/Products.php:481
msgid "No product found!"
msgstr ""

#: includes/Dashboard/Templates/Products.php:492
msgid "I swear this is not your product!"
msgstr ""

#. translators: %s: maximum tag length
#: includes/Dashboard/Templates/Products.php:513
#: includes/Product/functions.php:121
msgid "You can only select %s tags"
msgstr ""

#. translators: 1) reverse withdrawal balance, 2) navigation url, 3) due status, 4) will be taken actions
#: includes/Dashboard/Templates/ReverseWithdrawal.php:127
msgid "You have a reverse withdrawal balance of %1$s to be paid. Please <a href=\"%2$s\">pay</a> it before %3$s. Below actions will be taken after the billing period is over. %4$s"
msgstr ""

#. translators: 1) reverse withdrawal failed actions, 2) reverse withdrawal navigation url
#: includes/Dashboard/Templates/ReverseWithdrawal.php:157
msgid "Below actions have been taken due to unpaid reverse withdrawal balance: %1$s Kindly <a href=\"%2$s\">pay</a> your due to start selling again."
msgstr ""

#: includes/Dashboard/Templates/ReverseWithdrawal.php:281
msgid "Are you sure you want to pay this amount?"
msgstr ""

#: includes/Dashboard/Templates/ReverseWithdrawal.php:283
msgid "Something went wrong."
msgstr ""

#: includes/Dashboard/Templates/ReverseWithdrawal.php:284
msgid "Success."
msgstr ""

#: includes/Dashboard/Templates/Settings.php:63
msgid "Payment Method"
msgstr ""

#: includes/Dashboard/Templates/Settings.php:91
msgid "These are the withdraw methods available for you. Please update your payment information below to submit withdraw requests and get your store payments seamlessly."
msgstr ""

#: includes/Dashboard/Templates/Settings.php:201
msgid "No withdraw method is available. Please contact site admin."
msgstr ""

#: includes/Dashboard/Templates/Settings.php:281
msgid "Invalid withdraw method. Please contact site admin"
msgstr ""

#: includes/Dashboard/Templates/Settings.php:321
msgid "Pemission denied social"
msgstr ""

#: includes/Dashboard/Templates/Settings.php:333
#: includes/Dashboard/Templates/Settings.php:345
msgid "Pemission denied"
msgstr ""

#: includes/Dashboard/Templates/Settings.php:355
msgid "Failed to process data, invalid submission"
msgstr ""

#: includes/Dashboard/Templates/Settings.php:365
msgid "Your information has been saved successfully"
msgstr ""

#: includes/Dashboard/Templates/Settings.php:394
#: includes/Dashboard/Templates/Settings.php:437
msgid "Store type required"
msgstr ""

#: includes/Dashboard/Templates/Settings.php:402
#: includes/Dashboard/Templates/Settings.php:445
#: includes/Dashboard/Templates/Settings.php:480
#: includes/Dashboard/Templates/Settings.php:490
msgid "Invalid email"
msgstr ""

#: includes/Dashboard/Templates/Settings.php:432
msgid "Store name required"
msgstr ""

#: includes/Dashboard/Templates/Settings.php:506
msgid "Invalid Account Type"
msgstr ""

#: includes/Dashboard/Templates/Settings.php:511
msgid "You must attest that the bank account is yours."
msgstr ""

#: includes/Dashboard/Templates/Settings.php:680
msgid "Book"
msgstr ""

#: includes/Dashboard/Templates/Settings.php:681
msgid "Dress"
msgstr ""

#: includes/Dashboard/Templates/Settings.php:682
msgid "Electronic"
msgstr ""

#: includes/Dashboard/Templates/Settings.php:702
msgid "Bank Account Settings"
msgstr ""

#: includes/Dashboard/Templates/Settings.php:707
msgid "Paypal Settings"
msgstr ""

#: includes/Dashboard/Templates/Withdraw.php:129
msgid "Missing withdraw id."
msgstr ""

#: includes/Dashboard/Templates/Withdraw.php:138
#: templates/global/seller-warning.php:12
msgid "Your account is not enabled for selling, please contact the admin"
msgstr ""

#: includes/Dashboard/Templates/Withdraw.php:342
msgid "Your request has been cancelled successfully!"
msgstr ""

#: includes/Dashboard/Templates/Withdraw.php:346
msgid "Your request has been received successfully and being reviewed!"
msgstr ""

#: includes/Dashboard/Templates/Withdraw.php:350
msgid "Unknown error!"
msgstr ""

#: includes/Dashboard/Templates/Withdraw.php:391
#: includes/Dashboard/Templates/Withdraw.php:418
msgid "Sorry, no transactions were found!"
msgstr ""

#: includes/Dashboard/Templates/Withdraw.php:442
#: assets/js/frontend.js:172
msgid "You do not have any approved withdraw yet."
msgstr ""

#. translators: 1: Last formatted withdraw amount 2: Last formatted withdraw date 3: Last formatted withdraw method used.
#: includes/Dashboard/Templates/Withdraw.php:450
msgid "%1$s on %2$s to %3$s"
msgstr ""

#. translators: 1) Negative withdrawal balance amount
#: includes/Dashboard/Templates/Withdraw.php:489
msgid "You have already withdrawn %s. This amount will be deducted from your balance."
msgstr ""

#: includes/Dashboard/Templates/Withdraw.php:494
#: assets/js/frontend.js:172
msgid "You already have pending withdraw request(s). Please submit your request after approval or cancellation of your previous request."
msgstr ""

#: includes/Dashboard/Templates/Withdraw.php:500
#: assets/js/frontend.js:172
msgid "You don't have sufficient balance for a withdraw request!"
msgstr ""

#: includes/DummyData/Importer.php:192
msgid "A product with this SKU already exists in another vendor."
msgstr ""

#: includes/DummyData/Importer.php:204
msgid "A product with this SKU already exists."
msgstr ""

#: includes/DummyData/Importer.php:216
msgid "No matching product exists to update."
msgstr ""

#: includes/DummyData/Importer.php:304
msgid "Cleared all dummy data successfully."
msgstr ""

#: includes/Emails/ContactSeller.php:32
msgid "Dokan Contact Vendor"
msgstr ""

#: includes/Emails/ContactSeller.php:33
msgid "These emails are sent to a vendor who is contacted by customer via contact form widget "
msgstr ""

#: includes/Emails/ContactSeller.php:64
msgid "[{customer_name}] sent you a message from your store at - {site_title}"
msgstr ""

#: includes/Emails/ContactSeller.php:74
msgid "{customer_name} - Sent a message from {site_title}"
msgstr ""

#. translators: %s: list of placeholders
#: includes/Emails/ContactSeller.php:154
#: includes/Emails/NewProduct.php:175
#: includes/Emails/NewProductPending.php:173
#: includes/Emails/NewSeller.php:138
#: includes/Emails/ProductPublished.php:152
#: includes/Emails/ReverseWithdrawalInvoice.php:209
#: includes/Emails/VendorCompletedOrder.php:158
#: includes/Emails/VendorNewOrder.php:165
#: includes/Emails/VendorProductReview.php:188
#: includes/Emails/VendorWithdrawRequest.php:150
#: includes/Emails/WithdrawApproved.php:146
#: includes/Emails/WithdrawCancelled.php:150
msgid "Available placeholders: %s"
msgstr ""

#: includes/Emails/ContactSeller.php:157
#: includes/Emails/NewProduct.php:178
#: includes/Emails/NewProductPending.php:176
#: includes/Emails/NewSeller.php:141
#: includes/Emails/ProductPublished.php:155
#: includes/Emails/ReverseWithdrawalInvoice.php:213
#: includes/Emails/VendorCompletedOrder.php:161
#: includes/Emails/VendorNewOrder.php:168
#: includes/Emails/VendorProductReview.php:191
#: includes/Emails/VendorWithdrawRequest.php:153
#: includes/Emails/WithdrawApproved.php:149
#: includes/Emails/WithdrawCancelled.php:153
msgid "Enable/Disable"
msgstr ""

#: includes/Emails/ContactSeller.php:159
#: includes/Emails/NewProduct.php:180
#: includes/Emails/NewProductPending.php:178
#: includes/Emails/NewSeller.php:143
#: includes/Emails/ProductPublished.php:157
#: includes/Emails/ReverseWithdrawalInvoice.php:215
#: includes/Emails/VendorCompletedOrder.php:163
#: includes/Emails/VendorNewOrder.php:170
#: includes/Emails/VendorProductReview.php:193
#: includes/Emails/VendorWithdrawRequest.php:155
#: includes/Emails/WithdrawApproved.php:151
#: includes/Emails/WithdrawCancelled.php:155
msgid "Enable this email notification"
msgstr ""

#: includes/Emails/ContactSeller.php:164
#: includes/Emails/NewProduct.php:193
#: includes/Emails/NewProductPending.php:191
#: includes/Emails/NewSeller.php:156
#: includes/Emails/ProductPublished.php:162
#: includes/Emails/ReverseWithdrawalInvoice.php:219
#: includes/Emails/VendorCompletedOrder.php:167
#: includes/Emails/VendorNewOrder.php:174
#: includes/Emails/VendorProductReview.php:198
#: includes/Emails/VendorWithdrawRequest.php:168
#: includes/Emails/WithdrawApproved.php:155
#: includes/Emails/WithdrawCancelled.php:159
#: assets/js/vue-admin.js:2
msgid "Subject"
msgstr ""

#: includes/Emails/ContactSeller.php:172
#: includes/Emails/NewProduct.php:201
#: includes/Emails/NewProductPending.php:199
#: includes/Emails/NewSeller.php:164
#: includes/Emails/ProductPublished.php:170
#: includes/Emails/ReverseWithdrawalInvoice.php:227
#: includes/Emails/VendorCompletedOrder.php:175
#: includes/Emails/VendorNewOrder.php:182
#: includes/Emails/VendorProductReview.php:206
#: includes/Emails/VendorWithdrawRequest.php:176
#: includes/Emails/WithdrawApproved.php:163
#: includes/Emails/WithdrawCancelled.php:167
msgid "Email heading"
msgstr ""

#: includes/Emails/ContactSeller.php:180
#: includes/Emails/NewProduct.php:209
#: includes/Emails/NewProductPending.php:207
#: includes/Emails/NewSeller.php:172
#: includes/Emails/ProductPublished.php:178
#: includes/Emails/ReverseWithdrawalInvoice.php:235
#: includes/Emails/VendorCompletedOrder.php:183
#: includes/Emails/VendorNewOrder.php:190
#: includes/Emails/VendorProductReview.php:214
#: includes/Emails/VendorWithdrawRequest.php:184
#: includes/Emails/WithdrawApproved.php:171
#: includes/Emails/WithdrawCancelled.php:175
msgid "Additional content"
msgstr ""

#: includes/Emails/ContactSeller.php:181
#: includes/Emails/NewProduct.php:210
#: includes/Emails/NewProductPending.php:208
#: includes/Emails/NewSeller.php:173
#: includes/Emails/ProductPublished.php:179
#: includes/Emails/ReverseWithdrawalInvoice.php:236
#: includes/Emails/VendorCompletedOrder.php:184
#: includes/Emails/VendorNewOrder.php:191
#: includes/Emails/VendorProductReview.php:215
#: includes/Emails/VendorWithdrawRequest.php:185
#: includes/Emails/WithdrawApproved.php:172
#: includes/Emails/WithdrawCancelled.php:176
msgid "Text to appear below the main email content."
msgstr ""

#: includes/Emails/ContactSeller.php:183
#: includes/Emails/NewProduct.php:212
#: includes/Emails/NewProductPending.php:210
#: includes/Emails/NewSeller.php:175
#: includes/Emails/ProductPublished.php:181
#: includes/Emails/ReverseWithdrawalInvoice.php:238
#: includes/Emails/VendorCompletedOrder.php:186
#: includes/Emails/VendorNewOrder.php:193
#: includes/Emails/VendorProductReview.php:217
#: includes/Emails/VendorWithdrawRequest.php:187
#: includes/Emails/WithdrawApproved.php:174
#: includes/Emails/WithdrawCancelled.php:178
#: templates/orders/order-tax-html.php:10
msgid "N/A"
msgstr ""

#: includes/Emails/ContactSeller.php:189
#: includes/Emails/NewProduct.php:218
#: includes/Emails/NewProductPending.php:216
#: includes/Emails/NewSeller.php:181
#: includes/Emails/ProductPublished.php:187
#: includes/Emails/ReverseWithdrawalInvoice.php:244
#: includes/Emails/VendorCompletedOrder.php:192
#: includes/Emails/VendorNewOrder.php:199
#: includes/Emails/VendorProductReview.php:223
#: includes/Emails/VendorWithdrawRequest.php:193
#: includes/Emails/WithdrawApproved.php:180
#: includes/Emails/WithdrawCancelled.php:184
msgid "Email type"
msgstr ""

#: includes/Emails/ContactSeller.php:191
#: includes/Emails/NewProduct.php:220
#: includes/Emails/NewProductPending.php:218
#: includes/Emails/NewSeller.php:183
#: includes/Emails/ProductPublished.php:189
#: includes/Emails/ReverseWithdrawalInvoice.php:246
#: includes/Emails/VendorCompletedOrder.php:194
#: includes/Emails/VendorNewOrder.php:201
#: includes/Emails/VendorProductReview.php:225
#: includes/Emails/VendorWithdrawRequest.php:195
#: includes/Emails/WithdrawApproved.php:182
#: includes/Emails/WithdrawCancelled.php:186
msgid "Choose which format of email to send."
msgstr ""

#. translators: %1: from name, %2: from name
#: includes/Emails/Manager.php:207
msgid "\"%1$s\" sent you a message from your \"%2$s\" store"
msgstr ""

#. translators: %1: from name, %2: status
#: includes/Emails/Manager.php:259
msgid "[%1$s] Refund Request %2$s"
msgstr ""

#. translators: %s: from name
#: includes/Emails/Manager.php:293
msgid "[%s] New Refund Request"
msgstr ""

#. translators: %s: from name
#: includes/Emails/Manager.php:350
msgid "[%s] New Withdraw Request"
msgstr ""

#. translators: %s: from name
#: includes/Emails/Manager.php:373
msgid "[%s] Your Withdraw Request has been approved"
msgstr ""

#. translators: %s: from name
#: includes/Emails/Manager.php:397
msgid "[%s] Your Withdraw Request has been cancelled"
msgstr ""

#. translators: %s: from name
#: includes/Emails/Manager.php:436
msgid "[%s] New Vendor Registered"
msgstr ""

#. translators: %s: from name
#: includes/Emails/Manager.php:490
msgid "[%s] New Product Added"
msgstr ""

#. translators: %s: from name
#: includes/Emails/Manager.php:531
msgid "[%s] Your product has been approved!"
msgstr ""

#: includes/Emails/NewProduct.php:25
msgid "Dokan New Product"
msgstr ""

#: includes/Emails/NewProduct.php:26
msgid "New Product emails are sent to chosen recipient(s) when a new product is created by vendors."
msgstr ""

#: includes/Emails/NewProduct.php:60
msgid "[{site_title}] A New product is added by ({store_name}) - {product_title}"
msgstr ""

#: includes/Emails/NewProduct.php:70
msgid "New product added by Vendor {store_name}"
msgstr ""

#: includes/Emails/NewProduct.php:184
#: includes/Emails/NewProductPending.php:182
#: includes/Emails/NewSeller.php:147
#: includes/Emails/VendorWithdrawRequest.php:159
msgid "Recipient(s)"
msgstr ""

#. translators: 1) Email recipients
#: includes/Emails/NewProduct.php:187
#: includes/Emails/NewSeller.php:150
#: includes/Emails/VendorWithdrawRequest.php:162
msgid "Enter recipients (comma separated) for this email. Defaults to %s."
msgstr ""

#: includes/Emails/NewProductPending.php:26
msgid "Dokan New Pending Product"
msgstr ""

#: includes/Emails/NewProductPending.php:27
msgid "New Pending Product emails are sent to chosen recipient(s) when a new product is created by vendors."
msgstr ""

#: includes/Emails/NewProductPending.php:61
msgid "[{site_title}] A New product is pending from ({store_name}) - {product_title}"
msgstr ""

#: includes/Emails/NewProductPending.php:71
msgid "New pending product added by Vendor {store_name}"
msgstr ""

#. translators: 1) admin email address
#: includes/Emails/NewProductPending.php:185
msgid "Enter recipients (comma separated) for this email. Defaults to %1$s."
msgstr ""

#: includes/Emails/NewSeller.php:23
msgid "Dokan New Seller Registered"
msgstr ""

#: includes/Emails/NewSeller.php:24
msgid "These emails are sent to chosen recipient(s) when a new vendor registers in marketplace"
msgstr ""

#: includes/Emails/NewSeller.php:54
msgid "[{site_title}] A New vendor has registered"
msgstr ""

#: includes/Emails/NewSeller.php:64
msgid "New Vendor Registered - {seller_name}"
msgstr ""

#: includes/Emails/ProductPublished.php:25
msgid "Dokan Pending Product Published"
msgstr ""

#: includes/Emails/ProductPublished.php:26
msgid "These emails are sent to vendor of the product when a pending product is published."
msgstr ""

#: includes/Emails/ProductPublished.php:58
msgid "[{site_title}] Your product - {product_title} - is now published"
msgstr ""

#: includes/Emails/ProductPublished.php:68
msgid "{product_title} - is published"
msgstr ""

#: includes/Emails/ReverseWithdrawalInvoice.php:47
msgid "Dokan Reverse Withdrawal Invoice"
msgstr ""

#: includes/Emails/ReverseWithdrawalInvoice.php:48
msgid "These emails are sent to the vendor(s) who has a due reverse withdrawal payment based on settings."
msgstr ""

#: includes/Emails/ReverseWithdrawalInvoice.php:73
msgid "[{site_title}]: Your {month} invoice for store: {store_name} is available"
msgstr ""

#: includes/Emails/ReverseWithdrawalInvoice.php:84
msgid "Your latest {site_title} invoice"
msgstr ""

#: includes/Emails/ReverseWithdrawalInvoice.php:95
msgid "Thanks for using {site_url}!"
msgstr ""

#: includes/Emails/VendorCompletedOrder.php:27
msgid "Dokan Vendor Completed Order"
msgstr ""

#: includes/Emails/VendorCompletedOrder.php:28
msgid "Completed order emails are sent to the vendor when a order is completed."
msgstr ""

#: includes/Emails/VendorCompletedOrder.php:55
msgid "[{site_title}] Your customer order is now complete ({order_number}) - {order_date}"
msgstr ""

#: includes/Emails/VendorCompletedOrder.php:65
msgid "Complete Customer Order: #{order_number}"
msgstr ""

#: includes/Emails/VendorNewOrder.php:26
msgid "Dokan Vendor New Order"
msgstr ""

#: includes/Emails/VendorNewOrder.php:27
msgid "New order emails are sent to the vendor when a new order is received."
msgstr ""

#: includes/Emails/VendorNewOrder.php:62
msgid "[{site_title}] New customer order ({order_number}) - {order_date}"
msgstr ""

#: includes/Emails/VendorNewOrder.php:72
msgid "New Customer Order: #{order_number}"
msgstr ""

#: includes/Emails/VendorProductReview.php:36
msgid "Dokan Vendor Product Review"
msgstr ""

#: includes/Emails/VendorProductReview.php:37
msgid "After a product has been reviewed, an email is sent to the vendor containing information about the review. The email may include details such as the reviewer’s name, the product’s name and description, the review rating, and the review text. The email may also contain a link to the review page where the vendor can view the review and respond to it if necessary."
msgstr ""

#: includes/Emails/VendorProductReview.php:69
msgid "New Product Review Alert from {site_title}"
msgstr ""

#: includes/Emails/VendorProductReview.php:80
msgid "New Product Review Alert From Your Store: {store_name} at - {site_title}"
msgstr ""

#: includes/Emails/VendorWithdrawRequest.php:25
msgid "Dokan New Withdrawal Request"
msgstr ""

#: includes/Emails/VendorWithdrawRequest.php:26
msgid "These emails are sent to chosen recipient(s) when a vendor send request to withdraw"
msgstr ""

#: includes/Emails/VendorWithdrawRequest.php:59
msgid "[{site_title}] A New withdrawal request is made by {store_name}"
msgstr ""

#: includes/Emails/VendorWithdrawRequest.php:69
msgid "New Withdraw Request from - {store_name}"
msgstr ""

#: includes/Emails/WithdrawApproved.php:25
msgid "Dokan Withdraw Approved"
msgstr ""

#: includes/Emails/WithdrawApproved.php:26
msgid "These emails are sent to vendor when a vendor withdraw request is approved"
msgstr ""

#: includes/Emails/WithdrawApproved.php:59
msgid "[{site_title}] Your withdrawal request was approved"
msgstr ""

#: includes/Emails/WithdrawApproved.php:69
msgid "Withdrawal request for {amount} is approved"
msgstr ""

#: includes/Emails/WithdrawCancelled.php:25
msgid "Dokan Withdraw cancelled"
msgstr ""

#: includes/Emails/WithdrawCancelled.php:26
msgid "These emails are sent to vendor when a vendor withdraw request is cancelled"
msgstr ""

#: includes/Emails/WithdrawCancelled.php:60
msgid "[{site_title}] Your withdrawal request was cancelled"
msgstr ""

#: includes/Emails/WithdrawCancelled.php:70
msgid "Withdrawal request for {amount} is cancelled"
msgstr ""

#: includes/Exceptions/Handler.php:93
msgid "Follow Store  Module Deactivated"
msgstr ""

#: includes/Exceptions/Handler.php:94
msgid "The <strong>Follow Store </strong> module has been automatically deactivated due to incompatibility with the current versions of Dokan Lite and Dokan Pro. Please update Dokan Pro to the latest version and then reactivate the Follow Store  module."
msgstr ""

#: includes/Exceptions/Handler.php:99
msgid "Activate Module"
msgstr ""

#. translators: %s: Geteway fee
#: includes/Fees.php:105
msgid "Payment gateway processing fee %s"
msgstr ""

#: includes/Fees.php:161
#: includes/Fees.php:194
msgid "Please provide a valid order object."
msgstr ""

#: includes/Frontend/MyAccount/BecomeAVendor.php:82
#: includes/Frontend/MyAccount/BecomeAVendor.php:166
msgid "You need to login before applying for vendor."
msgstr ""

#: includes/Frontend/MyAccount/BecomeAVendor.php:88
#: includes/Frontend/MyAccount/BecomeAVendor.php:168
msgid "You are already a vendor."
msgstr ""

#: includes/Frontend/MyAccount/BecomeAVendor.php:96
msgid "Enter your first name."
msgstr ""

#: includes/Frontend/MyAccount/BecomeAVendor.php:97
msgid "Enter your shop name."
msgstr ""

#: includes/Frontend/MyAccount/BecomeAVendor.php:98
msgid "Enter your phone number."
msgstr ""

#: includes/Frontend/MyAccount/BecomeAVendor.php:170
msgid "You are an administrator. Please use dokan admin settings to enable your selling capabilities."
msgstr ""

#: includes/functions-dashboard-navigation.php:41
#: includes/functions.php:1214
#: templates/dashboard/products-widget.php:22
#: assets/js/vue-admin.js:2
msgid "Products"
msgstr ""

#: includes/functions-dashboard-navigation.php:48
#: includes/REST/AdminReportController.php:153
#: templates/dashboard/orders-widget.php:22
#: assets/js/dashboard-charts.js:1
#: assets/js/vue-admin.js:2
msgid "Orders"
msgstr ""

#: includes/functions-dashboard-navigation.php:225
#: includes/functions-dashboard-navigation.php:259
msgid "No Title"
msgstr ""

#: includes/functions-dashboard-navigation.php:302
#: templates/settings/header.php:14
#: templates/store-lists-loop.php:105
#: assets/js/components.js:172
#: assets/js/vue-admin.js:2
msgid "Visit Store"
msgstr ""

#: includes/functions-dashboard-navigation.php:303
#: templates/global/header-menu.php:54
msgid "Edit Account"
msgstr ""

#: includes/functions-dashboard-navigation.php:304
msgid "Log out"
msgstr ""

#. translators: 1) argument name, 2) argument value
#: includes/functions-rest-api.php:22
#: includes/functions-rest-api.php:61
msgid "%1$s is not of type %2$s"
msgstr ""

#. translators: 1) argument name
#: includes/functions-rest-api.php:28
#: includes/functions-rest-api.php:67
msgid "%s was not registered as a request argument."
msgstr ""

#. translators: 1) rest api endpoint key name
#: includes/functions-rest-api.php:37
#: includes/functions-rest-api.php:76
msgid "No store found with given store id"
msgstr ""

#: includes/functions.php:685
msgid "Online"
msgstr ""

#: includes/functions.php:686
#: includes/Order/functions.php:464
msgid "Draft"
msgstr ""

#: includes/functions.php:687
#: templates/dashboard/products-widget.php:47
#: templates/withdraw/pending-request-listing-dashboard.php:52
#: templates/withdraw/pending-request-listing.php:45
#: assets/js/frontend.js:172
msgid "Pending Review"
msgstr ""

#: includes/functions.php:688
#: templates/products/products-listing-row.php:329
msgid "Scheduled"
msgstr ""

#: includes/functions.php:757
msgid "Simple Product"
msgstr ""

#: includes/functions.php:758
#: assets/js/vue-admin.js:2
msgid "Variable Product"
msgstr ""

#: includes/functions.php:759
msgid "Grouped Product"
msgstr ""

#: includes/functions.php:760
msgid "External/Affiliate Product"
msgstr ""

#: includes/functions.php:998
msgid "Author"
msgstr ""

#: includes/functions.php:1218
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:518
#: templates/settings/store-form.php:195
msgid "Terms and Conditions"
msgstr ""

#. translators: 1) bank account name
#: includes/functions.php:1258
#: assets/js/vue-admin.js:2
msgid "Account Name: %s"
msgstr ""

#. translators: 1) bank account number
#: includes/functions.php:1263
#: assets/js/vue-admin.js:2
msgid "Account Number: %s"
msgstr ""

#. translators: 1) bank name
#: includes/functions.php:1268
#: assets/js/vue-admin.js:2
msgid "Bank Name: %s"
msgstr ""

#. translators: 1)  bank address
#: includes/functions.php:1273
msgid "Address: %s"
msgstr ""

#. translators: 1) bank routing number
#: includes/functions.php:1278
#: assets/js/vue-admin.js:2
msgid "Routing Number: %s"
msgstr ""

#. translators: 1) bank iban
#: includes/functions.php:1283
#: assets/js/vue-admin.js:2
msgid "IBAN: %s"
msgstr ""

#. translators: 1) bank swift
#: includes/functions.php:1288
msgid "SWIFT: %s"
msgstr ""

#: includes/functions.php:1640
msgid "Date is not valid"
msgstr ""

#: includes/functions.php:2012
msgid "- Select a location -"
msgstr ""

#: includes/functions.php:2016
#: includes/functions.php:2040
msgid "Everywhere Else"
msgstr ""

#: includes/functions.php:2036
msgid "- Select a State -"
msgstr ""

#: includes/functions.php:2059
msgid "Ready to ship in..."
msgstr ""

#: includes/functions.php:2060
msgid "1 business day"
msgstr ""

#: includes/functions.php:2061
msgid "1-2 business days"
msgstr ""

#: includes/functions.php:2062
msgid "1-3 business days"
msgstr ""

#: includes/functions.php:2063
msgid "3-5 business days"
msgstr ""

#: includes/functions.php:2064
msgid "1-2 weeks"
msgstr ""

#: includes/functions.php:2065
msgid "2-3 weeks"
msgstr ""

#: includes/functions.php:2066
msgid "3-4 weeks"
msgstr ""

#: includes/functions.php:2067
msgid "4-6 weeks"
msgstr ""

#: includes/functions.php:2068
msgid "6-8 weeks"
msgstr ""

#: includes/functions.php:2168
msgid "All dates"
msgstr ""

#. translators: 1: month name, 2: 4-digit year
#: includes/functions.php:2183
#: includes/REST/ProductControllerV2.php:218
msgid "%1$s %2$d"
msgstr ""

#: includes/functions.php:2198
#: includes/REST/ProductControllerV2.php:256
#: templates/products/edit-product-single.php:271
#: templates/products/products-listing-row.php:247
#: templates/products/products-listing.php:139
msgid "Simple"
msgstr ""

#: includes/functions.php:2283
#: includes/Privacy.php:201
#: assets/js/vue-bootstrap.js:2
msgid "Facebook"
msgstr ""

#: includes/functions.php:2287
#: includes/Privacy.php:202
#: assets/js/vue-bootstrap.js:2
msgid "Twitter"
msgstr ""

#: includes/functions.php:2291
#: includes/Privacy.php:203
#: assets/js/vue-bootstrap.js:2
msgid "Pinterest"
msgstr ""

#: includes/functions.php:2295
msgid "LinkedIn"
msgstr ""

#: includes/functions.php:2299
#: includes/Privacy.php:205
#: assets/js/vue-bootstrap.js:2
msgid "Youtube"
msgstr ""

#: includes/functions.php:2303
#: includes/Privacy.php:206
#: assets/js/vue-bootstrap.js:2
msgid "Instagram"
msgstr ""

#: includes/functions.php:2307
#: includes/Privacy.php:207
#: assets/js/vue-bootstrap.js:2
msgid "Flickr"
msgstr ""

#: includes/functions.php:2311
msgid "Threads"
msgstr ""

#: includes/functions.php:2575
msgid "Dokan Store Sidebar"
msgstr ""

#: includes/functions.php:2704
msgid "View sales overview"
msgstr ""

#: includes/functions.php:2705
msgid "View sales report chart"
msgstr ""

#: includes/functions.php:2706
msgid "View announcement"
msgstr ""

#: includes/functions.php:2707
msgid "View order report"
msgstr ""

#: includes/functions.php:2708
msgid "View review report"
msgstr ""

#: includes/functions.php:2709
msgid "View product status report"
msgstr ""

#: includes/functions.php:2712
msgid "View overview report"
msgstr ""

#: includes/functions.php:2713
msgid "View daily sales report"
msgstr ""

#: includes/functions.php:2714
msgid "View top selling report"
msgstr ""

#: includes/functions.php:2715
msgid "View top earning report"
msgstr ""

#: includes/functions.php:2716
msgid "View statement report"
msgstr ""

#: includes/functions.php:2719
msgid "View order"
msgstr ""

#: includes/functions.php:2720
msgid "Manage order"
msgstr ""

#: includes/functions.php:2721
msgid "Manage order note"
msgstr ""

#: includes/functions.php:2722
msgid "Manage refund"
msgstr ""

#: includes/functions.php:2723
msgid "Export order"
msgstr ""

#: includes/functions.php:2726
msgid "Add coupon"
msgstr ""

#: includes/functions.php:2727
msgid "Edit coupon"
msgstr ""

#: includes/functions.php:2728
msgid "Delete coupon"
msgstr ""

#: includes/functions.php:2731
msgid "View reviews"
msgstr ""

#: includes/functions.php:2732
#: assets/js/vue-admin.js:2
msgid "Manage reviews"
msgstr ""

#: includes/functions.php:2736
msgid "Manage withdraw"
msgstr ""

#: includes/functions.php:2739
msgid "Add product"
msgstr ""

#: includes/functions.php:2740
msgid "Edit product"
msgstr ""

#: includes/functions.php:2741
msgid "Delete product"
msgstr ""

#: includes/functions.php:2742
msgid "View product"
msgstr ""

#: includes/functions.php:2743
msgid "Duplicate product"
msgstr ""

#: includes/functions.php:2744
msgid "Import product"
msgstr ""

#: includes/functions.php:2745
msgid "Export product"
msgstr ""

#: includes/functions.php:2748
msgid "View overview menu"
msgstr ""

#: includes/functions.php:2749
msgid "View product menu"
msgstr ""

#: includes/functions.php:2750
msgid "View order menu"
msgstr ""

#: includes/functions.php:2751
msgid "View coupon menu"
msgstr ""

#: includes/functions.php:2752
msgid "View report menu"
msgstr ""

#: includes/functions.php:2753
msgid "View review menu"
msgstr ""

#: includes/functions.php:2754
msgid "View withdraw menu"
msgstr ""

#: includes/functions.php:2755
msgid "View store settings menu"
msgstr ""

#: includes/functions.php:2756
msgid "View payment settings menu"
msgstr ""

#: includes/functions.php:2757
msgid "View shipping settings menu"
msgstr ""

#: includes/functions.php:2758
msgid "View social settings menu"
msgstr ""

#: includes/functions.php:2759
msgid "View seo settings menu"
msgstr ""

#: includes/functions.php:2778
#: assets/js/vendor-dashboard/reports/index.js:2
#: assets/js/vue-admin.js:2
msgid "Overview"
msgstr ""

#: includes/functions.php:2779
msgid "Report"
msgstr ""

#: includes/functions.php:2780
#: templates/dashboard/big-counter-widget.php:29
#: templates/my-orders.php:25
#: templates/orders/details.php:19
#: templates/orders/listing.php:30
#: templates/orders/listing.php:57
#: templates/orders/sub-order-related-order-meta-box-html.php:37
#: templates/sub-orders.php:45
#: assets/js/vue-admin.js:2
msgid "Order"
msgstr ""

#: includes/functions.php:2781
msgid "Coupon"
msgstr ""

#: includes/functions.php:2782
msgid "Review"
msgstr ""

#: includes/functions.php:2784
#: templates/emails/vendor-completed-order.php:54
#: templates/emails/vendor-new-order.php:53
#: assets/js/dashboard-charts.js:1
#: assets/js/vue-admin.js:2
msgid "Product"
msgstr ""

#: includes/functions.php:2785
msgid "Menu"
msgstr ""

#: includes/functions.php:2914
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:350
msgid "Sunday"
msgstr ""

#: includes/functions.php:2915
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:302
msgid "Monday"
msgstr ""

#: includes/functions.php:2916
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:310
msgid "Tuesday"
msgstr ""

#: includes/functions.php:2917
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:318
msgid "Wednesday"
msgstr ""

#: includes/functions.php:2918
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:326
msgid "Thursday"
msgstr ""

#: includes/functions.php:2919
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:334
msgid "Friday"
msgstr ""

#: includes/functions.php:2920
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:342
msgid "Saturday"
msgstr ""

#: includes/functions.php:2925
msgid "Sun"
msgstr ""

#: includes/functions.php:2926
msgid "Mon"
msgstr ""

#: includes/functions.php:2927
msgid "Tue"
msgstr ""

#: includes/functions.php:2928
msgid "Wed"
msgstr ""

#: includes/functions.php:2929
msgid "Thu"
msgstr ""

#: includes/functions.php:2930
msgid "Fri"
msgstr ""

#: includes/functions.php:2931
msgid "Sat"
msgstr ""

#: includes/functions.php:3227
msgid "privacy policy"
msgstr ""

#. translators: 1) plugin slug
#: includes/functions.php:3449
msgid "Unable to fetch plugin information from wordpress.org for %s."
msgstr ""

#. translators: 1) plugin slug
#: includes/functions.php:3462
msgid "Unable to install %s from wordpress.org"
msgstr ""

#: includes/Intelligence/Admin/Settings.php:26
msgid "AI Assist"
msgstr ""

#: includes/Intelligence/Admin/Settings.php:28
msgid "Dokan AI Assist"
msgstr ""

#: includes/Intelligence/Admin/Settings.php:30
msgid "AI Assist Settings"
msgstr ""

#: includes/Intelligence/Admin/Settings.php:31
msgid "Set up AI to elevate your platform with enhanced capabilities."
msgstr ""

#: includes/Intelligence/Admin/Settings.php:47
msgid "Engine"
msgstr ""

#: includes/Intelligence/Admin/Settings.php:50
msgid "Select which AI provider to use for generating content."
msgstr ""

#: includes/Intelligence/Admin/Settings.php:56
msgid "Gemini API Key"
msgstr ""

#. translators: 1: OpenAi Link
#: includes/Intelligence/Admin/Settings.php:59
msgid "You can get your API Keys in your <a href=\"%s\" target=\"_blank\"> AI Studio Account.</a>"
msgstr ""

#: includes/Intelligence/Admin/Settings.php:68
#: includes/Intelligence/Admin/Settings.php:84
msgid "Your API key provides secure access to the AI service. Usage costs will be charged to the connected account."
msgstr ""

#: includes/Intelligence/Admin/Settings.php:72
msgid "OpenAI API Key"
msgstr ""

#. translators: 1: OpenAi Link
#: includes/Intelligence/Admin/Settings.php:75
msgid "You can get your API Keys in your <a href=\"%s\" target=\"_blank\">OpenAI Account.</a>"
msgstr ""

#: includes/Intelligence/Admin/Settings.php:88
#: includes/Intelligence/Admin/Settings.php:102
msgid "Model"
msgstr ""

#: includes/Intelligence/Admin/Settings.php:91
#: includes/Intelligence/Admin/Settings.php:105
msgid "More advanced models provide higher quality output but may cost more per generation."
msgstr ""

#: includes/Intelligence/Manager.php:22
msgid "OpenAI"
msgstr ""

#: includes/Intelligence/Manager.php:23
msgid "Gemini"
msgstr ""

#: includes/Intelligence/REST/AIRequestController.php:57
msgid "Prompt to process"
msgstr ""

#: includes/Intelligence/REST/AIRequestController.php:62
msgid "Optional data payload"
msgstr ""

#: includes/Intelligence/REST/AIRequestController.php:87
msgid "Something went wrong in the configuration. Kindly reach out to Marketplace Owner"
msgstr ""

#: includes/Intelligence/REST/AIRequestController.php:93
msgid "Service is not available due to some reason. Kindly reach out to Marketplace Owner"
msgstr ""

#: includes/Intelligence/Services/BaseAIService.php:74
msgid "API key is not configured"
msgstr ""

#: includes/Intelligence/Services/ChatgptResponseService.php:96
msgid "GPT-3.5 Turbo"
msgstr ""

#: includes/Intelligence/Services/ChatgptResponseService.php:97
msgid "GPT-4o Mini"
msgstr ""

#: includes/Intelligence/Services/ChatgptResponseService.php:98
msgid "GPT-4o"
msgstr ""

#: includes/Intelligence/Services/ChatgptResponseService.php:99
msgid "GPT-4 Turbo"
msgstr ""

#: includes/Intelligence/Services/ChatgptResponseService.php:100
msgid "ChatGPT 4o Latest"
msgstr ""

#. translators: %1$s: class name, %2$s: interface name
#: includes/Intelligence/Services/EngineFactory.php:34
msgid "Unsupported service type provided: %1$s must be an instance of %2$s"
msgstr ""

#: includes/Intelligence/Services/GeminiResponseService.php:115
msgid "Gemini 1.5 Flash"
msgstr ""

#: includes/Intelligence/Services/GeminiResponseService.php:116
msgid "Gemini 2.0 Flash"
msgstr ""

#: includes/Intelligence/Services/GeminiResponseService.php:117
msgid "Gemini 2.0 Flash-Lite"
msgstr ""

#: includes/Intelligence/Utils/AISupportedFields.php:11
msgid "Generate an SEO-friendly one line title for"
msgstr ""

#: includes/Intelligence/Utils/AISupportedFields.php:12
msgid "Craft Your Title"
msgstr ""

#: includes/Intelligence/Utils/AISupportedFields.php:17
msgid "Refine the following product description for "
msgstr ""

#: includes/Intelligence/Utils/AISupportedFields.php:18
msgid "Craft Your Short Description"
msgstr ""

#: includes/Intelligence/Utils/AISupportedFields.php:23
msgid "Refine the following product description"
msgstr ""

#: includes/Intelligence/Utils/AISupportedFields.php:24
msgid "Craft Your Description"
msgstr ""

#: includes/Order/Admin/Hooks.php:98
#: assets/js/vue-admin.js:2
msgid "Actions"
msgstr ""

#: includes/Order/Admin/Hooks.php:99
msgid "Sub Order"
msgstr ""

#: includes/Order/Admin/Hooks.php:138
msgid "&nbsp;Sub Order of"
msgstr ""

#: includes/Order/Admin/Hooks.php:146
msgid "Show Sub-Orders"
msgstr ""

#: includes/Order/Admin/Hooks.php:146
msgid "Hide Sub-Orders"
msgstr ""

#: includes/Order/Admin/Hooks.php:159
#: templates/orders/sub-order-related-order-meta-box-html.php:96
#: assets/js/vue-admin.js:2
msgid "(no name)"
msgstr ""

#: includes/Order/Admin/Hooks.php:545
msgid "Toggle Sub-orders"
msgstr ""

#: includes/Order/Admin/Hooks.php:571
msgid "Commissions"
msgstr ""

#: includes/Order/Admin/Hooks.php:581
msgid "Sub orders"
msgstr ""

#: includes/Order/Admin/Hooks.php:581
msgid "Related orders"
msgstr ""

#: includes/Order/EmailHooks.php:145
msgid "Your {site_title} order receipt from {order_date}"
msgstr ""

#: includes/Order/EmailHooks.php:146
msgid "Your {site_title} order from {order_date} is complete"
msgstr ""

#: includes/Order/functions.php:435
msgid "Pending Payment"
msgstr ""

#: includes/Order/functions.php:460
msgid "Failed"
msgstr ""

#: includes/Order/functions.php:647
msgid "Order No"
msgstr ""

#: includes/Order/functions.php:648
#: templates/orders/details.php:19
msgid "Order Items"
msgstr ""

#: includes/Order/functions.php:649
msgid "Shipping method"
msgstr ""

#: includes/Order/functions.php:650
msgid "Shipping Cost"
msgstr ""

#: includes/Order/functions.php:651
msgid "Payment method"
msgstr ""

#: includes/Order/functions.php:652
#: templates/orders/listing.php:31
#: templates/orders/listing.php:77
msgid "Order Total"
msgstr ""

#: includes/Order/functions.php:653
msgid "Earnings"
msgstr ""

#: includes/Order/functions.php:654
#: includes/REST/OrderController.php:107
msgid "Order Status"
msgstr ""

#: includes/Order/functions.php:655
msgid "Order Date"
msgstr ""

#: includes/Order/functions.php:656
msgid "Billing Company"
msgstr ""

#: includes/Order/functions.php:657
msgid "Billing First Name"
msgstr ""

#: includes/Order/functions.php:658
msgid "Billing Last Name"
msgstr ""

#: includes/Order/functions.php:659
msgid "Billing Full Name"
msgstr ""

#: includes/Order/functions.php:660
msgid "Billing Email"
msgstr ""

#: includes/Order/functions.php:661
msgid "Billing Phone"
msgstr ""

#: includes/Order/functions.php:662
msgid "Billing Address 1"
msgstr ""

#: includes/Order/functions.php:663
msgid "Billing Address 2"
msgstr ""

#: includes/Order/functions.php:664
msgid "Billing City"
msgstr ""

#: includes/Order/functions.php:665
msgid "Billing State"
msgstr ""

#: includes/Order/functions.php:666
msgid "Billing Postcode"
msgstr ""

#: includes/Order/functions.php:667
msgid "Billing Country"
msgstr ""

#: includes/Order/functions.php:668
msgid "Shipping Company"
msgstr ""

#: includes/Order/functions.php:669
msgid "Shipping First Name"
msgstr ""

#: includes/Order/functions.php:670
msgid "Shipping Last Name"
msgstr ""

#: includes/Order/functions.php:671
msgid "Shipping Full Name"
msgstr ""

#: includes/Order/functions.php:672
msgid "Shipping Address 1"
msgstr ""

#: includes/Order/functions.php:673
msgid "Shipping Address 2"
msgstr ""

#: includes/Order/functions.php:674
msgid "Shipping City"
msgstr ""

#: includes/Order/functions.php:675
msgid "Shipping State"
msgstr ""

#: includes/Order/functions.php:676
msgid "Shipping Postcode"
msgstr ""

#: includes/Order/functions.php:677
msgid "Shipping Country"
msgstr ""

#: includes/Order/functions.php:678
msgid "Customer IP"
msgstr ""

#: includes/Order/functions.php:679
msgid "Customer Note"
msgstr ""

#: includes/Order/Hooks.php:371
msgid "Marked as completed because it contains digital products only."
msgstr ""

#: includes/Order/Hooks.php:385
msgid "Mark parent order completed when all child orders are completed."
msgstr ""

#: includes/Order/Hooks.php:436
msgid "This coupon is invalid for multiple vendors."
msgstr ""

#. translators: %s item name.
#: includes/Order/Hooks.php:484
msgid "Unable to restore stock for item %s."
msgstr ""

#. translators: 1: Order ID, 2: Refund ID, 3: Refund Amount
#: includes/Order/RefundHandler.php:192
msgid "Dokan refund adjustment error: Seller not found, Order ID: %1$d, Refund ID: %2$d, Refund Amount: %3$f "
msgstr ""

#: includes/Order/RefundHandler.php:210
msgid "Refunded by Dokan"
msgstr ""

#: includes/Privacy.php:33
#: includes/Privacy.php:36
#: includes/Privacy.php:147
msgid "Vendor Data"
msgstr ""

#: includes/Privacy.php:51
msgid "This sample privacy policy includes the basics around what personal data your multivendor store may be collecting, storing and sharing, as well as who may have access to that data. Depending on what settings are enabled and which additional plugins are used, the specific information shared by your store will vary. We recommend consulting with a lawyer when deciding what information to disclose on your privacy policy."
msgstr ""

#: includes/Privacy.php:53
msgid "We collect information about you during the checkout process on our store."
msgstr ""

#: includes/Privacy.php:54
msgid "What we collect and store"
msgstr ""

#: includes/Privacy.php:55
msgid "While you visit our site, we’ll track:"
msgstr ""

#: includes/Privacy.php:57
msgid "Stores you’ve viewed: we’ll use this to, for example, show you vendor stores you’ve recently viewed"
msgstr ""

#: includes/Privacy.php:58
msgid "Products you’ve viewed: we’ll use this to, for example, show you products you’ve recently viewed"
msgstr ""

#: includes/Privacy.php:59
msgid "Location, IP address and browser type: we’ll use this for purposes like estimating taxes and shipping"
msgstr ""

#: includes/Privacy.php:60
msgid "Shipping address: we’ll ask you to enter this so we can, for instance, estimate shipping before you place an order, and send you the order!"
msgstr ""

#: includes/Privacy.php:62
msgid "We’ll also use cookies to keep track of cart contents while you’re browsing our site."
msgstr ""

#: includes/Privacy.php:63
msgid "Note: you may want to further detail your cookie policy, and link to that section from here."
msgstr ""

#: includes/Privacy.php:64
msgid "When you purchase from us, we’ll ask you to provide information including your name, billing address, shipping address, email address, phone number, credit card/payment details and optional account information like username and password. We’ll use this information for purposes, such as, to:"
msgstr ""

#: includes/Privacy.php:66
msgid "Send you information about your account and order"
msgstr ""

#: includes/Privacy.php:67
msgid "Respond to your requests, including refunds and complaints"
msgstr ""

#: includes/Privacy.php:68
msgid "Process payments and prevent fraud"
msgstr ""

#: includes/Privacy.php:69
msgid "Set up your account for our store"
msgstr ""

#: includes/Privacy.php:70
msgid "Comply with any legal obligations we have, such as calculating taxes"
msgstr ""

#: includes/Privacy.php:71
msgid "Improve our store offerings"
msgstr ""

#: includes/Privacy.php:72
msgid "Send you marketing messages, if you choose to receive them"
msgstr ""

#: includes/Privacy.php:74
msgid "If you create an account, we will store your name, address, email and phone number, which will be used to populate the checkout for future orders."
msgstr ""

#: includes/Privacy.php:75
msgid "We generally store information about you for as long as we need the information for the purposes for which we collect and use it, and we are not legally required to continue to keep it. For example, we will store order information for XXX years for tax and accounting purposes. This includes your name, email address and billing and shipping addresses."
msgstr ""

#: includes/Privacy.php:76
msgid "We will also store comments or reviews, if you choose to leave them."
msgstr ""

#: includes/Privacy.php:77
msgid "Who on our team has access"
msgstr ""

#: includes/Privacy.php:78
msgid "Members of our team have access to the information you provide us. For example, both Administrators and Shop Managers can access:"
msgstr ""

#: includes/Privacy.php:80
msgid "Order information like what was purchased, when it was purchased and where it should be sent, and"
msgstr ""

#: includes/Privacy.php:81
msgid "Customer information like your name, email address, and billing and shipping information."
msgstr ""

#: includes/Privacy.php:83
msgid "Our team members have access to this information to help fulfill orders, process refunds and support you."
msgstr ""

#: includes/Privacy.php:84
msgid "What we share with others"
msgstr ""

#: includes/Privacy.php:85
msgid "In this section you should list who you’re sharing data with, and for what purpose. This could include, but may not be limited to, analytics, marketing, payment gateways, shipping providers, and third party embeds."
msgstr ""

#: includes/Privacy.php:86
msgid "We share information with third parties who help us provide our orders and store services to you; for example --"
msgstr ""

#: includes/Privacy.php:87
msgid "Payments"
msgstr ""

#: includes/Privacy.php:88
msgid "In this subsection you should list which third party payment processors you’re using to take payments on your store since these may handle customer data. We’ve included PayPal as an example, but you should remove this if you’re not using PayPal."
msgstr ""

#: includes/Privacy.php:89
msgid "We accept payments through PayPal. When processing payments, some of your data will be passed to PayPal, including information required to process or support the payment, such as the purchase total and billing information."
msgstr ""

#: includes/Privacy.php:90
msgid "Please see the <a href=\"https://www.paypal.com/us/webapps/mpp/ua/privacy-full\">PayPal Privacy Policy</a> for more details."
msgstr ""

#: includes/Privacy.php:92
msgid "Dokan has premium modules that perform specific and special purpose tasks. Each of the modules collect additional information. Also third party extensions and integrations collect data that is applicable to the each of their individual privacy policy."
msgstr ""

#: includes/Privacy.php:148
msgid "Dokan vendor personal data."
msgstr ""

#: includes/Privacy.php:179
msgid "Social"
msgstr ""

#: includes/Privacy.php:180
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:165
#: assets/js/vue-admin.js:2
msgid "Phone"
msgstr ""

#: includes/Privacy.php:181
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:175
#: templates/admin-setup-wizard/step-store-wc-fields.php:33
#: templates/maps/mapbox-with-search.php:5
#: templates/settings/address-form.php:26
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Address"
msgstr ""

#: includes/Privacy.php:182
msgid "GEO Locations"
msgstr ""

#: includes/Privacy.php:183
msgid "Banner Url"
msgstr ""

#: includes/Privacy.php:184
msgid "Gravatar Url"
msgstr ""

#: includes/Privacy.php:204
#: assets/js/vue-bootstrap.js:2
msgid "Linkedin"
msgstr ""

#: includes/Privacy.php:224
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:202
#: includes/Vendor/SetupWizard.php:243
#: templates/admin-setup-wizard/step-store-wc-fields.php:51
#: templates/settings/address-form.php:75
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "City"
msgstr ""

#: includes/Privacy.php:225
msgid "Postal Code"
msgstr ""

#: includes/Privacy.php:227
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:227
#: includes/Vendor/SetupWizard.php:297
#: templates/admin-setup-wizard/step-store-wc-fields.php:58
#: templates/admin-setup-wizard/step-store-wc-fields.php:59
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "State"
msgstr ""

#: includes/Privacy.php:272
msgid "Bank Details"
msgstr ""

#: includes/Privacy.php:274
#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/Bank.php:51
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Account Name"
msgstr ""

#: includes/Privacy.php:275
#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/Bank.php:59
#: includes/Withdraw/functions.php:293
#: includes/Withdraw/functions.php:294
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Account Number"
msgstr ""

#: includes/Privacy.php:279
#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/Bank.php:91
#: includes/Withdraw/functions.php:310
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "IBAN"
msgstr ""

#: includes/Privacy.php:280
#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/Bank.php:99
#: includes/Withdraw/functions.php:314
msgid "Swift Code"
msgstr ""

#: includes/Privacy.php:291
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "PayPal Email"
msgstr ""

#. translators: vendor name.
#: includes/Privacy.php:369
msgid "Vendor %s data is removed."
msgstr ""

#: includes/Product/functions.php:70
msgid "Sorry, You can not modify another vendor's product !"
msgstr ""

#: includes/Product/functions.php:202
msgid "Before you can add a variation you need to add some variation attributes on the <strong>Attributes</strong> section"
msgstr ""

#: includes/Product/functions.php:212
msgid "Add variation"
msgstr ""

#: includes/Product/functions.php:213
msgid "Create variations from all attributes"
msgstr ""

#: includes/Product/functions.php:214
msgid "Delete all variations"
msgstr ""

#: includes/Product/functions.php:216
msgid "Toggle &quot;Enabled&quot;"
msgstr ""

#: includes/Product/functions.php:217
msgid "Toggle &quot;Downloadable&quot;"
msgstr ""

#: includes/Product/functions.php:218
msgid "Toggle &quot;Virtual&quot;"
msgstr ""

#: includes/Product/functions.php:220
msgid "Pricing"
msgstr ""

#: includes/Product/functions.php:221
msgid "Set regular prices"
msgstr ""

#: includes/Product/functions.php:222
msgid "Increase regular prices (fixed amount or percentage)"
msgstr ""

#: includes/Product/functions.php:223
msgid "Decrease regular prices (fixed amount or percentage)"
msgstr ""

#: includes/Product/functions.php:224
msgid "Set sale prices"
msgstr ""

#: includes/Product/functions.php:225
msgid "Increase sale prices (fixed amount or percentage)"
msgstr ""

#: includes/Product/functions.php:226
msgid "Decrease sale prices (fixed amount or percentage)"
msgstr ""

#: includes/Product/functions.php:227
msgid "Set scheduled sale dates"
msgstr ""

#: includes/Product/functions.php:229
#: templates/products/inventory.php:8
msgid "Inventory"
msgstr ""

#: includes/Product/functions.php:230
msgid "Toggle &quot;Manage stock&quot;"
msgstr ""

#: includes/Product/functions.php:231
#: templates/products/products-listing-row.php:189
#: templates/products/products-listing.php:126
msgid "Stock"
msgstr ""

#: includes/Product/functions.php:234
msgid "Length"
msgstr ""

#: includes/Product/functions.php:235
msgid "Width"
msgstr ""

#: includes/Product/functions.php:236
msgid "Height"
msgstr ""

#: includes/Product/functions.php:237
msgid "Weight"
msgstr ""

#: includes/Product/functions.php:239
msgid "Downloadable products"
msgstr ""

#: includes/Product/functions.php:240
msgid "Download limit"
msgstr ""

#: includes/Product/functions.php:241
msgid "Download expiry"
msgstr ""

#: includes/Product/functions.php:245
msgid "Go"
msgstr ""

#: includes/Product/functions.php:252
msgid "Default Form Values: These are the attributes that will be pre-selected on the frontend."
msgstr ""

#: includes/Product/functions.php:270
msgid "No default"
msgstr ""

#: includes/Product/functions.php:309
msgid "Save Variations"
msgstr ""

#. translators: number of items
#: includes/Product/functions.php:316
msgid "%s item"
msgid_plural "%s items"
msgstr[0] ""
msgstr[1] ""

#: includes/Product/functions.php:321
msgid "Expand"
msgstr ""

#: includes/Product/functions.php:324
msgid "Go to the first page"
msgstr ""

#: includes/Product/functions.php:325
msgid "Go to the previous page"
msgstr ""

#: includes/Product/functions.php:327
msgid "Select Page"
msgstr ""

#: includes/Product/functions.php:328
msgid "Current page"
msgstr ""

#: includes/Product/functions.php:333
msgctxt "number of pages"
msgid "of"
msgstr ""

#: includes/Product/functions.php:335
msgid "Go to the next page"
msgstr ""

#: includes/Product/functions.php:336
msgid "Go to the last page"
msgstr ""

#: includes/Product/functions.php:361
msgid "Visible"
msgstr ""

#: includes/Product/functions.php:362
msgid "Catalog"
msgstr ""

#: includes/Product/functions.php:363
#: includes/Product/Hooks.php:196
#: templates/products/listing-filter.php:110
#: assets/js/dashboard-charts.js:1
msgid "Search"
msgstr ""

#: includes/Product/functions.php:364
#: templates/products/edit-product-single.php:197
msgid "Hidden"
msgstr ""

#: includes/Product/functions.php:486
#: assets/js/vue-admin.js:2
msgid "Edit"
msgstr ""

#: includes/Product/functions.php:510
#: templates/my-orders.php:101
#: templates/orders/listing.php:160
#: templates/sub-orders.php:99
msgid "View"
msgstr ""

#: includes/Product/functions.php:605
msgid "Default sorting"
msgstr ""

#: includes/Product/functions.php:606
msgid "Sort by popularity"
msgstr ""

#: includes/Product/functions.php:607
msgid "Sort by average rating"
msgstr ""

#: includes/Product/functions.php:608
msgid "Sort by latest"
msgstr ""

#: includes/Product/functions.php:609
msgid "Sort by price: low to high"
msgstr ""

#: includes/Product/functions.php:610
msgid "Sort by price: high to low"
msgstr ""

#: includes/Product/functions.php:618
msgid "Relevance"
msgstr ""

#: includes/Product/Hooks.php:66
msgid "Products not found with this search"
msgstr ""

#: includes/Product/Hooks.php:155
#: templates/orders/commission-meta-box-html.php:85
msgid "SKU:"
msgstr ""

#: includes/Product/Hooks.php:192
msgid "Enter product name"
msgstr ""

#: includes/Product/Hooks.php:200
msgid "Shop order"
msgstr ""

#: includes/Product/Hooks.php:418
msgid "As this is your own product, the \"Add to Cart\" button has been removed. Please visit as a guest to view it."
msgstr ""

#: includes/Product/Hooks.php:488
#: includes/Product/Hooks.php:489
#: assets/js/vue-admin.js:2
msgid "When the value is 0, no commissions will be deducted from this vendor."
msgstr ""

#: includes/Product/Manager.php:474
msgid "No product ID found for updating"
msgstr ""

#: includes/Product/VendorStoreInfo.php:65
msgid "Show Vendor Info"
msgstr ""

#: includes/Product/VendorStoreInfo.php:66
msgid "Show vendor information on single product page"
msgstr ""

#: includes/ProductCategory/Helper.php:255
msgid "Select a category"
msgstr ""

#: includes/ProductCategory/Helper.php:256
msgid "This category has already been selected"
msgstr ""

#: includes/ProductSections/BestSelling.php:39
msgid "Best Selling Products"
msgstr ""

#: includes/ProductSections/BestSelling.php:52
msgid "best selling products"
msgstr ""

#: includes/ProductSections/Featured.php:39
msgid "Featured Products"
msgstr ""

#: includes/ProductSections/Featured.php:52
msgid "featured products"
msgstr ""

#: includes/ProductSections/Latest.php:39
msgid "Latest Products"
msgstr ""

#: includes/ProductSections/Latest.php:52
msgid "latest products"
msgstr ""

#. translators: %s: product section name.
#: includes/ProductSections/Manager.php:96
msgid "Hide %s section"
msgstr ""

#. translators: %s: product section name.
#: includes/ProductSections/Manager.php:118
msgid "Section title for %s"
msgstr ""

#: includes/ProductSections/Manager.php:176
msgid "All Products"
msgstr ""

#: includes/ProductSections/TopRated.php:39
msgid "Top Rated Products"
msgstr ""

#: includes/ProductSections/TopRated.php:52
msgid "top rated products"
msgstr ""

#: includes/Registration.php:41
msgid "Nonce verification failed"
msgstr ""

#: includes/Registration.php:48
msgid "Cheating, eh?"
msgstr ""

#: includes/Registration.php:55
msgid "Please enter your first name."
msgstr ""

#: includes/Registration.php:56
msgid "Please enter your last name."
msgstr ""

#: includes/Registration.php:57
msgid "Please enter your phone number."
msgstr ""

#: includes/Registration.php:58
msgid "Please provide a shop name."
msgstr ""

#: includes/Registration.php:59
msgid "Please provide a unique shop URL."
msgstr ""

#: includes/Registration.php:73
msgid "Shop URL is not available"
msgstr ""

#: includes/reports.php:357
msgid "Sales total"
msgstr ""

#: includes/reports.php:374
msgid "Sales"
msgstr ""

#: includes/REST/AdminDashboardController.php:42
msgid "Number of feed item"
msgstr ""

#: includes/REST/AdminDashboardController.php:48
msgid "Flag for showing summary"
msgstr ""

#: includes/REST/AdminDashboardController.php:54
msgid "Flag for showing author"
msgstr ""

#: includes/REST/AdminDashboardController.php:60
msgid "Flag for showing date"
msgstr ""

#: includes/REST/AdminDashboardController.php:112
msgid "An error has occurred, which probably means the feed is down. Try again later."
msgstr ""

#: includes/REST/AdminDashboardController.php:125
msgid "Untitled"
msgstr ""

#: includes/REST/AdminMiscController.php:48
msgid "Dokan setting section"
msgstr ""

#: includes/REST/AdminMiscController.php:54
msgid "Dokan setting section key"
msgstr ""

#: includes/REST/AdminNoticeController.php:42
msgid "Choose notice scope: \"local\" displays only on Dokan pages, \"global\" displays across the entire site."
msgstr ""

#: includes/REST/AdminOnboardingController.php:62
msgid "Onboarding data"
msgstr ""

#: includes/REST/AdminOnboardingController.php:68
msgid "Marketplace goal"
msgstr ""

#: includes/REST/AdminOnboardingController.php:74
msgid "Marketplace focus"
msgstr ""

#: includes/REST/AdminOnboardingController.php:80
msgid "Handle delivery"
msgstr ""

#: includes/REST/AdminOnboardingController.php:86
msgid "Top priority"
msgstr ""

#: includes/REST/AdminOnboardingController.php:94
msgid "Custom store URL"
msgstr ""

#: includes/REST/AdminOnboardingController.php:100
msgid "Share essentials"
msgstr ""

#: includes/REST/AdminOnboardingController.php:106
msgid "Plugins to install"
msgstr ""

#: includes/REST/AdminOnboardingController.php:113
msgid "Plugin ID"
msgstr ""

#: includes/REST/AdminOnboardingController.php:117
msgid "Plugin info"
msgstr ""

#: includes/REST/AdminOnboardingController.php:188
msgid "Onboarding created successfully"
msgstr ""

#: includes/REST/AdminReportController.php:145
#: templates/dashboard/orders-widget.php:28
#: templates/dashboard/products-widget.php:32
#: templates/my-orders.php:28
#: templates/orders/order-fee-html.php:24
#: templates/orders/sub-order-related-order-meta-box-html.php:40
#: templates/sub-orders.php:51
msgid "Total"
msgstr ""

#: includes/REST/AdminSetupGuideController.php:129
#: includes/REST/AdminSetupGuideController.php:173
msgid "Invalid step."
msgstr ""

#: includes/REST/CommissionControllerV1.php:43
msgid "Products price"
msgstr ""

#: includes/REST/CommissionControllerV1.php:50
msgid "The amount on that the commission will be calculated."
msgstr ""

#: includes/REST/CommissionControllerV1.php:57
msgid "Vendor id"
msgstr ""

#: includes/REST/CommissionControllerV1.php:64
msgid "Category ids"
msgstr ""

#: includes/REST/CommissionControllerV1.php:75
msgid "In which context the commission will be calculated"
msgstr ""

#: includes/REST/CustomersController.php:38
msgid "Search string."
msgstr ""

#: includes/REST/CustomersController.php:43
msgid "Comma-separated list of customer IDs to exclude."
msgstr ""

#: includes/REST/CustomersController.php:63
msgid "Sorry, you cannot list resources."
msgstr ""

#: includes/REST/CustomersController.php:64
msgid "Sorry, you are not allowed to create resources."
msgstr ""

#: includes/REST/CustomersController.php:65
msgid "Sorry, you are not allowed to edit this resource."
msgstr ""

#: includes/REST/CustomersController.php:66
msgid "Sorry, you are not allowed to delete this resource."
msgstr ""

#: includes/REST/CustomersController.php:67
msgid "Sorry, you are not allowed to batch update resources."
msgstr ""

#: includes/REST/CustomersController.php:68
msgid "Sorry, you are not allowed to search customers."
msgstr ""

#: includes/REST/CustomersController.php:172
msgid "You do not have permission to search customers."
msgstr ""

#: includes/REST/CustomersController.php:180
msgid "Search term is required."
msgstr ""

#: includes/REST/CustomersController.php:265
msgid "Invalid customer."
msgstr ""

#: includes/REST/DokanDataContinentsController.php:41
#: includes/REST/DokanDataCountriesController.php:41
msgid "You are not allowed to do this action."
msgstr ""

#: includes/REST/DummyDataController.php:173
msgid "Vendors products data."
msgstr ""

#: includes/REST/DummyDataController.php:181
#: includes/REST/OrderController.php:918
#: includes/REST/ProductController.php:1757
#: includes/REST/VendorProductCategoriesController.php:44
msgid "Unique identifier for the resource."
msgstr ""

#: includes/REST/DummyDataController.php:187
#: includes/REST/OrderController.php:1274
#: includes/REST/ProductController.php:1763
msgid "Product name."
msgstr ""

#: includes/REST/DummyDataController.php:193
#: includes/REST/ProductController.php:1768
msgid "Product slug."
msgstr ""

#: includes/REST/DummyDataController.php:199
#: includes/REST/ProductController.php:1773
msgid "Product URL."
msgstr ""

#: includes/REST/DummyDataController.php:207
#: includes/REST/ProductController.php:1780
msgid "The date the product was created, in the site's timezone."
msgstr ""

#: includes/REST/DummyDataController.php:214
#: includes/REST/ProductController.php:1786
msgid "The date the product was created, as GMT."
msgstr ""

#: includes/REST/DummyDataController.php:221
#: includes/REST/ProductController.php:1792
msgid "The date the product was last modified, in the site's timezone."
msgstr ""

#: includes/REST/DummyDataController.php:228
#: includes/REST/ProductController.php:1798
msgid "The date the product was last modified, as GMT."
msgstr ""

#: includes/REST/DummyDataController.php:235
#: includes/REST/ProductController.php:1804
msgid "Product type."
msgstr ""

#: includes/REST/DummyDataController.php:244
#: includes/REST/ProductController.php:1811
msgid "Product status (post status)."
msgstr ""

#: includes/REST/DummyDataController.php:253
#: includes/REST/ProductController.php:1818
msgid "Featured product."
msgstr ""

#: includes/REST/DummyDataController.php:259
#: includes/REST/ProductController.php:1824
msgid "Catalog visibility."
msgstr ""

#: includes/REST/DummyDataController.php:267
#: includes/REST/ProductController.php:1831
msgid "Product description."
msgstr ""

#: includes/REST/DummyDataController.php:273
#: includes/REST/ProductController.php:1836
msgid "Product short description."
msgstr ""

#: includes/REST/DummyDataController.php:279
#: includes/REST/ProductController.php:1841
msgid "Unique identifier."
msgstr ""

#: includes/REST/DummyDataController.php:285
#: includes/REST/ProductController.php:1846
msgid "Current product price."
msgstr ""

#: includes/REST/DummyDataController.php:291
#: includes/REST/ProductController.php:1852
msgid "Product regular price."
msgstr ""

#: includes/REST/DummyDataController.php:297
#: includes/REST/ProductController.php:1857
msgid "Product sale price."
msgstr ""

#: includes/REST/DummyDataController.php:304
#: includes/REST/ProductController.php:1862
msgid "Start date of sale price, in the site's timezone."
msgstr ""

#: includes/REST/DummyDataController.php:310
#: includes/REST/ProductController.php:1867
msgid "Start date of sale price, as GMT."
msgstr ""

#: includes/REST/DummyDataController.php:316
#: includes/REST/ProductController.php:1872
msgid "End date of sale price, in the site's timezone."
msgstr ""

#: includes/REST/DummyDataController.php:322
#: includes/REST/ProductController.php:1877
msgid "End date of sale price, as GMT."
msgstr ""

#: includes/REST/DummyDataController.php:328
#: includes/REST/ProductController.php:1888
msgid "Shows if the product is on sale."
msgstr ""

#: includes/REST/DummyDataController.php:334
#: includes/REST/ProductController.php:1894
msgid "Shows if the product can be bought."
msgstr ""

#: includes/REST/DummyDataController.php:340
#: includes/REST/ProductController.php:1900
msgid "Amount of sales."
msgstr ""

#: includes/REST/DummyDataController.php:347
#: includes/REST/ProductController.php:1906
msgid "If the product is virtual."
msgstr ""

#: includes/REST/DummyDataController.php:354
#: includes/REST/ProductController.php:1912
msgid "If the product is downloadable."
msgstr ""

#: includes/REST/DummyDataController.php:361
#: includes/REST/ProductController.php:1918
msgid "List of downloadable files."
msgstr ""

#: includes/REST/DummyDataController.php:368
#: includes/REST/ProductController.php:1925
msgid "File MD5 hash."
msgstr ""

#: includes/REST/DummyDataController.php:375
#: includes/REST/ProductController.php:1931
msgid "File name."
msgstr ""

#: includes/REST/DummyDataController.php:381
#: includes/REST/ProductController.php:1936
msgid "File URL."
msgstr ""

#: includes/REST/DummyDataController.php:390
#: includes/REST/ProductController.php:1944
msgid "Number of times downloadable files can be downloaded after purchase."
msgstr ""

#: includes/REST/DummyDataController.php:397
#: includes/REST/ProductController.php:1950
msgid "Number of days until access to downloadable files expires."
msgstr ""

#: includes/REST/DummyDataController.php:404
#: includes/REST/ProductController.php:1956
msgid "Product external URL. Only for external products."
msgstr ""

#: includes/REST/DummyDataController.php:411
#: includes/REST/ProductController.php:1962
msgid "Product external button text. Only for external products."
msgstr ""

#: includes/REST/DummyDataController.php:417
#: includes/REST/ProductController.php:1967
msgid "Tax status."
msgstr ""

#: includes/REST/DummyDataController.php:425
#: includes/REST/ProductController.php:1974
msgid "Tax class."
msgstr ""

#: includes/REST/DummyDataController.php:431
#: includes/REST/ProductController.php:1979
msgid "Stock management at product level."
msgstr ""

#: includes/REST/DummyDataController.php:437
#: includes/REST/ProductController.php:1985
msgid "Stock quantity."
msgstr ""

#: includes/REST/DummyDataController.php:444
#: includes/REST/ProductController.php:1990
msgid "Controls whether or not the product is listed as \"in stock\" or \"out of stock\" on the frontend."
msgstr ""

#: includes/REST/DummyDataController.php:450
#: includes/REST/ProductController.php:1996
msgid "If managing stock, this controls if backorders are allowed."
msgstr ""

#: includes/REST/DummyDataController.php:457
#: includes/REST/ProductController.php:2003
msgid "Shows if backorders are allowed."
msgstr ""

#: includes/REST/DummyDataController.php:463
#: includes/REST/ProductController.php:2009
msgid "Shows if the product is on backordered."
msgstr ""

#: includes/REST/DummyDataController.php:469
#: includes/REST/ProductController.php:2015
msgid "Allow one item to be bought in a single order."
msgstr ""

#. translators: %s: weight unit
#: includes/REST/DummyDataController.php:477
#: includes/REST/ProductController.php:2022
msgid "Product weight (%s)."
msgstr ""

#: includes/REST/DummyDataController.php:483
#: includes/REST/ProductController.php:2027
msgid "Product dimensions."
msgstr ""

#. translators: %s: dimension unit
#: includes/REST/DummyDataController.php:489
#: includes/REST/ProductController.php:2033
msgid "Product length (%s)."
msgstr ""

#. translators: %s: dimension unit
#: includes/REST/DummyDataController.php:496
#: includes/REST/ProductController.php:2039
msgid "Product width (%s)."
msgstr ""

#. translators: %s: dimension unit
#: includes/REST/DummyDataController.php:503
#: includes/REST/ProductController.php:2045
msgid "Product height (%s)."
msgstr ""

#: includes/REST/DummyDataController.php:511
#: includes/REST/ProductController.php:2052
msgid "Shows if the product need to be shipped."
msgstr ""

#: includes/REST/DummyDataController.php:517
#: includes/REST/ProductController.php:2058
msgid "Shows whether or not the product shipping is taxable."
msgstr ""

#: includes/REST/DummyDataController.php:523
#: includes/REST/ProductController.php:2064
msgid "Shipping class slug."
msgstr ""

#: includes/REST/DummyDataController.php:529
#: includes/REST/ProductController.php:2069
msgid "Shipping class ID."
msgstr ""

#: includes/REST/DummyDataController.php:536
#: includes/REST/ProductController.php:2075
msgid "Allow reviews."
msgstr ""

#: includes/REST/DummyDataController.php:542
#: includes/REST/ProductController.php:2081
msgid "Reviews average rating."
msgstr ""

#: includes/REST/DummyDataController.php:549
#: includes/REST/ProductController.php:2087
msgid "Amount of reviews that the product have."
msgstr ""

#: includes/REST/DummyDataController.php:556
#: includes/REST/ProductController.php:2093
msgid "List of related products IDs."
msgstr ""

#: includes/REST/DummyDataController.php:565
#: includes/REST/ProductController.php:2102
msgid "List of up-sell products IDs."
msgstr ""

#: includes/REST/DummyDataController.php:573
#: includes/REST/ProductController.php:2110
msgid "List of cross-sell products IDs."
msgstr ""

#: includes/REST/DummyDataController.php:581
#: includes/REST/ProductController.php:2118
msgid "Product parent ID."
msgstr ""

#: includes/REST/DummyDataController.php:588
#: includes/REST/ProductController.php:2123
msgid "Optional note to send the customer after purchase."
msgstr ""

#: includes/REST/DummyDataController.php:594
#: includes/REST/ProductController.php:2128
msgid "List of categories."
msgstr ""

#: includes/REST/DummyDataController.php:601
#: includes/REST/ProductController.php:2135
msgid "Category ID."
msgstr ""

#: includes/REST/DummyDataController.php:607
#: includes/REST/ProductController.php:2140
msgid "Category name."
msgstr ""

#: includes/REST/DummyDataController.php:614
#: includes/REST/ProductController.php:2146
msgid "Category slug."
msgstr ""

#: includes/REST/DummyDataController.php:624
#: includes/REST/ProductController.php:2155
msgid "List of tags."
msgstr ""

#: includes/REST/DummyDataController.php:631
#: includes/REST/ProductController.php:2162
msgid "Tag ID."
msgstr ""

#: includes/REST/DummyDataController.php:637
#: includes/REST/ProductController.php:2167
msgid "Tag name."
msgstr ""

#: includes/REST/DummyDataController.php:644
#: includes/REST/ProductController.php:2173
msgid "Tag slug."
msgstr ""

#: includes/REST/DummyDataController.php:654
#: includes/REST/ProductController.php:2182
msgid "List of images."
msgstr ""

#: includes/REST/DummyDataController.php:661
#: includes/REST/ProductController.php:2189
msgid "Image ID."
msgstr ""

#: includes/REST/DummyDataController.php:667
#: includes/REST/ProductController.php:2194
msgid "The date the image was created, in the site's timezone."
msgstr ""

#: includes/REST/DummyDataController.php:675
#: includes/REST/ProductController.php:2200
msgid "The date the image was created, as GMT."
msgstr ""

#: includes/REST/DummyDataController.php:683
#: includes/REST/ProductController.php:2206
msgid "The date the image was last modified, in the site's timezone."
msgstr ""

#: includes/REST/DummyDataController.php:691
#: includes/REST/ProductController.php:2212
msgid "The date the image was last modified, as GMT."
msgstr ""

#: includes/REST/DummyDataController.php:699
#: includes/REST/ProductController.php:2218
msgid "Image URL."
msgstr ""

#: includes/REST/DummyDataController.php:706
#: includes/REST/ProductController.php:2224
msgid "Image name."
msgstr ""

#: includes/REST/DummyDataController.php:712
#: includes/REST/ProductController.php:2229
msgid "Image alternative text."
msgstr ""

#: includes/REST/DummyDataController.php:718
#: includes/REST/ProductController.php:2234
msgid "Image position. 0 means that the image is featured."
msgstr ""

#: includes/REST/DummyDataController.php:728
#: includes/REST/ProductController.php:2242
msgid "List of attributes."
msgstr ""

#: includes/REST/DummyDataController.php:735
#: includes/REST/DummyDataController.php:785
#: includes/REST/ProductController.php:2249
#: includes/REST/ProductController.php:2294
msgid "Attribute ID."
msgstr ""

#: includes/REST/DummyDataController.php:741
#: includes/REST/DummyDataController.php:791
#: includes/REST/ProductAttributeController.php:181
#: includes/REST/ProductController.php:2254
#: includes/REST/ProductController.php:2299
msgid "Attribute name."
msgstr ""

#: includes/REST/DummyDataController.php:747
#: includes/REST/ProductController.php:2259
msgid "Attribute position."
msgstr ""

#: includes/REST/DummyDataController.php:754
#: includes/REST/ProductController.php:2264
msgid "Define if the attribute is visible on the \"Additional information\" tab in the product's page."
msgstr ""

#: includes/REST/DummyDataController.php:760
#: includes/REST/ProductController.php:2270
msgid "Define if the attribute can be used as variation."
msgstr ""

#: includes/REST/DummyDataController.php:766
#: includes/REST/ProductController.php:2276
msgid "List of available term names of the attribute."
msgstr ""

#: includes/REST/DummyDataController.php:778
#: includes/REST/ProductController.php:2287
msgid "Defaults variation attributes."
msgstr ""

#: includes/REST/DummyDataController.php:797
#: includes/REST/ProductController.php:2304
msgid "Selected attribute term name."
msgstr ""

#: includes/REST/DummyDataController.php:806
#: includes/REST/ProductController.php:2312
msgid "List of variations IDs."
msgstr ""

#: includes/REST/DummyDataController.php:816
#: includes/REST/ProductController.php:2321
msgid "List of grouped products ID."
msgstr ""

#: includes/REST/DummyDataController.php:825
#: includes/REST/ProductController.php:2329
msgid "Menu order, used to custom sort products."
msgstr ""

#: includes/REST/DummyDataController.php:831
#: includes/REST/OrderController.php:1235
#: includes/REST/OrderController.php:1347
#: includes/REST/OrderController.php:1438
#: includes/REST/OrderController.php:1524
#: includes/REST/OrderController.php:1622
#: includes/REST/OrderController.php:1680
#: includes/REST/ProductController.php:2334
msgid "Meta data."
msgstr ""

#: includes/REST/DummyDataController.php:838
#: includes/REST/OrderController.php:1242
#: includes/REST/OrderController.php:1354
#: includes/REST/OrderController.php:1445
#: includes/REST/OrderController.php:1531
#: includes/REST/OrderController.php:1629
#: includes/REST/OrderController.php:1687
#: includes/REST/ProductController.php:2341
msgid "Meta ID."
msgstr ""

#: includes/REST/DummyDataController.php:845
#: includes/REST/OrderController.php:1248
#: includes/REST/OrderController.php:1360
#: includes/REST/OrderController.php:1451
#: includes/REST/OrderController.php:1537
#: includes/REST/OrderController.php:1635
#: includes/REST/OrderController.php:1693
#: includes/REST/ProductController.php:2347
msgid "Meta key."
msgstr ""

#: includes/REST/DummyDataController.php:851
#: includes/REST/OrderController.php:1253
#: includes/REST/OrderController.php:1365
#: includes/REST/OrderController.php:1456
#: includes/REST/OrderController.php:1542
#: includes/REST/OrderController.php:1640
#: includes/REST/OrderController.php:1698
#: includes/REST/ProductController.php:2352
msgid "Meta value."
msgstr ""

#: includes/REST/DummyDataController.php:863
msgid "Vendors profile data."
msgstr ""

#: includes/REST/DummyDataController.php:869
msgid "Vendor email."
msgstr ""

#: includes/REST/DummyDataController.php:876
msgid "Vendor password."
msgstr ""

#: includes/REST/DummyDataController.php:882
msgid "Vendor store name."
msgstr ""

#: includes/REST/DummyDataController.php:889
msgid "Vendor social"
msgstr ""

#: includes/REST/DummyDataController.php:894
msgid "Vendor payments"
msgstr ""

#: includes/REST/DummyDataController.php:899
msgid "Vendor phone"
msgstr ""

#: includes/REST/DummyDataController.php:906
msgid "Vendor show email"
msgstr ""

#: includes/REST/DummyDataController.php:912
msgid "Vendor address"
msgstr ""

#: includes/REST/DummyDataController.php:918
msgid "Vendor location"
msgstr ""

#: includes/REST/DummyDataController.php:924
msgid "Vendor banner"
msgstr ""

#: includes/REST/DummyDataController.php:930
msgid "Vendor icon"
msgstr ""

#: includes/REST/DummyDataController.php:936
msgid "Vendor gravatar"
msgstr ""

#: includes/REST/DummyDataController.php:942
msgid "Vendor show more tpab"
msgstr ""

#: includes/REST/DummyDataController.php:948
msgid "Vendor show product per page"
msgstr ""

#: includes/REST/DummyDataController.php:955
msgid "Enable terms and condition"
msgstr ""

#: includes/REST/DummyDataController.php:963
msgid "Store seo"
msgstr ""

#: includes/REST/DummyDataController.php:968
msgid "Store time open close array."
msgstr ""

#: includes/REST/DummyDataController.php:973
msgid "Vendor enabled."
msgstr ""

#: includes/REST/DummyDataController.php:980
msgid "Vendor is trusted."
msgstr ""

#: includes/REST/DummyDataController.php:989
msgid "Vendor item index to import."
msgstr ""

#: includes/REST/DummyDataController.php:995
msgid "Total vendors"
msgstr ""

#: includes/REST/OrderController.php:90
#: includes/REST/OrderController.php:121
#: includes/REST/OrderController.php:152
#: includes/REST/ProductBlockController.php:42
#: includes/REST/ProductController.php:119
#: includes/REST/ProductController.php:143
#: includes/REST/ProductController.php:188
#: includes/REST/StoreController.php:62
#: includes/REST/StoreController.php:98
#: includes/REST/StoreController.php:115
#: includes/REST/StoreController.php:142
#: includes/REST/StoreController.php:176
#: includes/REST/StoreController.php:208
#: includes/REST/WithdrawController.php:91
#: includes/REST/WithdrawController.php:958
msgid "Unique identifier for the object."
msgstr ""

#: includes/REST/OrderController.php:139
msgid "Order note content."
msgstr ""

#: includes/REST/OrderController.php:156
msgid "Unique identifier for the note object."
msgstr ""

#: includes/REST/OrderController.php:212
msgid "Invalid Order ID."
msgstr ""

#: includes/REST/OrderController.php:232
#: includes/REST/ProductController.php:345
#: includes/REST/ProductController.php:389
msgid "No seller found"
msgstr ""

#: includes/REST/OrderController.php:244
#: includes/REST/ProductController.php:401
#: includes/REST/ProductController.php:426
msgid "Sorry, you have no permission to do this. Since it's not your product."
msgstr ""

#: includes/REST/OrderController.php:374
msgid "Invalid order ID"
msgstr ""

#: includes/REST/OrderController.php:382
msgid "Order status must me required"
msgstr ""

#: includes/REST/OrderController.php:390
msgid "Order status not valid"
msgstr ""

#: includes/REST/OrderController.php:580
#: includes/REST/OrderController.php:650
#: includes/REST/OrderController.php:692
#: includes/REST/OrderController.php:724
msgid "Invalid order ID."
msgstr ""

#: includes/REST/OrderController.php:586
#: includes/REST/OrderController.php:688
#: includes/REST/OrderController.php:720
msgid "You have no permission to view this notes"
msgstr ""

#. translators: 1) %s: post type name
#: includes/REST/OrderController.php:639
#: includes/REST/ProductController.php:354
msgid "Cannot create existing %s."
msgstr ""

#: includes/REST/OrderController.php:646
msgid "You have no permission to create this notes"
msgstr ""

#: includes/REST/OrderController.php:657
msgid "Cannot create order note, please try again."
msgstr ""

#: includes/REST/OrderController.php:698
#: includes/REST/OrderController.php:730
msgid "Invalid resource ID."
msgstr ""

#: includes/REST/OrderController.php:739
msgid "Given order note cannot be deleted."
msgstr ""

#: includes/REST/OrderController.php:924
msgid "Parent order ID."
msgstr ""

#: includes/REST/OrderController.php:929
msgid "Orders belongs to specific seller"
msgstr ""

#: includes/REST/OrderController.php:935
msgid "Order number."
msgstr ""

#: includes/REST/OrderController.php:941
msgid "Order key."
msgstr ""

#: includes/REST/OrderController.php:947
msgid "Shows where the order was created."
msgstr ""

#: includes/REST/OrderController.php:953
msgid "Version of WooCommerce which last updated the order."
msgstr ""

#: includes/REST/OrderController.php:959
msgid "Order status."
msgstr ""

#: includes/REST/OrderController.php:966
msgid "Currency the order was created with, in ISO format."
msgstr ""

#: includes/REST/OrderController.php:973
msgid "The date the order was created, in the site's timezone."
msgstr ""

#: includes/REST/OrderController.php:980
msgid "The date the order was created, as GMT."
msgstr ""

#: includes/REST/OrderController.php:986
msgid "The date the order was last modified, in the site's timezone."
msgstr ""

#: includes/REST/OrderController.php:992
msgid "The date the order was last modified, as GMT."
msgstr ""

#: includes/REST/OrderController.php:998
msgid "Total discount amount for the order."
msgstr ""

#: includes/REST/OrderController.php:1004
msgid "Total discount tax amount for the order."
msgstr ""

#: includes/REST/OrderController.php:1010
msgid "Total shipping amount for the order."
msgstr ""

#: includes/REST/OrderController.php:1016
msgid "Total shipping tax amount for the order."
msgstr ""

#: includes/REST/OrderController.php:1022
msgid "Sum of line item taxes only."
msgstr ""

#: includes/REST/OrderController.php:1028
msgid "Grand total."
msgstr ""

#: includes/REST/OrderController.php:1034
msgid "Sum of all taxes."
msgstr ""

#: includes/REST/OrderController.php:1040
msgid "True the prices included tax during checkout."
msgstr ""

#: includes/REST/OrderController.php:1046
#: includes/REST/VendorDashboardController.php:521
msgid "User ID who owns the order. 0 for guests."
msgstr ""

#: includes/REST/OrderController.php:1052
msgid "Order id to search order"
msgstr ""

#: includes/REST/OrderController.php:1058
msgid "Customer's IP address."
msgstr ""

#: includes/REST/OrderController.php:1064
msgid "User agent of the customer."
msgstr ""

#: includes/REST/OrderController.php:1070
msgid "Note left by customer during checkout."
msgstr ""

#: includes/REST/OrderController.php:1075
msgid "Billing address."
msgstr ""

#: includes/REST/OrderController.php:1080
#: includes/REST/OrderController.php:1143
msgid "First name."
msgstr ""

#: includes/REST/OrderController.php:1085
#: includes/REST/OrderController.php:1148
msgid "Last name."
msgstr ""

#: includes/REST/OrderController.php:1090
#: includes/REST/OrderController.php:1153
msgid "Company name."
msgstr ""

#: includes/REST/OrderController.php:1095
#: includes/REST/OrderController.php:1158
#: templates/admin-setup-wizard/step-store-wc-fields.php:37
msgid "Address line 1"
msgstr ""

#: includes/REST/OrderController.php:1100
#: includes/REST/OrderController.php:1163
#: templates/admin-setup-wizard/step-store-wc-fields.php:44
msgid "Address line 2"
msgstr ""

#: includes/REST/OrderController.php:1105
#: includes/REST/OrderController.php:1168
msgid "City name."
msgstr ""

#: includes/REST/OrderController.php:1110
#: includes/REST/OrderController.php:1173
msgid "ISO code or name of the state, province or district."
msgstr ""

#: includes/REST/OrderController.php:1115
#: includes/REST/OrderController.php:1178
msgid "Postal code."
msgstr ""

#: includes/REST/OrderController.php:1120
#: includes/REST/OrderController.php:1183
msgid "Country code in ISO 3166-1 alpha-2 format."
msgstr ""

#: includes/REST/OrderController.php:1125
msgid "Email address."
msgstr ""

#: includes/REST/OrderController.php:1131
msgid "Phone number."
msgstr ""

#: includes/REST/OrderController.php:1138
msgid "Shipping address."
msgstr ""

#: includes/REST/OrderController.php:1190
msgid "Payment method ID."
msgstr ""

#: includes/REST/OrderController.php:1195
msgid "Payment method title."
msgstr ""

#: includes/REST/OrderController.php:1200
msgid "Unique transaction ID."
msgstr ""

#: includes/REST/OrderController.php:1205
msgid "The date the order was paid, in the site's timezone."
msgstr ""

#: includes/REST/OrderController.php:1211
msgid "The date the order was paid, as GMT."
msgstr ""

#: includes/REST/OrderController.php:1217
msgid "The date the order was completed, in the site's timezone."
msgstr ""

#: includes/REST/OrderController.php:1223
msgid "The date the order was completed, as GMT."
msgstr ""

#: includes/REST/OrderController.php:1229
msgid "MD5 hash of cart items to ensure orders are not modified."
msgstr ""

#: includes/REST/OrderController.php:1261
msgid "Line items data."
msgstr ""

#: includes/REST/OrderController.php:1268
#: includes/REST/OrderController.php:1396
#: includes/REST/OrderController.php:1474
#: includes/REST/OrderController.php:1560
#: includes/REST/OrderController.php:1658
msgid "Item ID."
msgstr ""

#: includes/REST/OrderController.php:1279
#: includes/REST/OrderControllerV2.php:76
msgid "Product ID."
msgstr ""

#: includes/REST/OrderController.php:1284
msgid "Variation ID, if applicable."
msgstr ""

#: includes/REST/OrderController.php:1289
msgid "Quantity ordered."
msgstr ""

#: includes/REST/OrderController.php:1294
msgid "Tax class of product."
msgstr ""

#: includes/REST/OrderController.php:1299
msgid "Line subtotal (before discounts)."
msgstr ""

#: includes/REST/OrderController.php:1304
msgid "Line subtotal tax (before discounts)."
msgstr ""

#: includes/REST/OrderController.php:1310
#: includes/REST/OrderController.php:1490
#: includes/REST/OrderController.php:1582
msgid "Line total (after discounts)."
msgstr ""

#: includes/REST/OrderController.php:1315
#: includes/REST/OrderController.php:1495
#: includes/REST/OrderController.php:1587
msgid "Line total tax (after discounts)."
msgstr ""

#: includes/REST/OrderController.php:1321
#: includes/REST/OrderController.php:1501
#: includes/REST/OrderController.php:1593
msgid "Line taxes."
msgstr ""

#: includes/REST/OrderController.php:1329
#: includes/REST/OrderController.php:1408
#: includes/REST/OrderController.php:1509
#: includes/REST/OrderController.php:1601
msgid "Tax rate ID."
msgstr ""

#: includes/REST/OrderController.php:1334
#: includes/REST/OrderController.php:1515
#: includes/REST/OrderController.php:1607
msgid "Tax total."
msgstr ""

#: includes/REST/OrderController.php:1339
#: includes/REST/OrderController.php:1613
msgid "Tax subtotal."
msgstr ""

#: includes/REST/OrderController.php:1373
msgid "Product SKU."
msgstr ""

#: includes/REST/OrderController.php:1379
msgid "Product price."
msgstr ""

#: includes/REST/OrderController.php:1388
msgid "Tax lines data."
msgstr ""

#: includes/REST/OrderController.php:1402
msgid "Tax rate code."
msgstr ""

#: includes/REST/OrderController.php:1414
msgid "Tax rate label."
msgstr ""

#: includes/REST/OrderController.php:1420
msgid "Show if is a compound tax rate."
msgstr ""

#: includes/REST/OrderController.php:1426
msgid "Tax total (not including shipping taxes)."
msgstr ""

#: includes/REST/OrderController.php:1432
msgid "Shipping tax total."
msgstr ""

#: includes/REST/OrderController.php:1467
msgid "Shipping lines data."
msgstr ""

#: includes/REST/OrderController.php:1480
msgid "Shipping method name."
msgstr ""

#: includes/REST/OrderController.php:1485
msgid "Shipping method ID."
msgstr ""

#: includes/REST/OrderController.php:1553
msgid "Fee lines data."
msgstr ""

#: includes/REST/OrderController.php:1566
msgid "Fee name."
msgstr ""

#: includes/REST/OrderController.php:1571
msgid "Tax class of fee."
msgstr ""

#: includes/REST/OrderController.php:1576
msgid "Tax status of fee."
msgstr ""

#: includes/REST/OrderController.php:1651
msgid "Coupons line data."
msgstr ""

#: includes/REST/OrderController.php:1664
msgid "Coupon code."
msgstr ""

#: includes/REST/OrderController.php:1669
msgid "Discount total."
msgstr ""

#: includes/REST/OrderController.php:1674
msgid "Discount total tax."
msgstr ""

#: includes/REST/OrderController.php:1709
msgid "List of refunds."
msgstr ""

#: includes/REST/OrderController.php:1717
msgid "Refund ID."
msgstr ""

#: includes/REST/OrderController.php:1723
msgid "Refund reason."
msgstr ""

#: includes/REST/OrderController.php:1729
msgid "Refund total."
msgstr ""

#: includes/REST/OrderController.php:1738
msgid "Define if the order is paid. It will set the status to processing and reduce stock items."
msgstr ""

#: includes/REST/OrderController.php:1767
msgid "The customer_id must be an integer, accepted value is 0 or any integer value"
msgstr ""

#: includes/REST/OrderController.php:1823
msgid "Limit response to resources published after a given ISO8601 compliant date."
msgstr ""

#: includes/REST/OrderController.php:1830
msgid "Limit response to resources published before a given ISO8601 compliant date."
msgstr ""

#: includes/REST/OrderControllerV2.php:54
msgid "Download IDs."
msgstr ""

#: includes/REST/OrderControllerV2.php:58
msgid "Download product IDs."
msgstr ""

#: includes/REST/OrderControllerV2.php:71
msgid "Download ID."
msgstr ""

#: includes/REST/OrderControllerV2.php:81
msgid "Permission ID."
msgstr ""

#: includes/REST/OrderControllerV2.php:99
msgid "Order ids"
msgstr ""

#: includes/REST/OrderControllerV2.php:105
#: assets/js/dashboard-charts.js:1
msgid "Order status"
msgstr ""

#. translators: numeric number of files
#: includes/REST/OrderControllerV2.php:254
msgid "File %d"
msgstr ""

#: includes/REST/ProductAttributeController.php:99
#: includes/REST/ProductAttributeController.php:114
#: includes/REST/ProductAttributeController.php:140
msgid "Resource does not exist."
msgstr ""

#: includes/REST/ProductAttributeController.php:167
msgid "Attribute options."
msgstr ""

#: includes/REST/ProductAttributeController.php:175
msgid "Attribute id."
msgstr ""

#: includes/REST/ProductAttributeController.php:187
msgid "Attribute visible in product list page or not."
msgstr ""

#: includes/REST/ProductAttributeController.php:194
msgid "Attribute is for variation or not."
msgstr ""

#: includes/REST/ProductAttributeController.php:201
msgid "Attribute values."
msgstr ""

#: includes/REST/ProductAttributeController.php:224
#: includes/REST/ProductAttributeController.php:256
msgid "Invalid product id."
msgstr ""

#: includes/REST/ProductAttributeController.php:230
#: includes/REST/ProductAttributeController.php:262
msgid "No product found."
msgstr ""

#: includes/REST/ProductAttributeController.php:237
msgid "Failed to save product bulk attribute and terms. Please try again later."
msgstr ""

#: includes/REST/ProductAttributeController.php:269
msgid "Failed to save product default attribute and terms. Please try again later."
msgstr ""

#: includes/REST/ProductAttributeTermsController.php:89
msgid "The term cannot found"
msgstr ""

#: includes/REST/ProductAttributeTermsController.php:97
msgid "The resource cannot be deleted."
msgstr ""

#: includes/REST/ProductController.php:98
msgid "If truthy value then only downloadable products will be returned"
msgstr ""

#: includes/REST/ProductController.php:167
msgid "Whether to bypass trash and force deletion."
msgstr ""

#: includes/REST/ProductController.php:193
#: includes/REST/ProductController.php:210
#: includes/REST/ProductController.php:236
#: includes/REST/ProductController.php:262
#: includes/REST/ProductController.php:288
msgid "Number of product you want to get top rated product"
msgstr ""

#: includes/REST/ProductController.php:215
#: includes/REST/ProductController.php:241
#: includes/REST/ProductController.php:267
#: includes/REST/ProductController.php:293
msgid "Number of page number"
msgstr ""

#: includes/REST/ProductController.php:220
#: includes/REST/ProductController.php:246
#: includes/REST/ProductController.php:272
#: includes/REST/ProductController.php:298
msgid "Top rated product for specific vendor"
msgstr ""

#: includes/REST/ProductController.php:349
msgid "Error! Your account is not enabled for selling, please contact the admin"
msgstr ""

#: includes/REST/ProductController.php:358
msgid "Product title must be required"
msgstr ""

#: includes/REST/ProductController.php:364
msgid "Category must be required"
msgstr ""

#: includes/REST/ProductController.php:369
msgid "You can not select more than category"
msgstr ""

#: includes/REST/ProductController.php:1354
#: includes/REST/ProductController.php:1355
msgid "Placeholder"
msgstr ""

#. translators: %s: attachment id
#: includes/REST/ProductController.php:1572
msgid "#%s is an invalid image ID."
msgstr ""

#: includes/REST/ProductController.php:1882
msgid "Price formatted in HTML."
msgstr ""

#: includes/REST/ProductControllerV2.php:126
msgid "Products author id"
msgstr ""

#: includes/REST/ProductControllerV2.php:134
msgid "Product status publish, pending, draft etc."
msgstr ""

#: includes/REST/ProductControllerV2.php:142
msgid "Products publish month"
msgstr ""

#: includes/REST/ProductControllerV2.php:150
msgid "Products category."
msgstr ""

#: includes/REST/ProductControllerV2.php:158
msgid "Products type simple, variable, grouped product etc."
msgstr ""

#: includes/REST/ProductControllerV2.php:166
msgid "Products stock status in stock or out of stock."
msgstr ""

#: includes/REST/ProductControllerV2.php:174
msgid "Best selling, featured products etc."
msgstr ""

#: includes/REST/ProductControllerV2.php:181
msgid "Limit result set to specific ids."
msgstr ""

#: includes/REST/ProductControllerV2.php:190
msgid "Ensure result set excludes specific IDs."
msgstr ""

#: includes/REST/ProductControllerV2.php:322
msgid "All product created months."
msgstr ""

#: includes/REST/ProductControllerV2.php:329
msgid "Product publish year."
msgstr ""

#: includes/REST/ProductControllerV2.php:333
msgid "Product publish month."
msgstr ""

#: includes/REST/ProductControllerV2.php:337
msgid "Product publish month and year full title."
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:68
#: includes/REST/ReverseWithdrawalController.php:588
#: includes/REST/ReverseWithdrawalController.php:632
msgid "Vendor ID to filter form"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:132
msgid "Payable amount"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:597
#: includes/REST/ReverseWithdrawalController.php:648
msgid "Get transactions via date range"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:641
msgid "Transaction type to filter form"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:687
#: includes/REST/ReverseWithdrawalController.php:768
msgid "ID of the Store"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:693
#: includes/REST/ReverseWithdrawalController.php:783
msgid "Amount that site admin charged to store owner"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:699
#: includes/REST/ReverseWithdrawalController.php:789
msgid "Amount that has been paid via store owner to site admin"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:705
#: includes/REST/ReverseWithdrawalController.php:795
msgid "Amount currently site owners owns from store owner"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:711
#: includes/REST/ReverseWithdrawalController.php:755
msgid "Localized date of last payment received date."
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:736
msgid "ID of the transaction"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:742
#: templates/reverse-withdrawal/transaction-listing.php:18
#: assets/js/vue-admin.js:2
msgid "Transaction ID"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:748
msgid "Transaction URL."
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:761
msgid "Transaction type"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:776
msgid "Added note."
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:820
msgid "ID of the transaction type"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:826
msgid "Translated title of the transaction type"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:851
msgid "ID of the store"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:857
msgid "Name of the store"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:882
msgid "Due Status, true if vendor needs to pay, otherwise false"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:888
msgid "Due Date, immediate or any future date"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:894
#: assets/js/vue-admin.js:2
msgid "Current Balance"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:900
msgid "Formatted Current Balance"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:906
#: includes/ReverseWithdrawal/Admin/Settings.php:64
msgid "Billing Type"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:912
msgid "Formatted Billing Type"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:918
msgid "Billing Day"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:924
msgid "Due Period"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:930
msgid "Threshold Period"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:936
msgid "Formatted Threshold Amount"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:942
msgid "Payable Amount"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:948
msgid "Formatted Payable Amount"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:954
#: includes/ReverseWithdrawal/Admin/Settings.php:125
msgid "Display notice to pay reverse withdrawal balance during grace period under vendor dashboard."
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:960
msgid "Formatted Failed Actions"
msgstr ""

#: includes/REST/ReverseWithdrawalController.php:966
msgid "Formatted Action Taken Message"
msgstr ""

#: includes/REST/StoreController.php:80
msgid "Reassign the deleted user's posts and links to this user ID."
msgstr ""

#: includes/REST/StoreController.php:152
#: templates/widgets/store-contact-form.php:15
#: assets/js/vue-admin.js:2
msgid "Your Name"
msgstr ""

#: includes/REST/StoreController.php:158
msgid "Your email"
msgstr ""

#: includes/REST/StoreController.php:163
msgid "Your Message"
msgstr ""

#: includes/REST/StoreController.php:181
msgid "Status for the store object."
msgstr ""

#: includes/REST/StoreController.php:212
msgid "Get Best Selling Products Category."
msgstr ""

#: includes/REST/StoreController.php:339
msgid "Invalid user ID for reassignment."
msgstr ""

#: includes/REST/StoreController.php:538
#: includes/REST/StoreController.php:941
msgid "No store found"
msgstr ""

#: includes/REST/StoreController.php:556
#: includes/REST/StoreController.php:575
msgid "No reviews found"
msgstr ""

#: includes/REST/StoreController.php:767
msgid "This email address is not valid"
msgstr ""

#: includes/REST/StoreController.php:810
msgid "No vendor is found to be send an email."
msgstr ""

#: includes/REST/StoreController.php:857
msgid "Status parameter must be active or inactive"
msgstr ""

#: includes/REST/StoreController.php:863
msgid "No vendor found for updating status"
msgstr ""

#: includes/REST/StoreController.php:891
msgid "No items found for bulk updating"
msgstr ""

#: includes/REST/StoreController.php:975
msgid "Status of the store"
msgstr ""

#: includes/REST/StoreController.php:982
msgid "Store List Order By"
msgstr ""

#: includes/REST/StoreController.php:988
#: includes/REST/StoreController.php:994
msgid "Store List Order"
msgstr ""

#: includes/REST/StoreController.php:1014
msgid "Unique identifier for the vendor."
msgstr ""

#: includes/REST/StoreController.php:1020
msgid "Store owner first name."
msgstr ""

#: includes/REST/StoreController.php:1025
msgid "Store owner last name."
msgstr ""

#: includes/REST/StoreController.php:1030
msgid "User nice name."
msgstr ""

#: includes/REST/StoreController.php:1035
msgid "User password."
msgstr ""

#: includes/REST/StoreController.php:1039
msgid "Store email address."
msgstr ""

#: includes/REST/StoreController.php:1045
msgid "Store name."
msgstr ""

#: includes/REST/StoreController.php:1050
msgid "Store phone number."
msgstr ""

#: includes/REST/StoreController.php:1055
msgid "Show email in store."
msgstr ""

#: includes/REST/StoreController.php:1060
msgid "Gravatar ID."
msgstr ""

#: includes/REST/StoreController.php:1064
msgid "Banner ID."
msgstr ""

#: includes/REST/StoreController.php:1068
msgid "Enable terms and conditions."
msgstr ""

#: includes/REST/StoreController.php:1072
msgid "Store terms and conditions."
msgstr ""

#: includes/REST/StoreController.php:1076
msgid "Store icon."
msgstr ""

#: includes/REST/StoreController.php:1080
msgid "Social profiles."
msgstr ""

#: includes/REST/StoreController.php:1092
msgid "Store address."
msgstr ""

#: includes/REST/StoreController.php:1105
msgid "Enable selling."
msgstr ""

#: includes/REST/StoreController.php:1109
msgid "Featured vendor."
msgstr ""

#: includes/REST/StoreController.php:1113
msgid "Trusted vendor."
msgstr ""

#: includes/REST/StoreController.php:1117
msgid "Admin commission amount."
msgstr ""

#: includes/REST/StoreController.php:1121
msgid "Admin additional fee."
msgstr ""

#: includes/REST/StoreController.php:1125
msgid "Category wise commission."
msgstr ""

#: includes/REST/StoreSettingController.php:110
#: includes/REST/WithdrawController.php:329
#: includes/REST/WithdrawController.php:466
msgid "No vendor found"
msgstr ""

#: includes/REST/StoreSettingController.php:128
msgid "You are not logged in"
msgstr ""

#: includes/REST/StoreSettingControllerV2.php:55
#: includes/REST/StoreSettingControllerV2.php:79
#: includes/REST/StoreSettingControllerV2.php:108
msgid "Unique identifier for the settings group."
msgstr ""

#: includes/REST/StoreSettingControllerV2.php:84
msgid "Unique identifier for the setting."
msgstr ""

#: includes/REST/StoreSettingControllerV2.php:113
msgid "Unique identifier for the parent setting."
msgstr ""

#: includes/REST/StoreSettingControllerV2.php:118
msgid "Unique identifier for the setting field."
msgstr ""

#: includes/REST/StoreSettingControllerV2.php:236
msgid "Settings or fields value"
msgstr ""

#: includes/REST/StoreSettingControllerV2.php:258
msgid "Settings ID"
msgstr ""

#: includes/REST/StoreSettingControllerV2.php:263
msgid "Settings value"
msgstr ""

#: includes/REST/VendorDashboardController.php:66
msgid "From Date"
msgstr ""

#: includes/REST/VendorDashboardController.php:72
msgid "To Date"
msgstr ""

#: includes/REST/VendorDashboardController.php:77
msgid "Returns all sales reports if true"
msgstr ""

#: includes/REST/VendorDashboardController.php:84
msgid "Group By"
msgstr ""

#: includes/REST/VendorDashboardController.php:388
msgid "Payment currency."
msgstr ""

#: includes/REST/VendorDashboardController.php:394
msgid "Payment currency position."
msgstr ""

#: includes/REST/VendorDashboardController.php:400
msgid "Currency symbol."
msgstr ""

#: includes/REST/VendorDashboardController.php:406
msgid "Decimal separator."
msgstr ""

#: includes/REST/VendorDashboardController.php:412
msgid "Thousand separator."
msgstr ""

#: includes/REST/VendorDashboardController.php:418
msgid "Decimal point."
msgstr ""

#: includes/REST/VendorDashboardController.php:424
msgid "Tax Calculation enabled or not."
msgstr ""

#: includes/REST/VendorDashboardController.php:430
msgid "Tax display in cart price."
msgstr ""

#: includes/REST/VendorDashboardController.php:436
msgid "Tax Tax price round up in subtotal."
msgstr ""

#: includes/REST/VendorDashboardController.php:442
msgid "Coupon enabled in store."
msgstr ""

#: includes/REST/VendorDashboardController.php:448
msgid "Compound coupon calculation."
msgstr ""

#: includes/REST/VendorDashboardController.php:454
msgid "Measurement unit for weight."
msgstr ""

#: includes/REST/VendorDashboardController.php:460
msgid "Measurement unit for dimension."
msgstr ""

#: includes/REST/VendorDashboardController.php:466
msgid "Enabled product reviews."
msgstr ""

#: includes/REST/VendorDashboardController.php:472
msgid "Enabled product rating."
msgstr ""

#: includes/REST/VendorDashboardController.php:478
msgid "Enabled product stock management."
msgstr ""

#: includes/REST/VendorDashboardController.php:484
msgid "Store timezone."
msgstr ""

#: includes/REST/VendorDashboardController.php:490
msgid "Store date format."
msgstr ""

#: includes/REST/VendorDashboardController.php:496
msgid "Store time format."
msgstr ""

#: includes/REST/VendorDashboardController.php:502
msgid "Store language."
msgstr ""

#: includes/REST/VendorDashboardController.php:527
msgid "Start date to show orders"
msgstr ""

#: includes/REST/VendorDashboardController.php:534
msgid "End date to show orders"
msgstr ""

#: includes/REST/WithdrawController.php:51
msgid "IDs of withdraws"
msgstr ""

#: includes/REST/WithdrawController.php:59
msgid "Is withdraws exportable"
msgstr ""

#: includes/REST/WithdrawController.php:160
#: includes/REST/WithdrawControllerV2.php:56
msgid "Withdraw method key"
msgstr ""

#: includes/REST/WithdrawController.php:167
msgid "Withdraw amount"
msgstr ""

#: includes/REST/WithdrawController.php:287
msgid "Withdraw not found"
msgstr ""

#: includes/REST/WithdrawController.php:456
msgid "User does not have permission to withdraw"
msgstr ""

#: includes/REST/WithdrawController.php:470
msgid "You already have a pending withdraw request"
msgstr ""

#: includes/REST/WithdrawController.php:801
msgid "Invalid withdraw amount. The withdraw charge is greater than the withdraw amount"
msgstr ""

#: includes/REST/WithdrawController.php:964
msgid "Requested User"
msgstr ""

#: includes/REST/WithdrawController.php:971
msgid "Requested User ID"
msgstr ""

#: includes/REST/WithdrawController.php:977
msgid "The amount of discount. Should always be numeric, even if setting a percentage."
msgstr ""

#: includes/REST/WithdrawController.php:982
msgid "The date the withdraw request has beed created in the site's timezone."
msgstr ""

#: includes/REST/WithdrawController.php:989
msgid "Withdraw status"
msgstr ""

#: includes/REST/WithdrawController.php:997
#: templates/withdraw/request-form.php:15
msgid "Withdraw Method"
msgstr ""

#: includes/REST/WithdrawController.php:1004
msgid "Withdraw Notes"
msgstr ""

#: includes/REST/WithdrawController.php:1010
msgid "User IP"
msgstr ""

#: includes/REST/WithdrawController.php:1035
msgid "Unique identifier for the payment method."
msgstr ""

#: includes/REST/WithdrawController.php:1042
msgid "Title for the payment method."
msgstr ""

#: includes/REST/WithdrawController.php:1069
msgid "List of withdraw IDs to be approved"
msgstr ""

#: includes/REST/WithdrawController.php:1079
msgid "List of withdraw IDs to be cancelled"
msgstr ""

#: includes/REST/WithdrawController.php:1089
msgid "List of withdraw IDs to be deleted"
msgstr ""

#: includes/REST/WithdrawControllerV2.php:134
msgid "Please provide Withdraw method."
msgstr ""

#: includes/REST/WithdrawControllerV2.php:138
#: includes/Withdraw/Hooks.php:247
msgid "Method not active."
msgstr ""

#: includes/REST/WithdrawControllerV2.php:144
#: includes/Withdraw/Hooks.php:253
msgid "Default method update successful."
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:46
msgid "Enable Reverse Withdrawal"
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:47
msgid "Check this checkbox if you want to enable reverse withdrawal feature for vendors."
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:55
msgid "Enable Reverse Withdrawal for this Gateway"
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:56
msgid "Check the payment gateways you want to enable reverse withdrawal for. For now, only cash on delivery is available."
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:65
msgid "Select how vendors will be charged for their reverse balance."
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:73
msgid "Reverse Balance Threshold"
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:74
msgid "Set reverse withdrawal threshold limit."
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:88
msgid "Monthly Billing Date"
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:89
msgid "Enter the day of month when you want to send reverse withdrawal balance invoice to vendors."
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:104
msgid "Grace Period"
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:105
msgid "Maximum payment due period in day(s) before selected action(s) is/are taken. Enter 0 to take action(s) immediately."
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:115
msgid "After Grace Period"
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:116
msgid "Select one or more actions to perform after due period is over and vendors was unable to pay."
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:124
msgid "Display Notice During Grace Period"
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:136
msgid "Send Announcement?"
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:137
msgid "Check this checkbox if you want to send an announcement during the grace period. Note that a maximum of one announcement will be sent during a single billing period."
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:173
msgid "Please select a value for billing type field."
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:177
msgid "Due period cannot be negative or empty."
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:177
msgid "Due period cannot be greater than 28."
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:187
msgid "Please select at least one action."
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:195
msgid "Reverse balance threshold cannot be empty, zero or negative."
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:200
msgid "Monthly billing day cannot be empty or less than 1."
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:200
msgid "Monthly billing day cannot be greater than 28."
msgstr ""

#: includes/ReverseWithdrawal/Admin/Settings.php:211
msgid "Monthly billing day + due period cannot be greater than 28."
msgstr ""

#: includes/ReverseWithdrawal/Ajax.php:42
msgid "You do not have permission to use this action."
msgstr ""

#: includes/ReverseWithdrawal/Ajax.php:52
msgid "Product has been added to your cart."
msgstr ""

#. translators: 1) Product title
#: includes/ReverseWithdrawal/Cart.php:92
msgid "<strong>Error!</strong> Could not add product <strong>%1$s</strong> to cart. You can not purchase other products along with reverse withdrawal payment."
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:63
msgid "Opening Balance"
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:66
msgid "Failed Transfer Reversal"
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:67
msgid "Product Advertisement"
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:68
msgid "Manual Order Commission"
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:71
#: assets/js/dashboard-charts.js:1
#: assets/js/vue-admin.js:2
msgid "Refund"
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:72
#: includes/ReverseWithdrawal/Helper.php:73
msgid "Manual"
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:74
#: assets/js/vue-admin.js:2
msgid "Other"
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:362
#: includes/ReverseWithdrawal/Manager.php:373
#: includes/ReverseWithdrawal/Manager.php:409
msgid "No vendor id provided"
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:369
#: includes/ReverseWithdrawal/Helper.php:434
#: includes/ReverseWithdrawal/Helper.php:501
msgid "Invalid date provided"
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:490
msgid "Invalid vendor provided."
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:632
msgid "Your products add to cart will be hidden. Hence users will not be able to purchase any of your products."
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:636
msgid "Withdraw menu will be hidden. Hence you will not be able to make any withdraw request from your account."
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:640
msgid "Your account will be disabled for selling. Hence you will no longer be able to sell any products."
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:675
msgid "Your products add to cart button has been temporarily hidden. Hence users are not able to purchase any of your products currently."
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:679
msgid "Withdraw menu has been temporarily hidden. Hence you are not able to make any withdrawal requests from your account."
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:683
msgid "Your account has been temporarily disabled for selling. Hence you are no longer able to sell any products."
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:732
msgid "Payment can not be less than or equal to zero."
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:738
#: includes/ReverseWithdrawal/Helper.php:743
msgid "Invalid base payment product id. Please contact with site admin."
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:747
msgid "Base payment product status is not published. Please contact with site admin."
msgstr ""

#: includes/ReverseWithdrawal/Helper.php:769
#: includes/ReverseWithdrawal/Helper.php:776
msgid "Something went wrong while adding product to cart. Please contact site admin."
msgstr ""

#: includes/ReverseWithdrawal/Manager.php:494
msgid "No reverse withdrawal data found with given id."
msgstr ""

#: includes/ReverseWithdrawal/Manager.php:526
msgid "Transaction id is required."
msgstr ""

#: includes/ReverseWithdrawal/Manager.php:530
msgid "Invalid transaction type is provide. Please check your input."
msgstr ""

#: includes/ReverseWithdrawal/Manager.php:534
#: includes/ReverseWithdrawal/Manager.php:655
msgid "Invalid vendor id provide. Please provide a valid vendor id."
msgstr ""

#: includes/ReverseWithdrawal/Manager.php:564
msgid "Something went wrong while inserting reverse withdrawal data. Please contact site admin."
msgstr ""

#: includes/ReverseWithdrawal/Order.php:154
msgid "Payment Amount"
msgstr ""

#: includes/ReverseWithdrawal/ReverseWithdrawal.php:31
#: includes/Traits/ChainableContainer.php:21
msgid "Cloning is forbidden."
msgstr ""

#: includes/ReverseWithdrawal/ReverseWithdrawal.php:41
#: includes/Traits/ChainableContainer.php:31
msgid "Unserializing instances of this class is forbidden."
msgstr ""

#: includes/ReverseWithdrawal/SettingsHelper.php:160
msgid "Cash on delivery"
msgstr ""

#: includes/ReverseWithdrawal/SettingsHelper.php:174
msgid "By Amount Limit"
msgstr ""

#: includes/ReverseWithdrawal/SettingsHelper.php:175
msgid "Monthly"
msgstr ""

#: includes/ReverseWithdrawal/SettingsHelper.php:189
msgid "Disable Add to Cart Button"
msgstr ""

#: includes/ReverseWithdrawal/SettingsHelper.php:190
msgid "Hide Withdraw Menu"
msgstr ""

#: includes/ReverseWithdrawal/SettingsHelper.php:191
msgid "Make Vendor Status Inactive"
msgstr ""

#: includes/Shipping/Hooks.php:116
msgid "Shipping: "
msgstr ""

#. translators: 1) wooCommerce installation url
#: includes/Shortcodes/Dashboard.php:26
msgid "Please install <a href=\"%s\"><strong>WooCommerce</strong></a> plugin first"
msgstr ""

#: includes/Shortcodes/VendorRegistration.php:18
msgid "You are already logged in"
msgstr ""

#: includes/template-tags.php:50
msgctxt "Previous post link"
msgid "&larr;"
msgstr ""

#: includes/template-tags.php:53
msgctxt "Next post link"
msgid "&rarr;"
msgstr ""

#: includes/template-tags.php:121
#: includes/template-tags.php:125
#: templates/store-lists-loop.php:129
msgid "&larr; Previous"
msgstr ""

#: includes/template-tags.php:137
#: includes/template-tags.php:140
#: templates/store-lists-loop.php:130
msgid "Next &rarr;"
msgstr ""

#: includes/template-tags.php:160
msgid "Product successfully deleted"
msgstr ""

#: includes/template-tags.php:168
msgid "Product successfully duplicated"
msgstr ""

#: includes/template-tags.php:546
#: includes/Widgets/StoreCategoryMenu.php:37
#: includes/Widgets/StoreCategoryMenu.php:81
msgid "Store Product Category"
msgstr ""

#: includes/template-tags.php:559
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:238
#: includes/Widgets/StoreLocation.php:40
#: includes/Widgets/StoreLocation.php:101
msgid "Store Location"
msgstr ""

#: includes/template-tags.php:572
#: includes/Widgets/StoreOpenClose.php:39
#: includes/Widgets/StoreOpenClose.php:110
msgid "Store Time"
msgstr ""

#: includes/template-tags.php:585
#: includes/Widgets/StoreContactForm.php:40
#: includes/Widgets/StoreContactForm.php:117
msgid "Contact Vendor"
msgstr ""

#: includes/ThemeSupport/Divi.php:119
msgid "No Name"
msgstr ""

#: includes/Traits/AjaxResponseError.php:32
#: includes/Traits/RESTResponseError.php:30
#: assets/js/vue-admin.js:2
msgid "Something went wrong"
msgstr ""

#: includes/Upgrade/AdminNotice.php:39
msgid "Dokan Data Update Required"
msgstr ""

#: includes/Upgrade/AdminNotice.php:40
msgid "Updating your Dokan data is required to continue functional operations. Kindly backup your database before running upgrade for safety."
msgstr ""

#: includes/Upgrade/AdminNotice.php:46
#: templates/orders/details.php:194
msgid "Update"
msgstr ""

#: includes/Upgrade/AdminNotice.php:47
msgid "Updating..."
msgstr ""

#: includes/Upgrade/AdminNotice.php:48
msgid "Updated"
msgstr ""

#: includes/Upgrade/AdminNotice.php:50
msgid "It is strongly recommended that you backup your database before proceeding. Are you sure you wish to run the updater now?"
msgstr ""

#: includes/Upgrade/AdminNotice.php:74
msgid "You are not authorize to perform this operation."
msgstr ""

#: includes/Upgrade/AdminNotice.php:78
msgid "There is an upgrading process going on."
msgstr ""

#: includes/Upgrade/AdminNotice.php:82
msgid "Update is not required."
msgstr ""

#: includes/Upgrade/Upgrades/BackgroundProcesses/V_3_1_1_RefundTableUpdate.php:69
msgid "Upgrading db fields error: "
msgstr ""

#: includes/Utilities/AdminSettings.php:42
msgid "Automatically"
msgstr ""

#: includes/Utilities/AdminSettings.php:43
msgid "Manually"
msgstr ""

#: includes/Vendor/Manager.php:197
msgid "Unable to create vendor"
msgstr ""

#: includes/Vendor/Manager.php:287
msgid "Email is not valid"
msgstr ""

#: includes/Vendor/SettingsApi/Processor.php:115
#: includes/Vendor/SettingsApi/Processor.php:411
msgid "Setting Option not found"
msgstr ""

#: includes/Vendor/SettingsApi/Processor.php:255
msgid "Settings Element not found"
msgstr ""

#: includes/Vendor/SettingsApi/Processor.php:267
msgid "Settings Element Fields not found"
msgstr ""

#: includes/Vendor/SettingsApi/Processor.php:385
msgid "Setting group not found"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/Bank.php:29
#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/Bank.php:40
msgid "Bank"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/Bank.php:30
msgid "Bank settings."
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/Bank.php:41
msgid "Bank settings"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/Bank.php:52
msgid "Enter your bank account name."
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/Bank.php:60
msgid "Enter your bank account number."
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/Bank.php:68
msgid "Enter your bank name."
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/Bank.php:76
msgid "Enter your bank address."
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/Bank.php:84
msgid "Enter your bank routing number."
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/Bank.php:92
msgid "Enter your IBAN number."
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/Bank.php:100
msgid "Enter your banks Swift Code."
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/PayPal.php:28
msgid "Paypal"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/PayPal.php:29
msgid "Paypal settings."
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/PayPal.php:40
msgid "Paypal settings"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/PayPal.php:50
#: includes/Vendor/SetupWizard.php:337
#: templates/settings/store-form.php:153
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Email"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Gateways/PayPal.php:51
msgid "Enter your paypal email address"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Payments.php:44
msgid "Payment Settings"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Payments.php:45
msgid "Vendor Payment Settings"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Payments/Payments.php:61
msgid "The general Payment settings."
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:30
msgid "Store Settings"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:59
msgid "Branding"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:60
msgid "Set the overall appearance of your store by setting banner image, logo and more"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:70
msgid "Store Banner"
msgstr ""

#. translators: 1) store banner width, 2) store banner height.
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:72
msgid "Upload your store banner [ jpg or png, %1$d X %2$d pixels (max), 5 mb (max) ]"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:81
msgid "Store Gravatar"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:82
msgid "Upload your brand logo [ jpg or png, 150 X 150 pixels (max), 5mb (max) ]"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:96
msgid "Business Info"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:97
msgid "Provide your business details for store visitors"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:119
msgid "Insert Store Name"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:137
#: includes/Vendor/SetupWizard.php:201
#: templates/admin-setup-wizard/step-store.php:1
msgid "Store Setup"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:148
msgid "Store Address & Details"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:149
msgid "Store locations, contact information and more"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:152
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:405
#: assets/js/dokan-admin-dashboard.js:172
msgid "Docs"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:166
msgid "Enter your store phone"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:176
msgid "Provide your store locations to be displayed on the site."
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:186
#: includes/Vendor/SetupWizard.php:207
msgid "Street"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:187
#: templates/settings/address-form.php:44
msgid "Street address"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:194
msgid "Street Line 2"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:195
msgid "Street address continued"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:203
msgid "City name"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:211
msgid "Zip code"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:219
msgid "Select your country"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:228
msgid "State or state code"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:231
msgid "Select a state"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:239
msgid "Store Location GPS coordinate."
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:264
msgid "Store Open Close Timing"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:265
msgid "Set business operation hours "
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:276
msgid "Enable Store Time"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:277
msgid "Show store opening closing time widget in store page"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:282
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:429
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:445
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:461
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:477
#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:505
msgid "Yes"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:292
msgid "Store Operation Time"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:293
msgid "Your store operation time"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:303
msgid "Monday Schedule"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:311
msgid "Tuesday Schedule"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:319
msgid "Wednesday Schedule"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:327
msgid "Thursday Schedule"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:335
msgid "Friday Schedule"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:343
msgid "Saturday Schedule"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:351
msgid "Sunday Schedule"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:360
#: templates/settings/store-form.php:249
msgid "Store Open Notice"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:361
msgid "Store open notice for store visitors"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:363
msgid "Store is open and taking orders"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:371
#: templates/settings/store-form.php:257
msgid "Store Close Notice"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:372
msgid "Store close notice for store visitors"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:373
msgid "Store closed. Please contact for emergencies only"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:390
msgid "Advanced"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:401
msgid "Product Display Settings"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:402
msgid "Configure which product sections you want to display in your store page"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:423
msgid "Show featured products section"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:424
msgid "Allow your Featured Products section to be displayed in your single store page"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:439
msgid "Show latest products section"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:440
msgid "Allow your Latest Products section to be displayed in your single store page"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:455
msgid "Show best selling products section"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:456
msgid "Allow your Best Selling Products section to be displayed in your single store page"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:471
msgid "Show top rated products section"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:472
msgid "Allow your Top Rated Products section to be displayed in your single store page"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:499
msgid "Show Email"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:500
msgid "Do you want to display the store email publicly?"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:519
msgid "Define the rules of your store page by providing a detailed break down of the Terms and Conditions "
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:529
msgid "Display Terms & Condition"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:530
msgid "Enable Store Terms & Condition"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:535
#: templates/admin-setup-wizard/step-selling.php:24
#: templates/admin-setup-wizard/step-store.php:105
msgid "On"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:536
#: templates/admin-setup-wizard/step-selling.php:25
#: templates/admin-setup-wizard/step-store.php:106
msgid "Off"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:544
msgid "Terms & Condition"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:545
msgid "Store Terms & Condition"
msgstr ""

#: includes/Vendor/SettingsApi/Settings/Pages/Store.php:546
msgid "Insert your store Terms & Conditions"
msgstr ""

#: includes/Vendor/SetupWizard.php:125
msgid "Vendor &rsaquo; Setup Wizard"
msgstr ""

#: includes/Vendor/SetupWizard.php:149
msgid "Return to the Marketplace"
msgstr ""

#. translators: %1$s and %2$s are HTML tags for bold text
#: includes/Vendor/SetupWizard.php:162
msgid "Thank you for choosing The Marketplace to power your online store! This quick setup wizard will help you configure the basic settings. %1$sIt’s completely optional and shouldn’t take longer than two minutes.%2$s"
msgstr ""

#: includes/Vendor/SetupWizard.php:165
msgid "Welcome to the Marketplace!"
msgstr ""

#: includes/Vendor/SetupWizard.php:169
msgid "No time right now? If you don’t want to go through the wizard, you can skip and return to the Store!"
msgstr ""

#: includes/Vendor/SetupWizard.php:217
#: includes/Vendor/SetupWizard.php:234
#: includes/Vendor/SetupWizard.php:252
#: includes/Vendor/SetupWizard.php:269
#: includes/Vendor/SetupWizard.php:288
#: includes/Vendor/SetupWizard.php:306
#: includes/Vendor/SetupWizard.php:360
#: templates/settings/bank-payment-method-settings.php:30
#: templates/settings/bank-payment-method-settings.php:52
#: templates/settings/bank-payment-method-settings.php:69
#: templates/settings/bank-payment-method-settings.php:87
#: templates/settings/bank-payment-method-settings.php:105
#: templates/settings/bank-payment-method-settings.php:123
#: templates/settings/bank-payment-method-settings.php:141
#: templates/settings/bank-payment-method-settings.php:159
msgid "This is required"
msgstr ""

#: includes/Vendor/SetupWizard.php:226
#: templates/settings/address-form.php:52
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Street 2"
msgstr ""

#: includes/Vendor/SetupWizard.php:260
msgid "Post/Zip Code"
msgstr ""

#: includes/Vendor/SetupWizard.php:302
msgid "State Name"
msgstr ""

#: includes/Vendor/SetupWizard.php:319
#: templates/settings/store-form.php:173
msgid "Map"
msgstr ""

#: includes/Vendor/SetupWizard.php:341
#: templates/settings/store-form.php:158
msgid "Show email address in store"
msgstr ""

#: includes/Vendor/SetupWizard.php:373
msgid "Select an option&hellip;"
msgstr ""

#: includes/Vendor/SetupWizard.php:538
msgid "Payment Setup"
msgstr ""

#: includes/Vendor/SetupWizard.php:648
msgid "Your Store is Ready!"
msgstr ""

#: includes/Vendor/SetupWizard.php:653
msgid "Go to your Store Dashboard!"
msgstr ""

#: includes/Vendor/StoreListsFilter.php:83
msgid "Most Recent"
msgstr ""

#: includes/Vendor/StoreListsFilter.php:84
msgid "Most Popular"
msgstr ""

#: includes/Vendor/StoreListsFilter.php:85
msgid "Random"
msgstr ""

#. Translators: 1: user display name; 2: username;
#: includes/Vendor/UserSwitch.php:133
#: includes/Vendor/UserSwitch.php:183
msgid "Switched to %1$s (%2$s)."
msgstr ""

#. Translators: 1: user display name; 2: username;
#: includes/Vendor/UserSwitch.php:147
msgid "Switch back to %1$s (%2$s)"
msgstr ""

#. Translators: 1: user display name; 2: username;
#: includes/Vendor/UserSwitch.php:176
msgid "Switched back to %1$s (%2$s)."
msgstr ""

#: includes/Vendor/Vendor.php:926
msgid "No ratings found yet!"
msgstr ""

#: includes/Vendor/Vendor.php:928
msgid "%s rating from %d review"
msgid_plural "%s rating from %d reviews"
msgstr[0] ""
msgstr[1] ""

#: includes/Vendor/Vendor.php:929
msgid "Rated %s out of %d"
msgstr ""

#: includes/Vendor/Vendor.php:1029
#: templates/settings/store-form.php:252
msgid "Store is open"
msgstr ""

#: includes/Vendor/Vendor.php:1043
#: templates/settings/store-form.php:260
msgid "Store is closed"
msgstr ""

#: includes/VendorNavMenuChecker.php:254
msgid "Some of Dokan Templates or functionalities are overridden which limit new features."
msgstr ""

#: includes/VendorNavMenuChecker.php:255
msgid "Some of the Dokan templates or routes are overridden, which can prevent new features and intended functionalities from working correctly."
msgstr ""

#: includes/VendorNavMenuChecker.php:259
msgid "Learn More"
msgstr ""

#: includes/VendorNavMenuChecker.php:287
#: includes/VendorNavMenuChecker.php:314
msgid "Overridden Template Table"
msgstr ""

#: includes/VendorNavMenuChecker.php:290
msgid "Template"
msgstr ""

#: includes/VendorNavMenuChecker.php:305
msgid "Please Remove the above file to enable new features."
msgstr ""

#: includes/VendorNavMenuChecker.php:317
msgid "Route"
msgstr ""

#: includes/VendorNavMenuChecker.php:318
msgid "Override Status"
msgstr ""

#: includes/VendorNavMenuChecker.php:336
msgid "Forcefully enabled new feature."
msgstr ""

#: includes/VendorNavMenuChecker.php:336
msgid "Forcefully disabled new feature."
msgstr ""

#: includes/VendorNavMenuChecker.php:344
msgid "Overridden Templates or Routes"
msgstr ""

#: includes/VendorNavMenuChecker.php:345
msgid "The listed templates or vendor dashboard routes are currently overridden, which are preventing enabling new features."
msgstr ""

#: includes/wc-functions.php:390
msgid "Product SKU must be unique"
msgstr ""

#: includes/wc-functions.php:854
#: templates/account/update-customer-to-vendor.php:26
#: templates/account/vendor-registration.php:18
#: templates/global/seller-registration-form.php:16
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "First Name"
msgstr ""

#: includes/wc-functions.php:855
#: templates/account/update-customer-to-vendor.php:30
#: templates/account/vendor-registration.php:23
#: templates/global/seller-registration-form.php:21
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Last Name"
msgstr ""

#: includes/wc-functions.php:856
#: templates/account/vendor-registration.php:36
#: templates/dashboard/edit-account.php:65
msgid "Email address"
msgstr ""

#: includes/wc-functions.php:862
msgid "is a required field."
msgstr ""

#: includes/wc-functions.php:868
msgid "Please provide a valid email address."
msgstr ""

#: includes/wc-functions.php:870
msgid "This email address is already registered."
msgstr ""

#: includes/wc-functions.php:876
msgid "Your current password is incorrect."
msgstr ""

#: includes/wc-functions.php:881
msgid "Please fill out all password fields."
msgstr ""

#: includes/wc-functions.php:884
msgid "Please enter your current password."
msgstr ""

#: includes/wc-functions.php:887
msgid "Please re-enter your password."
msgstr ""

#: includes/wc-functions.php:890
msgid "New passwords do not match."
msgstr ""

#: includes/wc-functions.php:910
msgid "Account details changed successfully."
msgstr ""

#: includes/wc-functions.php:956
msgid "More Products"
msgstr ""

#: includes/wc-functions.php:1009
msgid "No product has been found!"
msgstr ""

#: includes/wc-functions.php:1082
msgid "Reviews cannot be posted for products that you own."
msgstr ""

#: includes/wc-template.php:181
#: templates/products/dokan-products-edit-bulk-commission.php:20
msgid "— No change —"
msgstr ""

#: includes/wc-template.php:308
msgid "Go to Vendor Dashboard"
msgstr ""

#: includes/Widgets/BestSellingProducts.php:66
msgid "Best Selling Product"
msgstr ""

#: includes/Widgets/BestSellingProducts.php:73
#: includes/Widgets/FilterByAttributes.php:110
#: includes/Widgets/ProductCategoryMenu.php:96
#: includes/Widgets/StoreCategoryMenu.php:88
#: includes/Widgets/StoreContactForm.php:124
#: includes/Widgets/StoreLocation.php:108
#: includes/Widgets/StoreOpenClose.php:117
#: includes/Widgets/TopratedProducts.php:75
#: assets/js/dokan-intelligence.js:172
msgid "Title:"
msgstr ""

#: includes/Widgets/BestSellingProducts.php:77
#: includes/Widgets/TopratedProducts.php:79
msgid "No of Product:"
msgstr ""

#: includes/Widgets/BestSellingProducts.php:82
#: includes/Widgets/TopratedProducts.php:84
msgid "Show Product Rating"
msgstr ""

#: includes/Widgets/BestSellingProducts.php:86
msgid "Hide Out of Stock"
msgstr ""

#: includes/Widgets/FilterByAttributes.php:15
msgid "Dokan: Filter Products by Attribute Widget"
msgstr ""

#: includes/Widgets/FilterByAttributes.php:17
msgid "A Widget for displaying products by attribute for dokan"
msgstr ""

#: includes/Widgets/FilterByAttributes.php:85
msgid "Filter by"
msgstr ""

#: includes/Widgets/FilterByAttributes.php:103
msgid "AND"
msgstr ""

#: includes/Widgets/FilterByAttributes.php:104
msgid "OR"
msgstr ""

#: includes/Widgets/FilterByAttributes.php:114
msgid "Attribute"
msgstr ""

#: includes/Widgets/FilterByAttributes.php:122
msgid "Query Type"
msgstr ""

#: includes/Widgets/ProductCategoryMenu.php:18
msgid "Dokan product category menu"
msgstr ""

#: includes/Widgets/ProductCategoryMenu.php:89
msgid "Product Category"
msgstr ""

#: includes/Widgets/StoreCategoryMenu.php:17
msgid "Dokan Seller Store Menu"
msgstr ""

#: includes/Widgets/StoreCategoryMenu.php:19
msgid "Dokan: Store Product Category Menu"
msgstr ""

#: includes/Widgets/StoreContactForm.php:24
msgid "Dokan Vendor Contact Form"
msgstr ""

#: includes/Widgets/StoreContactForm.php:26
msgid "Dokan: Store Contact Form"
msgstr ""

#: includes/Widgets/StoreLocation.php:24
msgid "Dokan Vendor Store Location"
msgstr ""

#: includes/Widgets/StoreLocation.php:26
msgid "Dokan: Store Location"
msgstr ""

#: includes/Widgets/StoreOpenClose.php:23
msgid "Dokan Store Opening Closing Time"
msgstr ""

#: includes/Widgets/StoreOpenClose.php:25
msgid "Dokan: Store Opening Closing Time Widget"
msgstr ""

#: includes/Widgets/TopratedProducts.php:14
msgid "Dokan: Top Rated Product Widget"
msgstr ""

#: includes/Widgets/TopratedProducts.php:16
msgid "A Widget for displaying To rated products for dokan"
msgstr ""

#: includes/Widgets/TopratedProducts.php:68
msgid "Top Rated Product"
msgstr ""

#: includes/Withdraw/functions.php:133
#: includes/Withdraw/functions.php:164
#: assets/js/vue-admin.js:2
msgid "E-mail"
msgstr ""

#: includes/Withdraw/functions.php:143
#: templates/settings/bank-payment-method-settings.php:207
msgid "Disconnect"
msgstr ""

#: includes/Withdraw/functions.php:189
#: assets/js/vue-admin.js:2
msgid "Save"
msgstr ""

#: includes/Withdraw/functions.php:189
msgid "Add Account"
msgstr ""

#: includes/Withdraw/functions.php:221
msgid "Account holder name is required"
msgstr ""

#: includes/Withdraw/functions.php:222
msgid "Please select account type"
msgstr ""

#: includes/Withdraw/functions.php:223
msgid "Account number is required"
msgstr ""

#: includes/Withdraw/functions.php:224
msgid "Routing number is required"
msgstr ""

#: includes/Withdraw/functions.php:243
msgid "This field is required."
msgstr ""

#: includes/Withdraw/functions.php:285
msgid "Account Holder"
msgstr ""

#: includes/Withdraw/functions.php:286
msgid "Your bank account name"
msgstr ""

#: includes/Withdraw/functions.php:289
#: includes/Withdraw/functions.php:290
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Account Type"
msgstr ""

#: includes/Withdraw/functions.php:302
msgid "Name of bank"
msgstr ""

#: includes/Withdraw/functions.php:306
msgid "Address of your bank"
msgstr ""

#: includes/Withdraw/functions.php:313
msgid "Bank Swift Code"
msgstr ""

#: includes/Withdraw/functions.php:317
msgid "I attest that I am the owner and have full authorization to this bank account"
msgstr ""

#: includes/Withdraw/functions.php:321
msgid "Please double-check your account information!"
msgstr ""

#: includes/Withdraw/functions.php:322
msgid "Incorrect or mismatched account name and number can result in withdrawal delays and fees"
msgstr ""

#: includes/Withdraw/functions.php:415
msgid "No information found."
msgstr ""

#. translators: 1: Email address for withdraw method.
#: includes/Withdraw/functions.php:421
msgid "( %1$s )"
msgstr ""

#. translators: 1: Bank account holder name. 2: Bank name. 1: Bank account number
#: includes/Withdraw/functions.php:425
msgid "- %1$s - %2$s - ****%3$s"
msgstr ""

#: includes/Withdraw/Hooks.php:169
msgid "You already have a pending withdraw request."
msgstr ""

#: includes/Withdraw/Hooks.php:173
msgid "Withdraw method is required"
msgstr ""

#: includes/Withdraw/Hooks.php:177
#: assets/js/frontend.js:172
msgid "Withdraw amount is required"
msgstr ""

#: includes/Withdraw/Hooks.php:184
msgid "Withdraw method is not activated."
msgstr ""

#: includes/Withdraw/Hooks.php:188
msgid "Negative withdraw amount is not permitted."
msgstr ""

#: includes/Withdraw/Hooks.php:222
msgid "Withdraw request successful."
msgstr ""

#: includes/Withdraw/Hooks.php:243
msgid "Please provide Withdrew method."
msgstr ""

#: includes/Withdraw/Manager.php:41
msgid "Withdraw amount required "
msgstr ""

#: includes/Withdraw/Manager.php:45
msgid "You don't have enough balance for this request"
msgstr ""

#. translators: %s: withdraw limit amount
#: includes/Withdraw/Manager.php:50
msgid "Withdraw amount must be greater than or equal to %s"
msgstr ""

#: includes/Withdraw/Manager.php:54
msgid "Withdraw method is not active."
msgstr ""

#: includes/Withdraw/Manager.php:58
msgid "Vendor is not enabled for selling, please contact site admin"
msgstr ""

#: includes/Withdraw/Manager.php:62
msgid "Withdraw is already approved."
msgstr ""

#: includes/Withdraw/Manager.php:74
msgid "Withdraw amount is less then the withdraw charge."
msgstr ""

#: includes/Withdraw/Manager.php:111
msgid "Invalid permission to cancel the request."
msgstr ""

#: includes/Withdraw/Manager.php:117
msgid "Invalid cancel withdraw request"
msgstr ""

#: includes/Withdraw/Manager.php:157
msgid "Could not update withdraw status"
msgstr ""

#: includes/Withdraw/Manager.php:191
msgid "Could not add new withdraw approval request."
msgstr ""

#: includes/Withdraw/Withdraw.php:467
msgid "Could not create new withdraw"
msgstr ""

#: includes/Withdraw/Withdraw.php:513
msgid "Could not update withdraw"
msgstr ""

#: includes/Withdraw/Withdraw.php:554
msgid "Could not delete withdraw"
msgstr ""

#: includes/woo-views/html-product-download.php:3
msgid "File Name"
msgstr ""

#: includes/woo-views/html-product-download.php:8
msgid "Choose file"
msgstr ""

#: templates/account/become-a-vendor-section.php:16
#: templates/account/become-a-vendor-section.php:20
#: templates/account/update-customer-to-vendor.php:78
msgid "Become a Vendor"
msgstr ""

#: templates/account/become-a-vendor-section.php:17
msgid "Vendors can sell products and manage a store with a vendor dashboard."
msgstr ""

#: templates/account/update-customer-to-vendor.php:21
msgid "Update account to Vendor"
msgstr ""

#: templates/account/update-customer-to-vendor.php:36
#: templates/account/vendor-registration.php:61
#: templates/global/seller-registration-form.php:27
msgid "Shop Name"
msgstr ""

#: templates/account/update-customer-to-vendor.php:41
#: templates/account/vendor-registration.php:66
#: templates/global/seller-registration-form.php:32
msgid "Shop URL"
msgstr ""

#: templates/account/update-customer-to-vendor.php:67
msgid "Terms &amp; Conditions"
msgstr ""

#. translators: 1. Terms and conditions of agreement link.
#: templates/account/update-customer-to-vendor.php:69
msgid "I have read and agree to the %1$s."
msgstr ""

#: templates/account/vendor-registration.php:30
#: templates/login-form/login-form.php:4
#: assets/js/dashboard-charts.js:1
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Username"
msgstr ""

#: templates/account/vendor-registration.php:40
msgid "A link to set a new password will be sent to your email address."
msgstr ""

#: templates/account/vendor-registration.php:52
#: templates/login-form/login-form.php:9
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Password"
msgstr ""

#: templates/account/vendor-registration.php:58
msgid "Anti-spam"
msgstr ""

#. translators: %1$s: opening anchor tag with link, %2$s: an ampersand %3$s: closing anchor tag
#: templates/account/vendor-registration.php:101
#: templates/global/seller-registration-form.php:70
msgid "I have read and agree to the %1$sTerms %2$s Conditions%3$s."
msgstr ""

#: templates/account/vendor-registration.php:116
msgid "Register"
msgstr ""

#: templates/admin-header.php:47
msgid "Upgrade"
msgstr ""

#: templates/admin-header.php:75
#: assets/js/dokan-admin-dashboard.js:172
msgid "Get Help"
msgstr ""

#: templates/admin-header.php:90
msgid "What’s New"
msgstr ""

#: templates/admin-notice-dependencies.php:23
msgid "WooCommerce Missing"
msgstr ""

#. translators: 1$-2$: opening and closing <strong> tags, 3$-4$: link tags, takes to woocommerce plugin on wp.org, 5$-6$: opening and closing link tags, leads to plugins.php in admin
#: templates/admin-notice-dependencies.php:34
msgid "%1$sDokan is inactive.%2$s The %3$sWooCommerce plugin%4$s must be active for Dokan to work. Please %5$s install WooCommerce &raquo;%6$s"
msgstr ""

#. translators: 1$-2$: opening and closing <strong> tags, 3$-4$: link tags, takes to woocommerce plugin on wp.org, 5$-6$: opening and closing link tags, leads to plugins.php in admin
#: templates/admin-notice-dependencies.php:46
msgid "%1$sDokan is inactive.%2$s The %3$sWooCommerce plugin%4$s must be active for Dokan to work. Please %5$s activate WooCommerce &raquo;%6$s"
msgstr ""

#: templates/admin-setup-wizard/step-commission.php:7
msgid "Commission Setup"
msgstr ""

#. translators: %1$s: line break and opening strong tag, %2$s: closing strong tag
#: templates/admin-setup-wizard/step-no-wc-introduction.php:8
msgid "Thanks for choosing Dokan to power your online marketplace! This quick setup wizard will help you configure the basic settings. %1$sThis setup wizard is completely optional and shouldn't take longer than three minutes.%2$s"
msgstr ""

#: templates/admin-setup-wizard/step-no-wc-introduction.php:24
msgid "Should you choose to skip the setup wizard, you can always setup Dokan manually or come back here and complete the setup via the Wizard."
msgstr ""

#. translators: %1$s: opening anchor tag with WooCommerce plugin link, %2$s: closing anchor tag, %3$s: opening anchor tag with php update link
#: templates/admin-setup-wizard/step-no-wc-introduction.php:37
msgid "Please note that %1$sWooCommerce%2$s is necessary for Dokan to work but the current PHP version does not meet minimum requirements for WooCommerce. %3$sPlease learn more about updating PHP%2$s"
msgstr ""

#. translators: %1$s: opening anchor tag with WooCommerce plugin link, %2$s: closing anchor tag
#: templates/admin-setup-wizard/step-no-wc-introduction.php:58
msgid "Please note that %1$sWooCommerce%2$s is necessary for Dokan to work and it will be automatically installed if you haven't already done so."
msgstr ""

#: templates/admin-setup-wizard/step-selling.php:1
msgid "Selling Setup"
msgstr ""

#: templates/admin-setup-wizard/step-selling.php:5
msgid "New Vendor Enable Selling"
msgstr ""

#: templates/admin-setup-wizard/step-selling.php:15
msgid "Set selling status for newly registered vendor"
msgstr ""

#: templates/admin-setup-wizard/step-selling.php:28
msgid "Vendor can change order status"
msgstr ""

#: templates/admin-setup-wizard/step-store-wc-fields.php:22
msgid "Where is your store based?"
msgstr ""

#: templates/admin-setup-wizard/step-store-wc-fields.php:24
msgid "Choose a country&hellip;"
msgstr ""

#: templates/admin-setup-wizard/step-store-wc-fields.php:59
msgid "Choose a state&hellip;"
msgstr ""

#: templates/admin-setup-wizard/step-store-wc-fields.php:64
msgid "Postcode / ZIP"
msgstr ""

#: templates/admin-setup-wizard/step-store-wc-fields.php:73
msgid "What currency do you accept payments in?"
msgstr ""

#: templates/admin-setup-wizard/step-store-wc-fields.php:81
#: templates/admin-setup-wizard/step-store-wc-fields.php:84
msgid "Choose a currency&hellip;"
msgstr ""

#. translators: 1: currency name 2: currency symbol, 3: currency code
#: templates/admin-setup-wizard/step-store-wc-fields.php:95
msgid "%1$s (%2$s %3$s)"
msgstr ""

#: templates/admin-setup-wizard/step-store-wc-fields.php:111
msgid "What type of products do you plan to sell?"
msgstr ""

#: templates/admin-setup-wizard/step-store-wc-fields.php:117
msgid "Physical"
msgstr ""

#: templates/admin-setup-wizard/step-store-wc-fields.php:118
msgid "Digital"
msgstr ""

#: templates/admin-setup-wizard/step-store-wc-fields.php:119
msgid "Both"
msgstr ""

#: templates/admin-setup-wizard/step-store-wc-fields.php:124
#: templates/admin-setup-wizard/step-store-wc-fields.php:125
msgid "I will also be selling products or services in person."
msgstr ""

#: templates/admin-setup-wizard/step-store.php:10
msgid "Define vendor store URL"
msgstr ""

#: templates/admin-setup-wizard/step-store.php:23
msgid "Shipping fees will go to"
msgstr ""

#: templates/admin-setup-wizard/step-store.php:36
msgid "Product Tax fees will go to"
msgstr ""

#: templates/admin-setup-wizard/step-store.php:51
msgid "Shipping Tax fees will go to"
msgstr ""

#: templates/admin-setup-wizard/step-store.php:64
msgid "Which Map API source you want to use in your site?"
msgstr ""

#. translators: %1$s: opening anchor tag, %2$s: closing anchor tag
#: templates/admin-setup-wizard/step-store.php:75
msgid "%1$sAPI Key%2$s is needed to display map on store page"
msgstr ""

#. translators: %1$s: opening anchor tag, %2$s: closing anchor tag
#: templates/admin-setup-wizard/step-store.php:91
msgid "%1$sAccess Token%2$s is needed to display map on store page"
msgstr ""

#: templates/admin-setup-wizard/step-store.php:100
msgid "Share Essentials"
msgstr ""

#. translators: %1$s: opening anchor tag with link, %2$s: closing anchor tag
#: templates/admin-setup-wizard/step-store.php:113
msgid "Want to help make Dokan even more awesome? Allow Dokan Multivendor Marketplace to collect non-sensitive diagnostic data and usage information. %1$sWhat we collect%2$s"
msgstr ""

#. translators: %1$s: Appsero hypertext with link, %2$s: opening anchor tag with link, %3$s: closing anchor tag
#: templates/admin-setup-wizard/step-store.php:132
msgid "Server environment details (php, mysql, server, WordPress versions), Number of users in your site, Site language, Number of active and inactive plugins, Site name and url, Your name and email address. No sensitive data is tracked. We are using %1$s to collect your data. %2$sLearn more%3$s about how %1$s collects and handles your data."
msgstr ""

#: templates/dashboard/big-counter-widget.php:17
msgid "Net Sales"
msgstr ""

#: templates/dashboard/big-counter-widget.php:21
#: templates/orders/listing.php:32
#: templates/orders/listing.php:80
#: templates/products/products-listing-row.php:225
#: templates/products/products-listing.php:128
msgid "Earning"
msgstr ""

#: templates/dashboard/big-counter-widget.php:25
msgid "Pageview"
msgstr ""

#: templates/dashboard/edit-account.php:47
msgid "Edit Account Details"
msgstr ""

#: templates/dashboard/edit-account.php:54
msgid "First name"
msgstr ""

#: templates/dashboard/edit-account.php:59
msgid "Last name"
msgstr ""

#: templates/dashboard/edit-account.php:70
msgid "Password Change"
msgstr ""

#: templates/dashboard/edit-account.php:73
msgid "Current Password (leave blank to leave unchanged)"
msgstr ""

#: templates/dashboard/edit-account.php:78
msgid "New Password (leave blank to leave unchanged)"
msgstr ""

#: templates/dashboard/edit-account.php:83
msgid "Confirm New Password"
msgstr ""

#: templates/dashboard/edit-account.php:94
msgid "Save changes"
msgstr ""

#: templates/dashboard/orders-widget.php:58
msgid "On hold"
msgstr ""

#: templates/dashboard/products-widget.php:25
msgid "+ Add new product"
msgstr ""

#: templates/dashboard/products-widget.php:37
msgid "Live"
msgstr ""

#: templates/dashboard/products-widget.php:42
msgid "Offline"
msgstr ""

#: templates/dashboard/sales-chart-widget.php:15
msgid "Sales this Month"
msgstr ""

#. translators: 1) customer name 2) customer email
#: templates/emails/contact-seller.php:21
#: templates/emails/plain/contact-seller.php:20
msgid "From : %1$s (%2$s)"
msgstr ""

#: templates/emails/new-product-pending.php:17
#: templates/emails/new-product.php:17
msgid "Hello,"
msgstr ""

#: templates/emails/new-product-pending.php:19
msgid "A new product is submitted to your site and is pending review"
msgstr ""

#: templates/emails/new-product-pending.php:20
#: templates/emails/new-product.php:20
#: templates/emails/plain/new-product-pending.php:25
#: templates/emails/plain/new-product.php:25
msgid "Summary of the product:"
msgstr ""

#: templates/emails/new-product-pending.php:25
#: templates/emails/new-product.php:25
msgid "Title :"
msgstr ""

#: templates/emails/new-product-pending.php:31
#: templates/emails/new-product.php:31
msgid "Price :"
msgstr ""

#: templates/emails/new-product-pending.php:37
#: templates/emails/new-product.php:37
#: templates/emails/new-seller-registered.php:29
msgid "Vendor :"
msgstr ""

#: templates/emails/new-product-pending.php:43
#: templates/emails/new-product.php:43
msgid "Category :"
msgstr ""

#: templates/emails/new-product-pending.php:49
msgid "The product is currently in \"pending\" status."
msgstr ""

#: templates/emails/new-product.php:19
msgid "A new product is submitted to your site"
msgstr ""

#: templates/emails/new-product.php:49
msgid "The product is currently in \"publish\" status. So, everyone can view the product."
msgstr ""

#: templates/emails/new-seller-registered.php:17
#: templates/emails/plain/new-product-pending.php:19
#: templates/emails/plain/new-product.php:19
#: templates/emails/plain/new-seller-registered.php:19
#: templates/emails/plain/vendor-product-review.php:22
#: templates/emails/vendor-product-review.php:19
msgid "Hello there,"
msgstr ""

#: templates/emails/new-seller-registered.php:19
msgid "A new vendor has registered in your marketplace "
msgstr ""

#: templates/emails/new-seller-registered.php:23
#: templates/emails/plain/new-seller-registered.php:25
msgid "Vendor Details:"
msgstr ""

#: templates/emails/new-seller-registered.php:35
msgid "Vendor Store :"
msgstr ""

#. translators: 1) seller edit url
#: templates/emails/new-seller-registered.php:43
msgid "To edit vendor access and details <a href=\"%s\">Click Here</a>"
msgstr ""

#: templates/emails/plain/new-product-pending.php:22
msgid "A new product is submitted to your site and pending review"
msgstr ""

#. translators: 1) product title
#: templates/emails/plain/new-product-pending.php:30
msgid "Title: %s"
msgstr ""

#. translators: 1) product price
#: templates/emails/plain/new-product-pending.php:34
#: templates/emails/plain/new-product.php:34
msgid "Price: %1$s"
msgstr ""

#. translators: 1) product seller name
#: templates/emails/plain/new-product-pending.php:38
#: templates/emails/plain/new-product.php:38
msgid "Vendor: %1$s"
msgstr ""

#. translators: 1) product category
#: templates/emails/plain/new-product-pending.php:42
#: templates/emails/plain/new-product.php:42
msgid "Category: %1$s"
msgstr ""

#: templates/emails/plain/new-product-pending.php:45
msgid "The product is currently in \"pending\" state."
msgstr ""

#: templates/emails/plain/new-product-pending.php:48
msgid "In case it needs to be moderated, please the follow URL below."
msgstr ""

#: templates/emails/plain/new-product.php:22
msgid "A new product is submitted to your site."
msgstr ""

#. translators: 1) product title
#: templates/emails/plain/new-product.php:30
msgid "Title: %1$s"
msgstr ""

#: templates/emails/plain/new-product.php:45
msgid "The product is currently in \"publish\" state. So everyone can view the product."
msgstr ""

#: templates/emails/plain/new-product.php:48
msgid "In case it needs to be moderated, please follow the URL below."
msgstr ""

#: templates/emails/plain/new-seller-registered.php:22
msgid "A new vendor has registered in your marketplace  "
msgstr ""

#. translators: 1) seller name
#: templates/emails/plain/new-seller-registered.php:31
msgid "Vendor: %s"
msgstr ""

#. translators: 1) store name
#: templates/emails/plain/new-seller-registered.php:35
msgid "Vendor Store: %s"
msgstr ""

#. translators: 1) seller edit url
#: templates/emails/plain/new-seller-registered.php:39
msgid "To edit vendor access and details visit : %s"
msgstr ""

#. translators: 1) Seller name
#. translators: 1) seller name
#: templates/emails/plain/product-published.php:20
#: templates/emails/product-published.php:19
msgid "Hello %s"
msgstr ""

#. translators: 1) product title
#: templates/emails/plain/product-published.php:24
msgid "Your product %s"
msgstr ""

#. translators: 1) product title
#: templates/emails/plain/product-published.php:25
msgid " has been approved by one of our admin, congrats!"
msgstr ""

#. translators: 1) product url
#: templates/emails/plain/product-published.php:29
msgid "View product : %s"
msgstr ""

#. translators: 1) product edit url
#: templates/emails/plain/product-published.php:33
msgid "Update : %s"
msgstr ""

#. translators: 1) store name
#: templates/emails/plain/reverse-withdrawal-invoice.php:27
msgid "Hi %s,\\n\\n"
msgstr ""

#. translators: 1) invoice month 2) invoice year 3) store name
#: templates/emails/plain/reverse-withdrawal-invoice.php:30
msgid "Your %1$s %2$s invoice is now available for store: %3$s.\\n\\n"
msgstr ""

#. translators: 1) store name
#: templates/emails/plain/reverse-withdrawal-invoice.php:33
msgid "Summary for %1$s: \\n\\n"
msgstr ""

#. translators: 1) invoice month 2) invoice year 3) due balance amount
#: templates/emails/plain/reverse-withdrawal-invoice.php:36
msgid "Reverse withdrawal charges for %1$s %2$s: %3$s \\n\\n"
msgstr ""

#. translators: 1) invoice due date
#: templates/emails/plain/reverse-withdrawal-invoice.php:39
msgid "Due Date %1$s: \\n\\n"
msgstr ""

#: templates/emails/plain/reverse-withdrawal-invoice.php:43
#: templates/emails/reverse-withdrawal-invoice.php:73
#: templates/reverse-withdrawal/reverse-balance.php:45
msgid "Pay Now"
msgstr ""

#. translators: 1) order billing full name
#: templates/emails/plain/vendor-completed-order.php:20
#: templates/emails/vendor-completed-order.php:37
msgid "You have received complete order from %s."
msgstr ""

#: templates/emails/plain/vendor-completed-order.php:23
#: templates/emails/plain/vendor-new-order.php:23
msgid "Product            | Quantity        | Price"
msgstr ""

#: templates/emails/plain/vendor-completed-order.php:47
#: templates/emails/plain/vendor-new-order.php:47
#: templates/emails/vendor-completed-order.php:92
#: templates/emails/vendor-new-order.php:93
#: templates/sub-orders.php:23
msgid "Note:"
msgstr ""

#. translators: 1) order billing full name
#. translators: 1) order formatted billing full name
#: templates/emails/plain/vendor-new-order.php:20
#: templates/emails/vendor-new-order.php:36
msgid "You have received an order from %s."
msgstr ""

#. translators: 1) product name, 2) customer name, 3) rating
#: templates/emails/plain/vendor-product-review.php:27
msgid "We are happy to inform you that your product %1$s has received a new review on our website. The review was written by %2$s and has a rating of %3$s out of 5 stars."
msgstr ""

#: templates/emails/plain/vendor-product-review.php:34
#: templates/emails/vendor-product-review.php:35
msgid "The review text is as follows:"
msgstr ""

#: templates/emails/plain/vendor-product-review.php:42
#: templates/emails/vendor-product-review.php:39
msgid "You can view the review by visiting the following link:"
msgstr ""

#: templates/emails/plain/vendor-product-review.php:51
#: templates/emails/vendor-product-review.php:50
msgid "We appreciate your participation in our platform and hope that you will continue to offer quality products and services to our customers."
msgstr ""

#: templates/emails/plain/vendor-product-review.php:55
#: templates/emails/vendor-product-review.php:51
msgid "Thank you for your attention."
msgstr ""

#. translators: 1) user name
#. translators: user name
#: templates/emails/plain/withdraw-approve.php:20
#: templates/emails/plain/withdraw-cancel.php:20
#: templates/emails/withdraw-approve.php:19
#: templates/emails/withdraw-cancel.php:19
msgid "Hi %s"
msgstr ""

#: templates/emails/plain/withdraw-approve.php:23
#: templates/emails/withdraw-approve.php:23
msgid "Your withdraw request has been approved, congrats!"
msgstr ""

#: templates/emails/plain/withdraw-approve.php:26
#: templates/emails/plain/withdraw-cancel.php:26
#: templates/emails/withdraw-approve.php:26
#: templates/emails/withdraw-cancel.php:26
msgid "You sent a withdraw request of:"
msgstr ""

#. translators: 1) withdraw amount
#: templates/emails/plain/withdraw-approve.php:30
#: templates/emails/plain/withdraw-cancel.php:30
#: templates/emails/withdraw-cancel.php:30
msgid "Amount : %s"
msgstr ""

#. translators: 1) withdraw method
#. translators: 1) withdraw method title
#: templates/emails/plain/withdraw-approve.php:34
#: templates/emails/plain/withdraw-cancel.php:34
#: templates/emails/withdraw-approve.php:38
#: templates/emails/withdraw-cancel.php:40
msgid "Method : %s"
msgstr ""

#: templates/emails/plain/withdraw-approve.php:37
#: templates/emails/withdraw-approve.php:42
msgid "We'll transfer this amount to your preferred withdrawal method shortly."
msgstr ""

#: templates/emails/plain/withdraw-approve.php:40
#: templates/emails/withdraw-approve.php:44
msgid "Thanks for being with us."
msgstr ""

#: templates/emails/plain/withdraw-cancel.php:23
msgid "Your withdraw request was cancelled"
msgstr ""

#: templates/emails/plain/withdraw-cancel.php:37
#: templates/emails/withdraw-cancel.php:44
msgid "Here's the reason, why : "
msgstr ""

#: templates/emails/plain/withdraw-new.php:19
#: templates/emails/withdraw-new.php:17
msgid "Hi,"
msgstr ""

#. translators: 1) user name
#: templates/emails/plain/withdraw-new.php:23
msgid "A new withdraw request has been made by - %s"
msgstr ""

#. translators: 1) withdraw request amount
#: templates/emails/plain/withdraw-new.php:27
msgid "Request Amount : %s"
msgstr ""

#. translators: 1) withdraw payment method
#: templates/emails/plain/withdraw-new.php:31
msgid "Payment Method : %s"
msgstr ""

#. translators: 1) user name
#: templates/emails/plain/withdraw-new.php:35
msgid "Store Name : %s"
msgstr ""

#. translators: 1) vendor profile url
#: templates/emails/plain/withdraw-new.php:39
msgid "Profile : %s"
msgstr ""

#. translators: 1) with menu url
#: templates/emails/plain/withdraw-new.php:43
msgid "You can approve or deny it by going here : %s"
msgstr ""

#. translators: 1) Product url 2) Product title
#: templates/emails/product-published.php:25
msgid "Your product : <a href=\"%1$s\">%2$s</a> is approved by the admin, congrats!"
msgstr ""

#. translators: 1) product edit link
#: templates/emails/product-published.php:31
msgid "To Edit product click : <a href=\"%s\">here</a>"
msgstr ""

#. translators: 1) store name
#: templates/emails/reverse-withdrawal-invoice.php:27
msgid "Hi %s,"
msgstr ""

#. translators: 1) invoice month 2) invoice year 3) store name
#: templates/emails/reverse-withdrawal-invoice.php:34
msgid "Your %1$s %2$s invoice is now available for store: %3$s."
msgstr ""

#. translators: 1) store name
#: templates/emails/reverse-withdrawal-invoice.php:45
msgid "Summary for %1$s: "
msgstr ""

#. translators: 1) invoice month 2) invoice year
#: templates/emails/reverse-withdrawal-invoice.php:54
msgid "Reverse withdrawal charges for %1$s %2$s:"
msgstr ""

#: templates/emails/reverse-withdrawal-invoice.php:63
msgid "Due Date: "
msgstr ""

#. translators: %s: Order ID.
#: templates/emails/vendor-completed-order.php:46
#: templates/emails/vendor-new-order.php:45
msgid "[Order #%s]"
msgstr ""

#: templates/emails/vendor-completed-order.php:55
#: templates/emails/vendor-new-order.php:54
msgid "Quantity"
msgstr ""

#: templates/emails/vendor-completed-order.php:56
#: templates/emails/vendor-new-order.php:55
#: templates/products/edit-product-single.php:304
#: templates/products/new-product.php:221
#: templates/products/products-listing-row.php:207
#: templates/products/products-listing.php:127
#: templates/products/tmpl-add-product-popup.php:56
msgid "Price"
msgstr ""

#. translators: 1) product name, 2) customer name, 3) rating
#: templates/emails/vendor-product-review.php:26
msgid "We are happy to inform you that your product <strong>%1$s</strong> has received a new review on our website. The review was written by <strong>%2$s</strong> and has a rating of <strong>%3$s</strong> out of 5 stars."
msgstr ""

#: templates/emails/withdraw-approve.php:28
msgid "Amount : "
msgstr ""

#. translators: 1) withdraw receivable amount
#: templates/emails/withdraw-approve.php:33
#: templates/emails/withdraw-cancel.php:35
msgid "Receivable amount : %s"
msgstr ""

#: templates/emails/withdraw-cancel.php:23
msgid "Your withdraw request was cancelled!"
msgstr ""

#: templates/emails/withdraw-new.php:20
msgid "A new withdraw request has been made by"
msgstr ""

#: templates/emails/withdraw-new.php:26
msgid "Store Name : "
msgstr ""

#: templates/emails/withdraw-new.php:34
msgid "Request Amount:"
msgstr ""

#: templates/emails/withdraw-new.php:40
msgid "Charge:"
msgstr ""

#: templates/emails/withdraw-new.php:46
msgid "Payment Method: "
msgstr ""

#. translators: 1) withdraw page url
#: templates/emails/withdraw-new.php:56
msgid "You can approve or deny it by going <a href=\"%s\"> here </a>"
msgstr ""

#. translators: 1) cart total amount
#: templates/global/header-menu.php:16
msgid "Cart %s"
msgstr ""

#: templates/global/header-menu.php:35
msgid "Vendor Dashboard"
msgstr ""

#: templates/global/header-menu.php:38
msgid "Visit your store"
msgstr ""

#: templates/global/header-menu.php:53
msgid "My Account"
msgstr ""

#: templates/global/header-menu.php:56
#: templates/orders/details.php:121
msgid "Billing Address"
msgstr ""

#: templates/global/header-menu.php:57
#: templates/orders/details.php:136
msgid "Shipping Address"
msgstr ""

#: templates/global/header-menu.php:64
msgid "Log in"
msgstr ""

#: templates/global/header-menu.php:65
msgid "Sign Up"
msgstr ""

#: templates/global/product-tab.php:14
msgid "Vendor Information"
msgstr ""

#: templates/global/product-tab.php:21
msgid "Store Name:"
msgstr ""

#: templates/global/product-tab.php:28
msgid "Vendor:"
msgstr ""

#: templates/global/product-tab.php:38
msgid "Address:"
msgstr ""

#: templates/global/seller-registration-form.php:90
msgid "I am a customer"
msgstr ""

#: templates/global/seller-registration-form.php:95
msgid "I am a vendor"
msgstr ""

#: templates/global/seller-warning.php:11
#: templates/products/edit-product-single.php:220
#: templates/products/new-product.php:119
msgid "Error!"
msgstr ""

#: templates/login-form/login-form.php:17
msgid "Login"
msgstr ""

#: templates/login-form/login-form.php:21
msgid "Logging in"
msgstr ""

#: templates/login-form/login-form.php:26
msgid "or"
msgstr ""

#: templates/login-form/login-form.php:26
msgid "Create an account"
msgstr ""

#: templates/maps/dokan-maps-with-search.php:9
#: templates/widgets/store-map.php:30
msgid "Mapbox Access Token not found"
msgstr ""

#: templates/maps/google-maps-with-search.php:5
msgid "Type an address to find"
msgstr ""

#: templates/maps/google-maps-with-search.php:6
#: templates/maps/mapbox-with-search.php:6
msgid "Find Address"
msgstr ""

#: templates/maps/mapbox-with-search.php:120
#: assets/js/vue-bootstrap.js:2
msgid "Search Address"
msgstr ""

#: templates/my-orders.php:20
msgid "Recent Orders"
msgstr ""

#. translators: 1) order total amount 2) item count
#. translators: 1) order total amount 2) order item count
#: templates/my-orders.php:63
#: templates/sub-orders.php:88
msgid "%1$s for %2$s item"
msgid_plural "%1$s for %2$s items"
msgstr[0] ""
msgstr[1] ""

#: templates/my-orders.php:76
msgid "Multiple Vendor"
msgstr ""

#: templates/my-orders.php:88
msgid "Pay"
msgstr ""

#: templates/my-orders.php:121
msgid "No orders found!"
msgstr ""

#: templates/orders/commission-meta-box-html.php:45
#: templates/products/products-listing-row.php:235
#: templates/products/products-listing.php:129
msgid "Type"
msgstr ""

#: templates/orders/commission-meta-box-html.php:46
msgid "Rate"
msgstr ""

#: templates/orders/commission-meta-box-html.php:47
#: templates/orders/details.php:33
msgid "Qty"
msgstr ""

#: templates/orders/commission-meta-box-html.php:94
msgid "Variation ID:"
msgstr ""

#. translators: %s: variation id
#: templates/orders/commission-meta-box-html.php:100
msgid "%s (No longer exists)"
msgstr ""

#: templates/orders/commission-meta-box-html.php:116
msgid "Source: %s"
msgstr ""

#: templates/orders/commission-meta-box-html.php:160
msgid "Net total:"
msgstr ""

#: templates/orders/commission-meta-box-html.php:174
msgid "Vendor earning:"
msgstr ""

#: templates/orders/commission-meta-box-html.php:189
msgid "Subsidy:"
msgstr ""

#: templates/orders/commission-meta-box-html.php:205
msgid "Shipping Fee:"
msgstr ""

#: templates/orders/commission-meta-box-html.php:237
msgid "Product Tax Fee:"
msgstr ""

#: templates/orders/commission-meta-box-html.php:269
msgid "Shipping Tax Fee:"
msgstr ""

#: templates/orders/commission-meta-box-html.php:301
msgid "Gateway Fee:"
msgstr ""

#: templates/orders/commission-meta-box-html.php:324
msgid "Total commission:"
msgstr ""

#: templates/orders/date-export.php:16
msgid "Filter by registered customer"
msgstr ""

#: templates/orders/date-export.php:20
msgid "Search Orders"
msgstr ""

#: templates/orders/date-export.php:22
#: templates/reverse-withdrawal/filters.php:10
msgid "Select Date Range"
msgstr ""

#: templates/orders/date-export.php:23
msgid "Start Date"
msgstr ""

#: templates/orders/date-export.php:24
msgid "End Date"
msgstr ""

#: templates/orders/date-export.php:27
#: templates/products/listing-filter.php:101
#: templates/reverse-withdrawal/filters.php:15
#: templates/store-lists-filter.php:43
#: assets/js/components.js:172
msgid "Filter"
msgstr ""

#: templates/orders/date-export.php:28
#: templates/products/listing-filter.php:102
#: assets/js/components.js:172
msgid "Reset"
msgstr ""

#: templates/orders/date-export.php:40
msgid "Export All"
msgstr ""

#: templates/orders/date-export.php:41
msgid "Export Filtered"
msgstr ""

#: templates/orders/details.php:5
msgid "This is not yours, I swear!"
msgstr ""

#: templates/orders/details.php:29
msgid "Item"
msgstr ""

#: templates/orders/details.php:35
msgid "Totals"
msgstr ""

#: templates/orders/details.php:94
#: assets/js/dashboard-charts.js:1
#: assets/js/vue-admin.js:2
msgid "Coupons"
msgstr ""

#: templates/orders/details.php:127
msgid "No billing address set."
msgstr ""

#: templates/orders/details.php:142
msgid "No shipping address set."
msgstr ""

#: templates/orders/details.php:153
msgid "Downloadable Product Permission"
msgstr ""

#: templates/orders/details.php:168
msgid "General Details"
msgstr ""

#: templates/orders/details.php:172
msgid "Order Status:"
msgstr ""

#: templates/orders/details.php:176
msgid "&nbsp; Edit"
msgstr ""

#: templates/orders/details.php:202
msgid "Order Date:"
msgstr ""

#: templates/orders/details.php:206
msgid "Earning From Order:"
msgstr ""

#: templates/orders/details.php:213
msgid "Customer:"
msgstr ""

#: templates/orders/details.php:217
msgid "Email:"
msgstr ""

#: templates/orders/details.php:221
msgid "Phone:"
msgstr ""

#: templates/orders/details.php:225
msgid "Customer IP:"
msgstr ""

#: templates/orders/details.php:240
msgid "Customer Note:"
msgstr ""

#: templates/orders/details.php:253
msgid "Order Notes"
msgstr ""

#. translators: 1) human-readable date
#: templates/orders/details.php:280
msgid "added %s ago"
msgstr ""

#: templates/orders/details.php:290
msgid "There are no notes for this order yet."
msgstr ""

#: templates/orders/details.php:299
msgid "Add note"
msgstr ""

#: templates/orders/details.php:307
msgid "Customer note"
msgstr ""

#: templates/orders/details.php:308
msgid "Private note"
msgstr ""

#: templates/orders/details.php:316
#: assets/js/vue-admin.js:2
msgid "Add Note"
msgstr ""

#: templates/orders/details.php:324
#: templates/orders/details.php:333
msgid "Tracking Number"
msgstr ""

#: templates/orders/details.php:328
msgid "Shipping Provider Name / URL"
msgstr ""

#: templates/orders/details.php:338
msgid "Date Shipped"
msgstr ""

#: templates/orders/details.php:346
msgid "Add Tracking Details"
msgstr ""

#: templates/orders/downloadable.php:58
msgid "Search for a downloadable product&hellip;"
msgstr ""

#: templates/orders/downloadable.php:66
msgid "Grant Access"
msgstr ""

#: templates/orders/listing.php:9
#: templates/products/products-listing.php:92
#: assets/js/vue-admin.js:2
msgid "Select bulk action"
msgstr ""

#: templates/orders/listing.php:34
#: templates/orders/listing.php:96
msgid "Customer"
msgstr ""

#: templates/orders/listing.php:37
#: templates/sub-orders.php:49
msgid "Shipment"
msgstr ""

#: templates/orders/listing.php:41
#: templates/orders/listing.php:132
#: templates/products/downloadable.php:40
msgid "Action"
msgstr ""

#. translators: 1) order number
#: templates/orders/listing.php:64
#: templates/orders/listing.php:70
msgid "Order %s"
msgstr ""

#: templates/orders/listing.php:99
msgid "Guest"
msgstr ""

#: templates/orders/listing.php:106
#: templates/orders/listing.php:107
#: templates/products/products-listing-row.php:294
#: templates/products/products-listing-row.php:295
msgid "Unpublished"
msgstr ""

#. translators: 1)  human-readable date
#. translators: %s: time difference
#: templates/orders/listing.php:115
#: templates/products/products-listing-row.php:316
msgid "%s ago"
msgstr ""

#: templates/orders/listing.php:126
#: templates/sub-orders.php:79
msgid "Shipping Status"
msgstr ""

#: templates/orders/listing.php:151
msgid "Complete"
msgstr ""

#: templates/orders/listing.php:203
msgid "No orders found"
msgstr ""

#. translators: 1) download count, 2) download file name
#: templates/orders/order-download-permission-html.php:22
msgid "File %1$s: %2$s"
msgstr ""

#: templates/orders/order-download-permission-html.php:37
msgid "Revoke Access"
msgstr ""

#: templates/orders/order-download-permission-html.php:47
msgid "Downloaded"
msgstr ""

#. translators: 1) file download counter
#: templates/orders/order-download-permission-html.php:52
msgid "%s time"
msgid_plural "%s times"
msgstr[0] ""
msgstr[1] ""

#: templates/orders/order-download-permission-html.php:61
msgid "Downloads Remaining"
msgstr ""

#: templates/orders/order-download-permission-html.php:64
#: templates/products/downloadable.php:59
msgid "Unlimited"
msgstr ""

#: templates/orders/order-download-permission-html.php:67
msgid "Access Expires"
msgstr ""

#: templates/orders/order-download-permission-html.php:72
#: templates/products/downloadable.php:64
msgid "Never"
msgstr ""

#: templates/orders/order-fee-html.php:14
msgid "Fee Name"
msgstr ""

#: templates/orders/order-tax-html.php:8
msgid "Tax Rate:"
msgstr ""

#: templates/orders/order-tax-html.php:18
msgid "Sales Tax:"
msgstr ""

#: templates/orders/order-tax-html.php:22
msgid "Shipping Tax:"
msgstr ""

#: templates/orders/orders-status-filter.php:18
msgid "&larr; Orders"
msgstr ""

#: templates/orders/sub-order-related-order-meta-box-html.php:68
msgid "(Parent order)"
msgstr ""

#: templates/products/add-new-product-modal.php:2
#: templates/products/edit-product-single.php:160
#: templates/products/new-product.php:107
msgid "Add New Product"
msgstr ""

#: templates/products/catalog-mode-content.php:24
msgid "Catalog Mode"
msgstr ""

#: templates/products/catalog-mode-content.php:26
msgid "Enable/Disable Catalog Mode for this product"
msgstr ""

#: templates/products/dokan-category-header-ui.php:23
#: assets/js/dokan-admin-dashboard.js:172
#: assets/js/dokan-setup-wizard-commission.js:1
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Category"
msgstr ""

#: templates/products/dokan-category-header-ui.php:31
#: templates/products/listing-filter.php:33
msgid "- Select a category -"
msgstr ""

#: templates/products/dokan-category-header-ui.php:52
msgid "+ Add new category"
msgstr ""

#: templates/products/dokan-category-ui.php:8
msgid "Add new category"
msgstr ""

#: templates/products/dokan-category-ui.php:9
msgid "Please choose the right category for this product"
msgstr ""

#: templates/products/dokan-category-ui.php:19
msgid "Search category"
msgstr ""

#: templates/products/dokan-category-ui.php:48
msgid "Selected: "
msgstr ""

#: templates/products/dokan-category-ui.php:50
msgid "No category"
msgstr ""

#: templates/products/dokan-category-ui.php:54
#: assets/js/vue-admin.js:2
msgid "Done"
msgstr ""

#: templates/products/dokan-products-edit-bulk-commission.php:21
msgid "Change to:"
msgstr ""

#: templates/products/download-virtual.php:4
#: templates/products/products-listing-row.php:245
msgid "Downloadable"
msgstr ""

#: templates/products/download-virtual.php:4
msgid "Downloadable products give access to a file upon purchase."
msgstr ""

#: templates/products/download-virtual.php:9
#: templates/products/products-listing-row.php:243
msgid "Virtual"
msgstr ""

#: templates/products/download-virtual.php:9
msgid "Virtual products are intangible and aren't shipped."
msgstr ""

#: templates/products/downloadable.php:3
msgid "Downloadable Options"
msgstr ""

#: templates/products/downloadable.php:4
msgid "Configure your downloadable product settings"
msgstr ""

#: templates/products/downloadable.php:31
msgid "Add File"
msgstr ""

#: templates/products/downloadable.php:38
#: templates/products/products-listing-row.php:100
#: templates/products/products-listing.php:120
msgid "Name"
msgstr ""

#: templates/products/downloadable.php:38
msgid "This is the name of the download shown to the customer."
msgstr ""

#: templates/products/downloadable.php:39
msgid "File URL"
msgstr ""

#: templates/products/downloadable.php:39
msgid "This is the URL or absolute path to the file which customers will get access to."
msgstr ""

#: templates/products/downloadable.php:58
msgid "Download Limit"
msgstr ""

#: templates/products/downloadable.php:63
msgid "Download Expiry"
msgstr ""

#: templates/products/edit-product-single.php:19
msgid "Access Denied, No product found"
msgstr ""

#: templates/products/edit-product-single.php:41
msgid "This product is no longer available"
msgstr ""

#: templates/products/edit-product-single.php:80
msgid "Access Denied"
msgstr ""

#: templates/products/edit-product-single.php:162
msgid "Edit Product"
msgstr ""

#: templates/products/edit-product-single.php:192
msgid "View Product"
msgstr ""

#: templates/products/edit-product-single.php:228
#: templates/products/new-product.php:127
#: assets/js/vue-admin.js:2
msgid "Success!"
msgstr ""

#: templates/products/edit-product-single.php:228
msgid "The product has been saved successfully."
msgstr ""

#: templates/products/edit-product-single.php:231
msgid "View Product &rarr;"
msgstr ""

#: templates/products/edit-product-single.php:250
msgid "Title"
msgstr ""

#: templates/products/edit-product-single.php:256
#: templates/products/new-product.php:215
#: templates/products/tmpl-add-product-popup.php:50
msgid "Product name.."
msgstr ""

#: templates/products/edit-product-single.php:262
msgid "Please enter product title!"
msgstr ""

#: templates/products/edit-product-single.php:278
msgid "Product Type"
msgstr ""

#: templates/products/edit-product-single.php:278
msgid "Choose Variable if your product has multiple attributes - like sizes, colors, quality etc"
msgstr ""

#: templates/products/edit-product-single.php:309
msgid " You Earn : "
msgstr ""

#: templates/products/edit-product-single.php:343
#: templates/products/new-product.php:230
#: templates/products/tmpl-add-product-popup.php:65
msgid "Discounted Price"
msgstr ""

#: templates/products/edit-product-single.php:344
#: templates/products/new-product.php:231
#: templates/products/tmpl-add-product-popup.php:66
msgid "Schedule"
msgstr ""

#: templates/products/edit-product-single.php:367
msgid "Product price can't be less than the vendor fee!"
msgstr ""

#: templates/products/edit-product-single.php:375
#: templates/products/edit-product-single.php:382
#: templates/products/new-product.php:246
#: templates/products/new-product.php:253
#: templates/products/tmpl-add-product-popup.php:81
#: templates/products/tmpl-add-product-popup.php:88
msgid "YYYY-MM-DD"
msgstr ""

#: templates/products/edit-product-single.php:393
msgid "Brand"
msgstr ""

#: templates/products/edit-product-single.php:407
#: templates/products/new-product.php:270
#: templates/products/tmpl-add-product-popup.php:104
msgid "Tags"
msgstr ""

#: templates/products/edit-product-single.php:448
#: templates/products/tmpl-add-product-popup.php:24
msgid "Upload a product cover image"
msgstr ""

#: templates/products/edit-product-single.php:488
#: templates/products/new-product.php:195
msgid "Delete image"
msgstr ""

#: templates/products/edit-product-single.php:494
#: templates/products/new-product.php:202
#: templates/products/tmpl-add-product-popup.php:38
msgid "Add gallery image"
msgstr ""

#: templates/products/edit-product-single.php:510
msgid "Short Description"
msgstr ""

#: templates/products/edit-product-single.php:530
#: templates/products/new-product.php:286
msgid "Description"
msgstr ""

#: templates/products/edit-product-single.php:562
#: templates/products/edit-product-single.php:563
msgid "Save Product"
msgstr ""

#: templates/products/inventory.php:9
msgid "Manage inventory for this product."
msgstr ""

#: templates/products/inventory.php:19
#: templates/products/products-listing-row.php:171
#: templates/products/products-listing.php:125
msgid "SKU"
msgstr ""

#: templates/products/inventory.php:19
msgid "(Stock Keeping Unit)"
msgstr ""

#: templates/products/inventory.php:24
msgid "Stock Status"
msgstr ""

#: templates/products/inventory.php:34
msgid "On Backorder"
msgstr ""

#: templates/products/inventory.php:46
msgid "Enable product stock management"
msgstr ""

#: templates/products/inventory.php:52
msgid "Stock quantity"
msgstr ""

#: templates/products/inventory.php:53
#: templates/products/inventory.php:61
msgid "1"
msgstr ""

#: templates/products/inventory.php:60
msgid "Low stock threshold"
msgstr ""

#: templates/products/inventory.php:66
msgid "Allow Backorders"
msgstr ""

#: templates/products/inventory.php:75
msgid "Allow but notify customer"
msgstr ""

#: templates/products/inventory.php:90
msgid "Allow only one quantity of this product to be bought in a single order"
msgstr ""

#: templates/products/listing-filter.php:54
msgid "Product type"
msgstr ""

#: templates/products/listing-filter.php:70
msgid "- Select a brand -"
msgstr ""

#: templates/products/listing-filter.php:115
msgid "Search Products"
msgstr ""

#. translators: 1) All product count
#: templates/products/listing-status-filter.php:23
msgid "All (%s)"
msgstr ""

#: templates/products/listing-status-filter.php:40
msgid "In stock"
msgstr ""

#: templates/products/listing-status-filter.php:46
msgid "Out of stock"
msgstr ""

#. translators: %s: product title with edit link
#: templates/products/new-product.php:131
msgid "You have successfully created %s product"
msgstr ""

#: templates/products/new-product.php:164
msgid "Upload Product Image"
msgstr ""

#: templates/products/new-product.php:260
msgid "Short description of the product..."
msgstr ""

#: templates/products/new-product.php:286
msgid "Add your product description"
msgstr ""

#: templates/products/new-product.php:315
msgid "Create & Add New"
msgstr ""

#: templates/products/new-product.php:318
msgid "Create Product"
msgstr ""

#: templates/products/others.php:7
msgid "Other Options"
msgstr ""

#: templates/products/others.php:8
msgid "Set your extra product options"
msgstr ""

#: templates/products/others.php:17
msgid "Product Status"
msgstr ""

#: templates/products/others.php:28
msgid "Visibility"
msgstr ""

#: templates/products/others.php:41
msgid "Purchase Note"
msgstr ""

#: templates/products/others.php:42
msgid "Customer will get this info in their order email"
msgstr ""

#: templates/products/others.php:52
msgid "Enable product reviews"
msgstr ""

#: templates/products/products-listing-row.php:83
#: templates/products/products-listing.php:119
msgid "Image"
msgstr ""

#: templates/products/products-listing-row.php:238
msgid "Grouped"
msgstr ""

#: templates/products/products-listing-row.php:240
msgid "External/Affiliate"
msgstr ""

#: templates/products/products-listing-row.php:250
msgid "Variable"
msgstr ""

#: templates/products/products-listing-row.php:276
#: templates/products/products-listing.php:130
msgid "Views"
msgstr ""

#: templates/products/products-listing-row.php:324
msgid "Published"
msgstr ""

#: templates/products/products-listing-row.php:327
msgid "Missed schedule"
msgstr ""

#: templates/products/products-listing-row.php:332
msgid "Last Modified"
msgstr ""

#: templates/products/products-listing.php:71
#: templates/products/products-listing.php:292
msgid "Add new product"
msgstr ""

#: templates/products/products-listing.php:237
msgid "No product found"
msgstr ""

#: templates/products/products-listing.php:263
msgid "&laquo; Previous"
msgstr ""

#: templates/products/products-listing.php:264
msgid "Next &raquo;"
msgstr ""

#: templates/products/products-listing.php:280
msgid "No Products Found!"
msgstr ""

#: templates/products/products-listing.php:285
msgid "Ready to start selling something awesome?"
msgstr ""

#: templates/products/tmpl-add-product-popup.php:111
msgid "Enter some short description about this product..."
msgstr ""

#: templates/products/tmpl-add-product-popup.php:119
msgid "Create product"
msgstr ""

#: templates/products/tmpl-add-product-popup.php:129
msgid "Create & add new"
msgstr ""

#: templates/reverse-withdrawal/reverse-balance.php:18
msgid "Reverse Pay Balance: "
msgstr ""

#: templates/reverse-withdrawal/reverse-balance.php:30
msgid "Threshold: "
msgstr ""

#: templates/reverse-withdrawal/reverse-balance.php:36
msgid "Payable Amount: "
msgstr ""

#: templates/reverse-withdrawal/transaction-listing.php:20
#: assets/js/vue-admin.js:2
msgid "Transaction Type"
msgstr ""

#: templates/reverse-withdrawal/transaction-listing.php:22
#: assets/js/vue-admin.js:2
msgid "Debit"
msgstr ""

#: templates/reverse-withdrawal/transaction-listing.php:23
#: assets/js/vue-admin.js:2
msgid "Credit"
msgstr ""

#: templates/reverse-withdrawal/transaction-listing.php:24
#: templates/withdraw/withdraw-dashboard.php:11
#: assets/js/frontend.js:172
#: assets/js/vue-admin.js:2
msgid "Balance"
msgstr ""

#: templates/reverse-withdrawal/transaction-listing.php:62
#: assets/js/dashboard.js:1
msgid "Balance:"
msgstr ""

#: templates/reverse-withdrawal/transaction-listing.php:70
msgid "No transactions found!"
msgstr ""

#. translators: 1) search query
#: templates/seller-search-form.php:6
msgid "Search Results for: %s"
msgstr ""

#: templates/seller-search-form.php:15
msgid "Search Vendor &hellip;"
msgstr ""

#: templates/seller-search-form.php:15
msgid "Search seller &hellip;"
msgstr ""

#: templates/settings/address-form.php:31
msgid "Street "
msgstr ""

#: templates/settings/address-form.php:65
msgid "Apartment, suite, unit etc. (optional)"
msgstr ""

#: templates/settings/address-form.php:88
msgid "Town / City"
msgstr ""

#: templates/settings/address-form.php:96
msgid "Post/ZIP Code"
msgstr ""

#: templates/settings/address-form.php:109
msgid "Postcode / Zip"
msgstr ""

#: templates/settings/address-form.php:124
msgid "Country "
msgstr ""

#: templates/settings/address-form.php:153
msgid "State "
msgstr ""

#: templates/settings/bank-payment-method-settings.php:44
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Please Select..."
msgstr ""

#: templates/settings/bank-payment-method-settings.php:45
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Personal"
msgstr ""

#: templates/settings/bank-payment-method-settings.php:46
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Business"
msgstr ""

#: templates/settings/bank-payment-method-settings.php:167
msgid "bank check"
msgstr ""

#: templates/settings/payment-manage.php:16
#: assets/js/dokan-admin-dashboard.js:172
#: assets/js/dokan-admin-onboard.js:172
#: assets/js/frontend.js:172
msgid "Back"
msgstr ""

#: templates/settings/payment.php:16
#: templates/withdraw/withdraw-dashboard.php:91
#: assets/js/frontend.js:172
#: assets/js/vue-admin.js:2
msgid "Payment Methods"
msgstr ""

#: templates/settings/payment.php:19
msgid "Add Payment Method"
msgstr ""

#. translators: %s: payment method title
#: templates/settings/payment.php:33
msgid "Direct to %s"
msgstr ""

#: templates/settings/payment.php:45
msgid "There is no payment method to add."
msgstr ""

#: templates/settings/payment.php:74
msgid "Manage"
msgstr ""

#: templates/settings/payment.php:83
msgid "There is no payment method to show."
msgstr ""

#: templates/settings/store-form.php:39
#: templates/store-lists-loop.php:43
msgid "Open"
msgstr ""

#: templates/settings/store-form.php:99
msgid "Profile Picture"
msgstr ""

#: templates/settings/store-form.php:109
msgid "Upload Photo"
msgstr ""

#: templates/settings/store-form.php:118
msgid "store name"
msgstr ""

#: templates/settings/store-form.php:141
msgid "Phone No"
msgstr ""

#: templates/settings/store-form.php:143
msgid "+123456.."
msgstr ""

#: templates/settings/store-form.php:199
msgid "Show terms and conditions in store page"
msgstr ""

#: templates/settings/store-form.php:205
msgid "TOC Details"
msgstr ""

#: templates/settings/store-form.php:227
msgid "Store Schedule"
msgstr ""

#: templates/settings/store-form.php:234
msgid "Store has open close time"
msgstr ""

#: templates/store-header.php:17
msgid "Store Open"
msgstr ""

#: templates/store-header.php:18
msgid "Store Closed"
msgstr ""

#: templates/store-header.php:125
msgid "Weekly Store Timing"
msgstr ""

#: templates/store-header.php:126
msgid "CLOSED"
msgstr ""

#. translators: 1) number of stores
#: templates/store-lists-filter.php:28
msgid "Total store showing: %s"
msgid_plural "Total stores showing: %s"
msgstr[0] ""
msgstr[1] ""

#: templates/store-lists-filter.php:48
#: templates/store-lists-filter.php:50
msgid "Sort by"
msgstr ""

#: templates/store-lists-filter.php:74
#: assets/js/vue-admin.js:2
msgid "Search Vendors"
msgstr ""

#: templates/store-lists-loop.php:36
msgid "Featured"
msgstr ""

#: templates/store-lists-loop.php:43
msgid "Store is Open"
msgstr ""

#: templates/store-lists-loop.php:45
msgid "Store is Closed"
msgstr ""

#: templates/store-lists-loop.php:45
msgid "Closed"
msgstr ""

#. translators: 1) seller rating
#: templates/store-lists-loop.php:58
msgid "Rated %s out of 5"
msgstr ""

#. translators: 1) seller rating
#: templates/store-lists-loop.php:67
msgid "%s out of 5"
msgstr ""

#: templates/store-lists-loop.php:156
msgid "No vendor found!"
msgstr ""

#: templates/store-toc.php:42
msgid "Terms And Conditions"
msgstr ""

#: templates/store.php:70
msgid "No products were found of this vendor!"
msgstr ""

#: templates/sub-orders.php:19
msgid "Sub Orders"
msgstr ""

#: templates/sub-orders.php:34
msgid "This order has products from multiple vendors. So we divided this order into multiple vendor orders. Each order will be handled by their respective vendor independently."
msgstr ""

#. translators: %d reviews count
#: templates/vendor-store-info.php:32
msgid "%s Review"
msgid_plural "%s Reviews"
msgstr[0] ""
msgstr[1] ""

#: templates/widgets/store-contact-form.php:18
msgid "<EMAIL>"
msgstr ""

#: templates/widgets/store-contact-form.php:21
msgid "Type your message..."
msgstr ""

#: templates/widgets/store-contact-form.php:31
msgid "Send Message"
msgstr ""

#: templates/widgets/store-open-close.php:18
msgid "Off Day"
msgstr ""

#: templates/widgets/widget-content-product.php:51
msgid "No products found"
msgstr ""

#: templates/withdraw/approved-request-listing.php:16
#: templates/withdraw/cancelled-request-listing.php:16
#: templates/withdraw/pending-request-listing-dashboard.php:19
#: templates/withdraw/pending-request-listing.php:16
#: templates/withdraw/pending-request-listing.php:60
#: assets/js/components.js:172
#: assets/js/dashboard-charts.js:1
#: assets/js/frontend.js:172
#: assets/js/vue-admin.js:2
msgid "Amount"
msgstr ""

#: templates/withdraw/approved-request-listing.php:18
#: templates/withdraw/cancelled-request-listing.php:18
#: templates/withdraw/pending-request-listing-dashboard.php:22
#: assets/js/frontend.js:172
#: assets/js/vue-admin.js:2
msgid "Charge"
msgstr ""

#: templates/withdraw/approved-request-listing.php:19
#: templates/withdraw/cancelled-request-listing.php:19
#: templates/withdraw/pending-request-listing-dashboard.php:23
#: assets/js/frontend.js:172
msgid "Receivable"
msgstr ""

#: templates/withdraw/header.php:13
#: assets/js/frontend.js:172
msgid "Send Withdraw Request"
msgstr ""

#: templates/withdraw/pending-request-listing-dashboard.php:16
#: templates/withdraw/status-listing.php:29
#: assets/js/frontend.js:172
msgid "Pending Requests"
msgstr ""

#: templates/withdraw/pending-request-listing-dashboard.php:54
#: templates/withdraw/pending-request-listing.php:47
msgid "Accepted"
msgstr ""

#: templates/withdraw/pending-request-listing.php:68
msgid "No pending withdraw request"
msgstr ""

#: templates/withdraw/request-form.php:39
msgid "Withdraw Amount"
msgstr ""

#: templates/withdraw/request-form.php:52
msgid "Withdraw Charge"
msgstr ""

#: templates/withdraw/request-form.php:61
#: assets/js/frontend.js:172
msgid "Receivable amount"
msgstr ""

#: templates/withdraw/request-form.php:71
msgid "Submit Request"
msgstr ""

#: templates/withdraw/request-form.php:78
msgid "No withdraw method is available. Please update your payment method to withdraw funds."
msgstr ""

#: templates/withdraw/request-form.php:78
msgid "Payment Settings Setup"
msgstr ""

#: templates/withdraw/status-listing.php:32
#: assets/js/frontend.js:172
msgid "Approved Requests"
msgstr ""

#: templates/withdraw/status-listing.php:35
#: assets/js/frontend.js:172
msgid "Cancelled Requests"
msgstr ""

#: templates/withdraw/status-listing.php:40
#: templates/withdraw/withdraw-dashboard.php:50
#: assets/js/frontend.js:172
msgid "Request Withdraw"
msgstr ""

#: templates/withdraw/status-listing.php:42
msgid "Withdraw Dashboard"
msgstr ""

#: templates/withdraw/withdraw-dashboard.php:16
#: assets/js/frontend.js:172
msgid "Your Balance:"
msgstr ""

#: templates/withdraw/withdraw-dashboard.php:26
msgid "Minimum Withdraw Amount:"
msgstr ""

#: templates/withdraw/withdraw-dashboard.php:31
msgid "Withdraw Threshold:"
msgstr ""

#. translators: 1) withdraw threshold days
#: templates/withdraw/withdraw-dashboard.php:37
msgid "%s day"
msgid_plural "%s days"
msgstr[0] ""
msgstr[1] ""

#: templates/withdraw/withdraw-dashboard.php:64
#: assets/js/frontend.js:172
msgid "Payment Details"
msgstr ""

#: templates/withdraw/withdraw-dashboard.php:69
#: assets/js/frontend.js:172
msgid "Last Payment"
msgstr ""

#: templates/withdraw/withdraw-dashboard.php:77
#: assets/js/frontend.js:172
msgid "View Payments"
msgstr ""

#: templates/withdraw/withdraw-dashboard.php:108
#: assets/js/frontend.js:172
msgid "Default"
msgstr ""

#: templates/withdraw/withdraw-dashboard.php:110
#: assets/js/frontend.js:172
msgid "Make Default"
msgstr ""

#: assets/js/components.js:172
#: assets/js/dokan-admin-dashboard.js:172
#: assets/js/dokan-intelligence.js:172
#: assets/js/frontend.js:172
msgid "%1$s at %2$s"
msgstr ""

#: assets/js/components.js:172
msgid "Confirmation Dialog"
msgstr ""

#: assets/js/components.js:172
msgid "Delete Confirmation"
msgstr ""

#: assets/js/components.js:172
msgid "Are you sure you want to proceed with this %1$sdeletion%2$s?"
msgstr ""

#: assets/js/components.js:172
msgid "Yes, Delete"
msgstr ""

#: assets/js/components.js:172
msgid "No options"
msgstr ""

#: assets/js/components.js:172
msgid "Select or Upload Media"
msgstr ""

#: assets/js/components.js:172
#: assets/js/vendor-dashboard/reports/index.js:172
msgid "Oh no! Something went wrong…"
msgstr ""

#: assets/js/components.js:172
#: assets/js/vendor-dashboard/reports/index.js:172
msgid "Kindly refresh the page to load data or try again."
msgstr ""

#: assets/js/components.js:172
#: assets/js/dokan-admin-onboard.js:172
#: assets/js/vendor-dashboard/reports/index.js:172
msgid "Try Again"
msgstr ""

#: assets/js/components.js:172
#: assets/js/vue-admin.js:2
msgid "Enter amount"
msgstr ""

#: assets/js/components.js:179
#: assets/js/frontend.js:172
msgid "Sorry, the page can’t be found"
msgstr ""

#: assets/js/components.js:179
#: assets/js/frontend.js:172
msgid "The page you were looking for appears to have been moved, deleted or does not exist"
msgstr ""

#: assets/js/components.js:179
#: assets/js/dokan-admin-dashboard.js:172
#: assets/js/frontend.js:172
msgid "Back to Dashboard"
msgstr ""

#: assets/js/components.js:179
#: assets/js/dokan-admin-dashboard.js:172
#: assets/js/frontend.js:172
msgid "Permission Denied"
msgstr ""

#: assets/js/components.js:179
#: assets/js/dokan-admin-dashboard.js:172
#: assets/js/frontend.js:172
msgid "Sorry, you don’t have permission to access this page"
msgstr ""

#: assets/js/customizable-dashboard.js:1
msgid "No data recorded for the selected time period."
msgstr ""

#: assets/js/customizable-dashboard.js:1
msgid "There was an error getting your stats. Please try again."
msgstr ""

#: assets/js/customizable-dashboard.js:1
msgid "Reload"
msgstr ""

#: assets/js/customizable-dashboard.js:1
msgid "Performance"
msgstr ""

#: assets/js/customizable-dashboard.js:1
#: assets/js/dashboard-charts.js:1
msgid "Charts"
msgstr ""

#: assets/js/customizable-dashboard.js:1
msgid "Section title"
msgstr ""

#: assets/js/customizable-dashboard.js:1
msgid "Move up"
msgstr ""

#: assets/js/customizable-dashboard.js:1
msgid "Move down"
msgstr ""

#: assets/js/customizable-dashboard.js:1
msgid "Remove block"
msgstr ""

#: assets/js/customizable-dashboard.js:1
msgid "Remove section"
msgstr ""

#: assets/js/customizable-dashboard.js:1
msgid "Add more sections"
msgstr ""

#: assets/js/customizable-dashboard.js:1
msgid "Dashboard Sections"
msgstr ""

#: assets/js/customizable-dashboard.js:1
msgid "Add %s section"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "No data for the current search"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "No data for the selected date range"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "%s Report"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "TAX"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Net sales"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Average order value"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Average items per order"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "All orders"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Advanced filters"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "A sentence describing filters for Orders. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ"
msgid "Orders match <select/> filters"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Remove order status filter"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select an order status filter match"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "<title>Order status</title> <rule/> <filter/>"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select an order status"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "order status"
msgid "Is"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "order status"
msgid "Is Not"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Search products"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Remove product filter"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select a product filter match"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "<title>Product</title> <rule/> <filter/>"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select products"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "products"
msgid "Includes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "products"
msgid "Excludes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Product variation"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Search product variations"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Remove product variation filter"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select a product variation filter match"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "<title>Product variation</title> <rule/> <filter/>"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select variation"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "variations"
msgid "Includes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "variations"
msgid "Excludes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Coupon code"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Search coupons"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Remove coupon filter"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select a coupon filter match"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "<title>Coupon code</title> <rule/> <filter/>"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select coupon codes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "coupon code"
msgid "Includes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "coupon code"
msgid "Excludes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Customer type"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Remove customer filter"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select a customer filter match"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "<title>Customer is</title> <filter/>"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select a customer type"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "New"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Returning"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Remove refund filter"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select a refund filter match"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "<title>Refund</title> <filter/>"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select a refund type"
msgstr ""

#: assets/js/dashboard-charts.js:1
#: assets/js/vue-admin.js:2
msgid "All"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Partially refunded"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Fully refunded"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "None"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Tax rate"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Search tax rates"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Remove tax rate filter"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select a tax rate filter match"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "<title>Tax Rate</title> <rule/> <filter/>"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select tax rates"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "tax rate"
msgid "Includes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "tax rate"
msgid "Excludes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Product attribute"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Search product attributes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Remove product attribute filter"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select a product attribute filter match"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "<title>Product attribute</title> <rule/> <filter/>"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select attributes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "product attribute"
msgid "Is"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "product attribute"
msgid "Is Not"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Items sold"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "All products"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Single product"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Type to search for a product"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Comparison"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Check at least two products below to compare"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Search for products to compare"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Compare Products"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Compare"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "All variations"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Single variation"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Type to search for a variation"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Check at least two variations below to compare"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Search for variations to compare"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Compare Variations"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "A sentence describing filters for Products. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ"
msgid "Products Match <select/> Filters"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Advanced Filters"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Gross sales"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Returns"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Full refunds are not deducted from tax or net sales totals"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Taxes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Total sales"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "A sentence describing filters for Revenue. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ"
msgid "Revenue Matches <select/> Filters"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "All Revenue"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Discounted orders"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "A sentence describing filters for Coupons. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ"
msgid "Coupons match <select/> filters"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "All coupons"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Single coupon"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Type to search for a coupon"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Single Coupon"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Compare Coupon Codes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Check at least two coupon codes below to compare"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Total tax"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Order tax"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Shipping tax"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "A sentence describing filters for Taxes. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ"
msgid "Taxes match <select/> filters"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "All taxes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Check at least two tax codes below to compare"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Search for tax codes to compare"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Compare Tax Codes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Downloads"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "All downloads"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "A sentence describing filters for Downloads. See screen shot for context: https://cloudup.com/ccxhyH2mEDg"
msgid "Downloads match <select/> filters"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select product"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Search customer username"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Remove customer username filter"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select a customer username filter match"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "<title>Username</title> <rule/> <filter />"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select customer username"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "customer usernames"
msgid "Includes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "customer usernames"
msgid "Excludes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Order #"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Search order number"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Remove order number filter"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select a order number filter match"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "<title>Order #</title> <rule/> <filter/>"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select order number"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "order numbers"
msgid "Includes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "order numbers"
msgid "Excludes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "IP Address"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Search IP address"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Remove IP address filter"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select an IP address filter match"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "<title>IP Address</title> <rule/> <filter/>"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Select IP address"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "IP addresses"
msgid "Includes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgctxt "IP addresses"
msgid "Excludes"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Gross discounted"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "By hour"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "By day"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "By week"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "By month"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "By quarter"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "By year"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Choose which charts to display"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Line chart"
msgstr ""

#: assets/js/dashboard-charts.js:1
msgid "Bar chart"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Lite: %s"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Pro"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "%1$s: %2$s"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Video"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Requirements: %s"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Upgrade the Plan & Unlock"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Premium modules are limited in Dokan Lite. Upgrade to Dokan Pro to access more features and drive greater growth!"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "40+ Premium Modules"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Advanced Shipping Management"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "100+ Payment Methods"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Subscription & Recurring Payments"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "24/7 Priority Based Support"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Detailed Reporting"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Multilingual Integration"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "And Many More!"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
#: assets/js/vue-admin.js:2
msgid "Upgrade to Pro"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Flat 20%"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Bonus: As a Dokan Lite user, use the coupon"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "at checkout for 20% off."
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Already Upgraded?"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Confused? Talk to the Sales Team!"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Select %1$s Category %2$s"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
#: assets/js/dokan-setup-wizard-commission.js:1
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "All Categories"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Search Modules"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Pro Modules (%s)"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Clear filter"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Currency"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Enter currency amount"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
#: assets/js/dokan-setup-wizard-commission.js:1
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "%"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
#: assets/js/dokan-setup-wizard-commission.js:1
#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "+"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Category ID"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "#%s"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "unknown_error"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Retry Loading"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Skip"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Saving…"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "Your Marketplace is  ready to explore"
msgstr ""

#: assets/js/dokan-admin-dashboard.js:172
msgid "%1$s of %1$s tasks completed"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Welcome to Your New Multi-Vendor Marketplace!"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Ready to revolutionize online selling? We're excited to help you create a marketplace that stands out! Let's start with a few quick questions."
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Start Journey"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Basic Information"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Choose how vendor store URLs will appear"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "%s/"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "/vendor-name"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "write-your-store-name"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Please enter a valid input"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Vendor Store URL will be (<span class=\"text-indigo-600\">%1$s/%2$s/%3$s</span>)"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "vendor-name"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Help Us Tailor Your Marketplace"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Allow Dokan Multivendor Marketplace to collect non-sensitive diagnostic data and usage information."
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Please enter a valid store URL"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Digital Products"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Physical Products"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Services"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Subscriptions"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Marketplace handles delivery"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Vendors manage their deliveries"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "No delivery needed (Digital Products)"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "International market reach"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Maximizing sales conversion"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Local store management"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Marketplace Goal"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "What's the primary focus of your marketplace?"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "How do you plan to handle deliveries?"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "What's your top priority?"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Plugin"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Enhance your marketplace with this plugin"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Enhance Your Marketplace with these Recommended Add-ons"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "We recommend some plugins for the marketplace. You can select from the following suggestions and based on your selection we will install and activate them for you so that your experience remains flawless"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Preparing your resources"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Congratulations! 🎉"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "You're all set to start selling."
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Explore Dashboard"
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Failed to load initial data. Please refresh the page."
msgstr ""

#: assets/js/dokan-admin-onboard.js:172
msgid "Failed to submit data. Please try again."
msgstr ""

#: assets/js/dokan-intelligence.js:172
msgid "No content generated, please try again."
msgstr ""

#: assets/js/dokan-intelligence.js:172
msgid "Please enter a prompt."
msgstr ""

#: assets/js/dokan-intelligence.js:172
msgid "AI Assistant"
msgstr ""

#: assets/js/dokan-intelligence.js:172
msgid "Craft your product information"
msgstr ""

#: assets/js/dokan-intelligence.js:172
msgid "Start Over"
msgstr ""

#: assets/js/dokan-intelligence.js:172
#: assets/js/vue-admin.js:2
msgid "Error"
msgstr ""

#: assets/js/dokan-intelligence.js:172
msgid "Refine"
msgstr ""

#: assets/js/dokan-intelligence.js:172
msgid "Short Description:"
msgstr ""

#: assets/js/dokan-intelligence.js:172
msgid "Long Description:"
msgstr ""

#: assets/js/dokan-intelligence.js:172
msgid "** If you think the outcome doesn’t match your choice then you can"
msgstr ""

#: assets/js/dokan-intelligence.js:172
msgid "regenerate all again."
msgstr ""

#: assets/js/dokan-intelligence.js:172
msgid "You can generate your product title, short description, long description all at once with this prompt. Type your prompt below"
msgstr ""

#: assets/js/dokan-intelligence.js:172
msgid "Enter prompt"
msgstr ""

#: assets/js/dokan-intelligence.js:172
msgid "Insert"
msgstr ""

#: assets/js/dokan-intelligence.js:172
msgid "Generating…"
msgstr ""

#: assets/js/dokan-intelligence.js:172
msgid "Generate"
msgstr ""

#: assets/js/dokan-intelligence.js:172
msgid "Insert Generated Information?"
msgstr ""

#: assets/js/dokan-intelligence.js:172
msgid "Are you sure you want to insert the generated information? If you insert then your current product information will be updated with the generated content."
msgstr ""

#: assets/js/dokan-intelligence.js:172
msgid "Yes, Insert"
msgstr ""

#: assets/js/dokan-setup-wizard-commission.js:1
#: assets/js/vue-admin.js:2
msgid "Enable Commission Inheritance Setting?"
msgstr ""

#: assets/js/dokan-setup-wizard-commission.js:1
#: assets/js/vue-admin.js:2
msgid "Disable Commission Inheritance Setting?"
msgstr ""

#: assets/js/dokan-setup-wizard-commission.js:1
#: assets/js/vue-admin.js:2
msgid "Parent category commission changes will automatically update all subcategories. Existing rates will remain unchanged until parent category is modified."
msgstr ""

#: assets/js/dokan-setup-wizard-commission.js:1
#: assets/js/vue-admin.js:2
msgid "Subcategories will maintain their independent commission rates when parent category rates are changed."
msgstr ""

#: assets/js/dokan-setup-wizard-commission.js:1
#: assets/js/vue-admin.js:2
msgid "Enable"
msgstr ""

#: assets/js/dokan-setup-wizard-commission.js:1
#: assets/js/vue-admin.js:2
msgid "Disable"
msgstr ""

#: assets/js/dokan-setup-wizard-commission.js:1
#: assets/js/vue-admin.js:2
msgid "Admin Commission type"
msgstr ""

#: assets/js/dokan-setup-wizard-commission.js:1
#: assets/js/vue-admin.js:2
msgid "Apply Parent Category Commission to All Subcategories "
msgstr ""

#: assets/js/dokan-status.js:1
msgid "Select a tab"
msgstr ""

#: assets/js/dokan-status.js:1
msgid "All are up-to-date."
msgstr ""

#: assets/js/frontend.js:172
msgid "Withdraw method"
msgstr ""

#: assets/js/frontend.js:172
msgid "Withdraw charge"
msgstr ""

#: assets/js/frontend.js:172
msgid "Calculating…"
msgstr ""

#: assets/js/frontend.js:172
msgid "No payment methods found to submit a withdrawal request."
msgstr ""

#: assets/js/frontend.js:172
msgid "Please set up your %1$spayment methods%2$s first."
msgstr ""

#: assets/js/frontend.js:172
msgid "Withdraw request created."
msgstr ""

#: assets/js/frontend.js:172
msgid "Failed to create withdraw."
msgstr ""

#: assets/js/frontend.js:172
msgid "Creating…"
msgstr ""

#: assets/js/frontend.js:172
msgid "Submit request"
msgstr ""

#: assets/js/frontend.js:172
msgid "Minimum Withdraw Amount: "
msgstr ""

#: assets/js/frontend.js:172
#: assets/js/vue-admin.js:2
msgid "Approved"
msgstr ""

#: assets/js/frontend.js:172
msgid "Request cancelled successfully"
msgstr ""

#: assets/js/frontend.js:172
msgid "Failed to cancel request"
msgstr ""

#: assets/js/frontend.js:172
msgid "Cancel Withdraw Request"
msgstr ""

#: assets/js/frontend.js:172
msgid "Are you sure you want to proceed?"
msgstr ""

#: assets/js/frontend.js:172
msgid "Do you want to proceed for cancelling the withdraw request?"
msgstr ""

#: assets/js/frontend.js:172
msgid "Yes, Cancel"
msgstr ""

#: assets/js/frontend.js:172
msgid "on"
msgstr ""

#: assets/js/frontend.js:172
msgid "to"
msgstr ""

#: assets/js/frontend.js:172
msgid "Default method updated"
msgstr ""

#: assets/js/leaderboards.js:1
msgid "Leaderboards"
msgstr ""

#: assets/js/leaderboards.js:1
msgid "Choose which leaderboards to display and other settings"
msgstr ""

#: assets/js/leaderboards.js:1
msgid "Rows per table"
msgstr ""

#: assets/js/setup-guide-banner.js:172
msgid "Setup Step Icon"
msgstr ""

#: assets/js/setup-guide-banner.js:172
msgid "Complete your marketplace setup in minutes"
msgstr ""

#: assets/js/setup-guide-banner.js:172
msgid "Start Setup"
msgstr ""

#: assets/js/setup-guide-banner.js:172
msgid "Continue Setup"
msgstr ""

#: assets/js/store-performance.js:1
msgid "Choose which analytics to display and the section name"
msgstr ""

#: assets/js/store-performance.js:1
msgid "Display stats:"
msgstr ""

#: assets/js/store-performance.js:1
msgid "Previous period:"
msgstr ""

#: assets/js/store-performance.js:1
msgid "Previous year:"
msgstr ""

#: assets/js/store-performance.js:1
msgid "Store Performance"
msgstr ""

#: assets/js/vendor-dashboard/reports/index.js:2
msgid "Mutable settings should be accessed via data store."
msgstr ""

#: assets/js/vendor-dashboard/reports/index.js:2
msgid "Loading…"
msgstr ""

#: assets/js/vendor-dashboard/reports/index.js:2
msgid "Sorry, you are not allowed to access this page."
msgstr ""

#: assets/js/vendor-dashboard/reports/index.js:2
msgid "Analytics"
msgstr ""

#: assets/js/vendor-dashboard/reports/index.js:2
msgid "Not allowed"
msgstr ""

#: assets/js/vendor-dashboard/reports/index.js:2
msgid "%1$s &lsaquo; %2$s &#8212; WooCommerce"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "At a Glance"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "net sales this month"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "commission earned"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "signup this month"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "awaiting approval"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "created this month"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Withdrawals"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Dokan News Updates"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Thank you for subscribing!"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Stay up-to-date"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "We're constantly developing new features, stay up-to-date by subscribing to our newsletter."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Your Email Address"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Subscribe"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "No requests found."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Paypal Mass Payment File is Generated."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Payable"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Details"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Approve"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Download PayPal mass payment file"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Swift Code: %s"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Withdraw Requests"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Update Note"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Approve Request"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Mark as Pending"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Filter by Vendor"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Filter by Payment Methods"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Filter by Date"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Export"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Convinced?"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "With all the advance features you get it’s hard to resist buying Dokan Pro."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Premium modules to make everything easier & better"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Frontend dashboard for vendors with advanced controls"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Unlimited Product Variations and group product upload"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Zone wise shipping with multiple method for vendors"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Store support based on ticket system for your customers"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Vendors will be able to generate coupon codes"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Earning, Selling and Commission Reports & Statement"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "24/7 super fast premium customer support for you"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Add Social profile to your vendor’s store and support for store SEO"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Frontend order management"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Seller Statement Reports"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Making Announcements"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Customized Product Categories"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Store SEO"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Back Ordering System"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Store Contact Form"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Single Product Multiple Seller"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Seller Verification"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Featured Seller"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Multiple Commission Types"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Frontend Shipping Tracking"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Setup Wizard"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Bookable Product"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Upgrade to Dokan Pro!"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Seems To Be Convinced, You Need More Out Of Your Marketplace"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Dokan PRO offers powerful vendor controls and features, giving sellers full ownership, automation, and freedom to run their stores. Upgrade to PRO to unlock these great features!."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Check Out All Vendor Functionalities"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Why Upgrade"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "And Many More"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Comparison With Dokan PRO"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "The Packages We Provide"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Buy Now"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "14 Days Money Back Guarantee"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "After successful purchase, you will be eligible for conditional refund"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Terms & Condition Applied"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Payment Options:"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "People We Have Helped"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "We are proud to say the official"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "is built using Dokan"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Help"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Dokan Changelog"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Lite"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "PRO"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Jump to version"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Latest"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Current"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "View Less..."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Continue reading..."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Settings Menu"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Copied"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Copy to clipboard"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Click to view"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Automated"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "is required."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Both percentage and fixed fee is required."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Active"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Select"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Dokan Settings Banner"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Looking for Coupons, Variable Products, SEO or Shipping?"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Unlock these awesome frontend features with Dokan PRO"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Choose your file"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Withdraw Method Changed"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Do you want to send an announcement to vendors about the removal of currently active payment method?"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Save & send announcement"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Save only"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Disbursement Schedule Updated"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Do you want to inform your vendors about the removal of the previous disbursement schedule by sending them an announcement?"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Save and Send Announcement"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Save Only"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "All data and tables related to Dokan and Dokan Pro will be deleted permanently after deleting the Dokan plugin. You will not be able to recover your lost data unless you keep a backup. Do you want to continue?"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Okay"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Dismiss this notice."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Toggle Settings Menu"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Search e.g. vendor"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Save Changes"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Back to top"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Dokan Back to Top Button"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Select & Crop Image"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Select Image"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Searching..."
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "This email is already registered, please choose another one."
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Account Info"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Vendor Picture"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "You can change your profile picture on %s"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Upload Banner"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Store Name is required"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Store Category"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Store Url"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "+*********"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Email is required"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "<EMAIL>"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Username is required"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Send the vendor an email about their account."
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Generate Password"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Street 1"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Zip"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Select Country"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Select State"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Payment Options"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "**********"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "*********"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Swift"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Publish Product Directly"
msgstr ""

#: assets/js/vue-admin.js:2
#: assets/js/vue-bootstrap.js:2
msgid "Make Vendor Featured"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Add New Vendor"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Vendor Created"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "A vendor has been created successfully!"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Add Another"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Edit Vendor"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Create Vendor"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Registered"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Approve Vendors"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Disable Selling"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Switch To"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "The vendor has been disabled."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Selling has been enabled"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Add New"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Commission Options"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Set the commission type that admin will get"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Not Set"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Saving..."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Email has been sent successfully."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Vendor Updated"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Vendor Updated Successfully!"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Go Back"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Send Email"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Reply-To"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Message"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Change Store Photo"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "(No Name)"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Set Store Categories"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Set Store Category"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Select Category"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Add Categories"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Add Category"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Change Store Banner"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Total Products"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Items Sold"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Store Visitors"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Revenue"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Orders Processed"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Gross Sales"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Others"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Reviews"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Registered Since"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Social Profiles"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Dokan PayPal Marketplace"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Dokan Stripe Connect"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Bank Payment"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Dokan Razorpay"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Dokan MangoPay"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Custom Payment"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Product Publishing"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Direct"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Requires Review"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Import"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Done!"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Import complete!"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "View vendors"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "View products"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Clear dummy data"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Import dummy vendors and products"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "This tool allows you to import vendor and some products for vendors to your marketplace."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Run the importer"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Each vendor can create unlimited discount coupon codes for their products."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Vendors can configure their own shipping costs for each country, state & single products."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Duplicate Product"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Vendors can duplicate their own products for ease and time saving."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Reporting"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Earning, selling and commissions reports for vendors to improve sales & take major decisions."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Create Tags"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Vendors can create & attach tags to products to categorize & for better search engine optimization."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Vendor Biography"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Vendors can write about themselves & about their store in a text field which visitors can see from the store page"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Vendor Product Upload"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "New vendors can start uploading products upon registration if admins allow."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Vendors will be able to mark products as draft & update the order status to inform customers about progress."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Social Share"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Visitors and customers can share a store page with their friends and acquaintances on their social profiles."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Vendors can create variable products with predefined and custom attributes."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Store Opening & Closing Time"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Vendors can define the working hours of their online store for each day of the week for visitors to see."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Woocommerce Booking Integration"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Create hotel rooms, resorts, conference rooms, cars, bikes, etc for renting out."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Announcement For Vendors"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Admins can make announcements targeted to a single vendor, multiple or all vendors."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Woocommerce Simple Auctions Integration"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Sell auction-able products in your store with Dokan’s integration."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Vendors can upload & showcase their Facebook, Twitter and Linkedin profiles on their store page."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Vendors can send refund requests for an order to the admins who can approve or deny it from the dashboard."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Store Seo"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "The Yoast SEO integration lets vendors define Store Title, Description, Slug and Keyword to appear in search engine results."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "One of the finest attractions of Dokan PRO is the vast array of powerful vendor controls & functions it provides so sellers can enjoy ownership, automation & freedom to run their stores. To use these awesome vendor features listed below, consider Upgrading to PRO."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Dokan Capability"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Add New Reverse Withdrawal"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Reverse withdrawal created successfully."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Search vendor"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Please select a vendor"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Select Product"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Search product"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Please select a product"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Select Order"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Search order"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Please select a order"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Withdrawal Balance Type"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Adjust Balance by Creating a New Reverse Withdrawal Entry"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Reverse Withdrawal Amount"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Kindly provide the withdrawal amount"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Notes"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Write reverse withdrawal note"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Please write reverse withdrawal note"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "No transaction found."
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Stores"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Last Payment Date"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "All Stores"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Total Collected"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Remaining Balance"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Total Transactions"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Total Vendors"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Filter by store"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Filter by expire date"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "Loading"
msgstr ""

#: assets/js/vue-admin.js:2
msgid "items"
msgstr ""

#: assets/js/vue-bootstrap.js:2
msgid "Close modal panel"
msgstr ""

#: assets/js/vue-bootstrap.js:2
msgid "Select an image"
msgstr ""

#: assets/js/vue-bootstrap.js:2
msgid "Please select an image,"
msgstr ""

#: assets/js/vue-bootstrap.js:2
msgid "Please enter google map API key"
msgstr ""

#: assets/js/vue-bootstrap.js:2
msgid "Please enter Mapbox access token in `Appearance > Mapbox Access Token` settings."
msgstr ""

#: assets/js/vue-bootstrap.js:2
msgid "Refreshing options"
msgstr ""

#: assets/js/vue-bootstrap.js:2
msgid "Option refreshed!"
msgstr ""

#: assets/js/vue-bootstrap.js:2
msgid "Social Options"
msgstr ""
