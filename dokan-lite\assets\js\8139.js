/*! For license information please see 8139.js.LICENSE.txt */
"use strict";(self.webpackChunkdokan=self.webpackChunkdokan||[]).push([[8139],{2809:(e,t,n)=>{n.d(t,{A:()=>i});var o=n(5573),r=n(790);const i=(0,r.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(o.<PERSON>,{d:"m14.5 6.5-1 1 3.7 3.7H4v1.6h13.2l-3.7 3.7 1 1 5.6-5.5z"})})},3349:(e,t,n)=>{n.d(t,{A:()=>i});var o=n(5573),r=n(790);const i=(0,r.jsx)(o.<PERSON>G,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(o.<PERSON>,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 5.5A2.25 2.25 0 0 0 9.878 7h4.244A2.251 2.251 0 0 0 12 5.5ZM12 4a3.751 3.751 0 0 0-3.675 3H5v1.5h1.27l.818 8.997a2.75 2.75 0 0 0 2.739 2.501h4.347a2.75 2.75 0 0 0 2.738-2.5L17.73 8.5H19V7h-3.325A3.751 3.751 0 0 0 12 4Zm4.224 4.5H7.776l.806 8.861a1.25 1.25 0 0 0 1.245 1.137h4.347a1.25 1.25 0 0 0 1.245-1.137l.805-8.861Z"})})},3526:(e,t,n)=>{t.A=function(e){var t=e.size,n=void 0===t?24:t,o=e.onClick,l=(e.icon,e.className),c=function(e,t){if(null==e)return{};var n,o,r=function(e,t){if(null==e)return{};var n,o,r={},i=Object.keys(e);for(o=0;o<i.length;o++)n=i[o],0<=t.indexOf(n)||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],0<=t.indexOf(n)||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}(e,i),s=["gridicon","gridicons-chevron-down",l,!1,!1,!1].filter(Boolean).join(" ");return r.default.createElement("svg",a({className:s,height:n,width:n,onClick:o},c,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),r.default.createElement("g",null,r.default.createElement("path",{d:"M20 9l-8 8-8-8 1.414-1.414L12 14.172l6.586-6.586z"})))};var o,r=(o=n(1609))&&o.__esModule?o:{default:o},i=["size","onClick","icon","className"];function a(){return a=Object.assign?Object.assign.bind():function(e){for(var t,n=1;n<arguments.length;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},a.apply(this,arguments)}},4253:(e,t,n)=>{n.d(t,{A:()=>i});var o=n(5573),r=n(790);const i=(0,r.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(o.Path,{d:"M2 12C2 6.44444 6.44444 2 12 2C17.5556 2 22 6.44444 22 12C22 17.5556 17.5556 22 12 22C6.44444 22 2 17.5556 2 12ZM13 11V7H11V11H7V13H11V17H13V13H17V11H13Z"})})},6447:(e,t,n)=>{n.d(t,{A:()=>i});var o=n(5573),r=n(790);const i=(0,r.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(o.Path,{fillRule:"evenodd",d:"M11.25 5h1.5v15h-1.5V5zM6 10h1.5v10H6V10zm12 4h-1.5v6H18v-6z",clipRule:"evenodd"})})},7677:(e,t,n)=>{n.d(t,{A:()=>r});var o=n(6087);const r=(0,o.forwardRef)(function({icon:e,size:t=24,...n},r){return(0,o.cloneElement)(e,{width:t,height:t,...n,ref:r})})},9418:(e,t,n)=>{n.d(t,{A:()=>ne});const{entries:o,setPrototypeOf:r,isFrozen:i,getPrototypeOf:a,getOwnPropertyDescriptor:l}=Object;let{freeze:c,seal:s,create:u}=Object,{apply:m,construct:p}="undefined"!=typeof Reflect&&Reflect;c||(c=function(e){return e}),s||(s=function(e){return e}),m||(m=function(e,t,n){return e.apply(t,n)}),p||(p=function(e,t){return new e(...t)});const f=x(Array.prototype.forEach),d=x(Array.prototype.lastIndexOf),h=x(Array.prototype.pop),g=x(Array.prototype.push),y=x(Array.prototype.splice),T=x(String.prototype.toLowerCase),E=x(String.prototype.toString),A=x(String.prototype.match),b=x(String.prototype.replace),w=x(String.prototype.indexOf),S=x(String.prototype.trim),_=x(Object.prototype.hasOwnProperty),v=x(RegExp.prototype.test),N=(O=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return p(O,t)});var O;function x(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return m(e,t,o)}}function R(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:T;r&&r(e,null);let o=t.length;for(;o--;){let r=t[o];if("string"==typeof r){const e=n(r);e!==r&&(i(t)||(t[o]=e),r=e)}e[r]=!0}return e}function C(e){for(let t=0;t<e.length;t++)_(e,t)||(e[t]=null);return e}function k(e){const t=u(null);for(const[n,r]of o(e))_(e,n)&&(Array.isArray(r)?t[n]=C(r):r&&"object"==typeof r&&r.constructor===Object?t[n]=k(r):t[n]=r);return t}function L(e,t){for(;null!==e;){const n=l(e,t);if(n){if(n.get)return x(n.get);if("function"==typeof n.value)return x(n.value)}e=a(e)}return function(){return null}}const D=c(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),M=c(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),I=c(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),z=c(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),P=c(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),H=c(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),U=c(["#text"]),F=c(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),j=c(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),B=c(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),G=c(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),W=s(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Y=s(/<%[\w\W]*|[\w\W]*%>/gm),V=s(/\$\{[\w\W]*/gm),X=s(/^data-[\-\w.\u00B7-\uFFFF]+$/),q=s(/^aria-[\-\w]+$/),Z=s(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),$=s(/^(?:\w+script|data):/i),K=s(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),J=s(/^html$/i),Q=s(/^[a-z][.\w]*(-[.\w]+)+$/i);var ee=Object.freeze({__proto__:null,ARIA_ATTR:q,ATTR_WHITESPACE:K,CUSTOM_ELEMENT:Q,DATA_ATTR:X,DOCTYPE_NAME:J,ERB_EXPR:Y,IS_ALLOWED_URI:Z,IS_SCRIPT_OR_DATA:$,MUSTACHE_EXPR:W,TMPLIT_EXPR:V});const te=function(){return"undefined"==typeof window?null:window};var ne=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:te();const n=t=>e(t);if(n.version="3.2.6",n.removed=[],!t||!t.document||9!==t.document.nodeType||!t.Element)return n.isSupported=!1,n;let{document:r}=t;const i=r,a=i.currentScript,{DocumentFragment:l,HTMLTemplateElement:s,Node:m,Element:p,NodeFilter:O,NamedNodeMap:x=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:C,DOMParser:W,trustedTypes:Y}=t,V=p.prototype,X=L(V,"cloneNode"),q=L(V,"remove"),$=L(V,"nextSibling"),K=L(V,"childNodes"),Q=L(V,"parentNode");if("function"==typeof s){const e=r.createElement("template");e.content&&e.content.ownerDocument&&(r=e.content.ownerDocument)}let ne,oe="";const{implementation:re,createNodeIterator:ie,createDocumentFragment:ae,getElementsByTagName:le}=r,{importNode:ce}=i;let se={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};n.isSupported="function"==typeof o&&"function"==typeof Q&&re&&void 0!==re.createHTMLDocument;const{MUSTACHE_EXPR:ue,ERB_EXPR:me,TMPLIT_EXPR:pe,DATA_ATTR:fe,ARIA_ATTR:de,IS_SCRIPT_OR_DATA:he,ATTR_WHITESPACE:ge,CUSTOM_ELEMENT:ye}=ee;let{IS_ALLOWED_URI:Te}=ee,Ee=null;const Ae=R({},[...D,...M,...I,...P,...U]);let be=null;const we=R({},[...F,...j,...B,...G]);let Se=Object.seal(u(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),_e=null,ve=null,Ne=!0,Oe=!0,xe=!1,Re=!0,Ce=!1,ke=!0,Le=!1,De=!1,Me=!1,Ie=!1,ze=!1,Pe=!1,He=!0,Ue=!1,Fe=!0,je=!1,Be={},Ge=null;const We=R({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Ye=null;const Ve=R({},["audio","video","img","source","image","track"]);let Xe=null;const qe=R({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Ze="http://www.w3.org/1998/Math/MathML",$e="http://www.w3.org/2000/svg",Ke="http://www.w3.org/1999/xhtml";let Je=Ke,Qe=!1,et=null;const tt=R({},[Ze,$e,Ke],E);let nt=R({},["mi","mo","mn","ms","mtext"]),ot=R({},["annotation-xml"]);const rt=R({},["title","style","font","a","script"]);let it=null;const at=["application/xhtml+xml","text/html"];let lt=null,ct=null;const st=r.createElement("form"),ut=function(e){return e instanceof RegExp||e instanceof Function},mt=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!ct||ct!==e){if(e&&"object"==typeof e||(e={}),e=k(e),it=-1===at.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,lt="application/xhtml+xml"===it?E:T,Ee=_(e,"ALLOWED_TAGS")?R({},e.ALLOWED_TAGS,lt):Ae,be=_(e,"ALLOWED_ATTR")?R({},e.ALLOWED_ATTR,lt):we,et=_(e,"ALLOWED_NAMESPACES")?R({},e.ALLOWED_NAMESPACES,E):tt,Xe=_(e,"ADD_URI_SAFE_ATTR")?R(k(qe),e.ADD_URI_SAFE_ATTR,lt):qe,Ye=_(e,"ADD_DATA_URI_TAGS")?R(k(Ve),e.ADD_DATA_URI_TAGS,lt):Ve,Ge=_(e,"FORBID_CONTENTS")?R({},e.FORBID_CONTENTS,lt):We,_e=_(e,"FORBID_TAGS")?R({},e.FORBID_TAGS,lt):k({}),ve=_(e,"FORBID_ATTR")?R({},e.FORBID_ATTR,lt):k({}),Be=!!_(e,"USE_PROFILES")&&e.USE_PROFILES,Ne=!1!==e.ALLOW_ARIA_ATTR,Oe=!1!==e.ALLOW_DATA_ATTR,xe=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Re=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,Ce=e.SAFE_FOR_TEMPLATES||!1,ke=!1!==e.SAFE_FOR_XML,Le=e.WHOLE_DOCUMENT||!1,Ie=e.RETURN_DOM||!1,ze=e.RETURN_DOM_FRAGMENT||!1,Pe=e.RETURN_TRUSTED_TYPE||!1,Me=e.FORCE_BODY||!1,He=!1!==e.SANITIZE_DOM,Ue=e.SANITIZE_NAMED_PROPS||!1,Fe=!1!==e.KEEP_CONTENT,je=e.IN_PLACE||!1,Te=e.ALLOWED_URI_REGEXP||Z,Je=e.NAMESPACE||Ke,nt=e.MATHML_TEXT_INTEGRATION_POINTS||nt,ot=e.HTML_INTEGRATION_POINTS||ot,Se=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ut(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Se.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ut(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Se.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(Se.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Ce&&(Oe=!1),ze&&(Ie=!0),Be&&(Ee=R({},U),be=[],!0===Be.html&&(R(Ee,D),R(be,F)),!0===Be.svg&&(R(Ee,M),R(be,j),R(be,G)),!0===Be.svgFilters&&(R(Ee,I),R(be,j),R(be,G)),!0===Be.mathMl&&(R(Ee,P),R(be,B),R(be,G))),e.ADD_TAGS&&(Ee===Ae&&(Ee=k(Ee)),R(Ee,e.ADD_TAGS,lt)),e.ADD_ATTR&&(be===we&&(be=k(be)),R(be,e.ADD_ATTR,lt)),e.ADD_URI_SAFE_ATTR&&R(Xe,e.ADD_URI_SAFE_ATTR,lt),e.FORBID_CONTENTS&&(Ge===We&&(Ge=k(Ge)),R(Ge,e.FORBID_CONTENTS,lt)),Fe&&(Ee["#text"]=!0),Le&&R(Ee,["html","head","body"]),Ee.table&&(R(Ee,["tbody"]),delete _e.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw N('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw N('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');ne=e.TRUSTED_TYPES_POLICY,oe=ne.createHTML("")}else void 0===ne&&(ne=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const o="data-tt-policy-suffix";t&&t.hasAttribute(o)&&(n=t.getAttribute(o));const r="dompurify"+(n?"#"+n:"");try{return e.createPolicy(r,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+r+" could not be created."),null}}(Y,a)),null!==ne&&"string"==typeof oe&&(oe=ne.createHTML(""));c&&c(e),ct=e}},pt=R({},[...M,...I,...z]),ft=R({},[...P,...H]),dt=function(e){g(n.removed,{element:e});try{Q(e).removeChild(e)}catch(t){q(e)}},ht=function(e,t){try{g(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){g(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(Ie||ze)try{dt(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},gt=function(e){let t=null,n=null;if(Me)e="<remove></remove>"+e;else{const t=A(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===it&&Je===Ke&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const o=ne?ne.createHTML(e):e;if(Je===Ke)try{t=(new W).parseFromString(o,it)}catch(e){}if(!t||!t.documentElement){t=re.createDocument(Je,"template",null);try{t.documentElement.innerHTML=Qe?oe:o}catch(e){}}const i=t.body||t.documentElement;return e&&n&&i.insertBefore(r.createTextNode(n),i.childNodes[0]||null),Je===Ke?le.call(t,Le?"html":"body")[0]:Le?t.documentElement:i},yt=function(e){return ie.call(e.ownerDocument||e,e,O.SHOW_ELEMENT|O.SHOW_COMMENT|O.SHOW_TEXT|O.SHOW_PROCESSING_INSTRUCTION|O.SHOW_CDATA_SECTION,null)},Tt=function(e){return e instanceof C&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof x)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},Et=function(e){return"function"==typeof m&&e instanceof m};function At(e,t,o){f(e,e=>{e.call(n,t,o,ct)})}const bt=function(e){let t=null;if(At(se.beforeSanitizeElements,e,null),Tt(e))return dt(e),!0;const o=lt(e.nodeName);if(At(se.uponSanitizeElement,e,{tagName:o,allowedTags:Ee}),ke&&e.hasChildNodes()&&!Et(e.firstElementChild)&&v(/<[/\w!]/g,e.innerHTML)&&v(/<[/\w!]/g,e.textContent))return dt(e),!0;if(7===e.nodeType)return dt(e),!0;if(ke&&8===e.nodeType&&v(/<[/\w]/g,e.data))return dt(e),!0;if(!Ee[o]||_e[o]){if(!_e[o]&&St(o)){if(Se.tagNameCheck instanceof RegExp&&v(Se.tagNameCheck,o))return!1;if(Se.tagNameCheck instanceof Function&&Se.tagNameCheck(o))return!1}if(Fe&&!Ge[o]){const t=Q(e)||e.parentNode,n=K(e)||e.childNodes;if(n&&t)for(let o=n.length-1;o>=0;--o){const r=X(n[o],!0);r.__removalCount=(e.__removalCount||0)+1,t.insertBefore(r,$(e))}}return dt(e),!0}return e instanceof p&&!function(e){let t=Q(e);t&&t.tagName||(t={namespaceURI:Je,tagName:"template"});const n=T(e.tagName),o=T(t.tagName);return!!et[e.namespaceURI]&&(e.namespaceURI===$e?t.namespaceURI===Ke?"svg"===n:t.namespaceURI===Ze?"svg"===n&&("annotation-xml"===o||nt[o]):Boolean(pt[n]):e.namespaceURI===Ze?t.namespaceURI===Ke?"math"===n:t.namespaceURI===$e?"math"===n&&ot[o]:Boolean(ft[n]):e.namespaceURI===Ke?!(t.namespaceURI===$e&&!ot[o])&&!(t.namespaceURI===Ze&&!nt[o])&&!ft[n]&&(rt[n]||!pt[n]):!("application/xhtml+xml"!==it||!et[e.namespaceURI]))}(e)?(dt(e),!0):"noscript"!==o&&"noembed"!==o&&"noframes"!==o||!v(/<\/no(script|embed|frames)/i,e.innerHTML)?(Ce&&3===e.nodeType&&(t=e.textContent,f([ue,me,pe],e=>{t=b(t,e," ")}),e.textContent!==t&&(g(n.removed,{element:e.cloneNode()}),e.textContent=t)),At(se.afterSanitizeElements,e,null),!1):(dt(e),!0)},wt=function(e,t,n){if(He&&("id"===t||"name"===t)&&(n in r||n in st))return!1;if(Oe&&!ve[t]&&v(fe,t));else if(Ne&&v(de,t));else if(!be[t]||ve[t]){if(!(St(e)&&(Se.tagNameCheck instanceof RegExp&&v(Se.tagNameCheck,e)||Se.tagNameCheck instanceof Function&&Se.tagNameCheck(e))&&(Se.attributeNameCheck instanceof RegExp&&v(Se.attributeNameCheck,t)||Se.attributeNameCheck instanceof Function&&Se.attributeNameCheck(t))||"is"===t&&Se.allowCustomizedBuiltInElements&&(Se.tagNameCheck instanceof RegExp&&v(Se.tagNameCheck,n)||Se.tagNameCheck instanceof Function&&Se.tagNameCheck(n))))return!1}else if(Xe[t]);else if(v(Te,b(n,ge,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==w(n,"data:")||!Ye[e])if(xe&&!v(he,b(n,ge,"")));else if(n)return!1;return!0},St=function(e){return"annotation-xml"!==e&&A(e,ye)},_t=function(e){At(se.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||Tt(e))return;const o={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:be,forceKeepAttr:void 0};let r=t.length;for(;r--;){const i=t[r],{name:a,namespaceURI:l,value:c}=i,s=lt(a),u=c;let m="value"===a?u:S(u);if(o.attrName=s,o.attrValue=m,o.keepAttr=!0,o.forceKeepAttr=void 0,At(se.uponSanitizeAttribute,e,o),m=o.attrValue,!Ue||"id"!==s&&"name"!==s||(ht(a,e),m="user-content-"+m),ke&&v(/((--!?|])>)|<\/(style|title)/i,m)){ht(a,e);continue}if(o.forceKeepAttr)continue;if(!o.keepAttr){ht(a,e);continue}if(!Re&&v(/\/>/i,m)){ht(a,e);continue}Ce&&f([ue,me,pe],e=>{m=b(m,e," ")});const p=lt(e.nodeName);if(wt(p,s,m)){if(ne&&"object"==typeof Y&&"function"==typeof Y.getAttributeType)if(l);else switch(Y.getAttributeType(p,s)){case"TrustedHTML":m=ne.createHTML(m);break;case"TrustedScriptURL":m=ne.createScriptURL(m)}if(m!==u)try{l?e.setAttributeNS(l,a,m):e.setAttribute(a,m),Tt(e)?dt(e):h(n.removed)}catch(t){ht(a,e)}}else ht(a,e)}At(se.afterSanitizeAttributes,e,null)},vt=function e(t){let n=null;const o=yt(t);for(At(se.beforeSanitizeShadowDOM,t,null);n=o.nextNode();)At(se.uponSanitizeShadowNode,n,null),bt(n),_t(n),n.content instanceof l&&e(n.content);At(se.afterSanitizeShadowDOM,t,null)};return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=null,r=null,a=null,c=null;if(Qe=!e,Qe&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Et(e)){if("function"!=typeof e.toString)throw N("toString is not a function");if("string"!=typeof(e=e.toString()))throw N("dirty is not a string, aborting")}if(!n.isSupported)return e;if(De||mt(t),n.removed=[],"string"==typeof e&&(je=!1),je){if(e.nodeName){const t=lt(e.nodeName);if(!Ee[t]||_e[t])throw N("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof m)o=gt("\x3c!----\x3e"),r=o.ownerDocument.importNode(e,!0),1===r.nodeType&&"BODY"===r.nodeName||"HTML"===r.nodeName?o=r:o.appendChild(r);else{if(!Ie&&!Ce&&!Le&&-1===e.indexOf("<"))return ne&&Pe?ne.createHTML(e):e;if(o=gt(e),!o)return Ie?null:Pe?oe:""}o&&Me&&dt(o.firstChild);const s=yt(je?e:o);for(;a=s.nextNode();)bt(a),_t(a),a.content instanceof l&&vt(a.content);if(je)return e;if(Ie){if(ze)for(c=ae.call(o.ownerDocument);o.firstChild;)c.appendChild(o.firstChild);else c=o;return(be.shadowroot||be.shadowrootmode)&&(c=ce.call(i,c,!0)),c}let u=Le?o.outerHTML:o.innerHTML;return Le&&Ee["!doctype"]&&o.ownerDocument&&o.ownerDocument.doctype&&o.ownerDocument.doctype.name&&v(J,o.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+o.ownerDocument.doctype.name+">\n"+u),Ce&&f([ue,me,pe],e=>{u=b(u,e," ")}),ne&&Pe?ne.createHTML(u):u},n.setConfig=function(){mt(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),De=!0},n.clearConfig=function(){ct=null,De=!1},n.isValidAttribute=function(e,t,n){ct||mt({});const o=lt(e),r=lt(t);return wt(o,r,n)},n.addHook=function(e,t){"function"==typeof t&&g(se[e],t)},n.removeHook=function(e,t){if(void 0!==t){const n=d(se[e],t);return-1===n?void 0:y(se[e],n,1)[0]}return h(se[e])},n.removeHooks=function(e){se[e]=[]},n.removeAllHooks=function(){se={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},n}()},9671:(e,t,n)=>{t.A=function(e){var t=e.size,n=void 0===t?24:t,o=e.onClick,l=(e.icon,e.className),c=function(e,t){if(null==e)return{};var n,o,r=function(e,t){if(null==e)return{};var n,o,r={},i=Object.keys(e);for(o=0;o<i.length;o++)n=i[o],0<=t.indexOf(n)||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],0<=t.indexOf(n)||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}(e,i),s=["gridicon","gridicons-chevron-up",l,!1,!1,!1].filter(Boolean).join(" ");return r.default.createElement("svg",a({className:s,height:n,width:n,onClick:o},c,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),r.default.createElement("g",null,r.default.createElement("path",{d:"M4 15l8-8 8 8-1.414 1.414L12 9.828l-6.586 6.586z"})))};var o,r=(o=n(1609))&&o.__esModule?o:{default:o},i=["size","onClick","icon","className"];function a(){return a=Object.assign?Object.assign.bind():function(e){for(var t,n=1;n<arguments.length;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},a.apply(this,arguments)}}}]);