!function(e,a,r){const t={init(){e(".reverse-balance-section").on("click","#reverse_pay",t.add_to_cart)},add_to_cart(){e(this);let a=e("#reverse_pay_balance");t.disableProps();let o={price:a.val(),_reverse_withdrawal_nonce:dokan.reverse_withdrawal.nonce};wp.ajax.post("dokan_reverse_withdrawal_payment_to_cart",o).then(async e=>{let a={action:"confirm",title:dokan.reverse_withdrawal.on_success_title,icon:"success",showCloseButton:!1,showCancelButton:!1,focusConfirm:!0};await dokan_sweetalert(e.message,a).then(()=>{r.location.replace(dokan.reverse_withdrawal.checkout_url)})}).fail(e=>{t.disableProps(!1);let a=dokan_handle_ajax_error(e);a&&dokan_sweetalert(a,{action:"error",title:dokan.reverse_withdrawal.on_error_title,icon:"error"})})},disableProps(a=!0){e("#reverse_pay_balance").prop("disabled",a),e("#reverse_pay").prop("disabled",a)}};e(function(){t.init()})}(jQuery,document,window);