.minitoggle{
  position: relative;
  width: 40px;
  height: 18px;
  background-color: #ccc;
  border: 1px solid #ccc;
  border-radius: 34px;
  float: left;
}

.minitoggle .toggle-handle {
  position: absolute;
  top: 1px;
  left: 2px;
  z-index: 2;
  width: 14px;
  height: 14px;
  background-color: #fff;
  background-image: -webkit-linear-gradient(top, #fff 0, #f2f2f2 100%);
  background-image: linear-gradient(to bottom, #fff 0, #f2f2f2 100%);
  border-radius: 100px;
  -webkit-transition: -webkit-transform 0.4s ease-in-out, border 0.4s ease-in-out;
  transition: transform 0.4s ease-in-out, border 0.4s ease-in-out;
  box-shadow: 0px 2px 3px 0 rgba(0,0,0,0.20);
}

.minitoggle.active {
  background-color: #2271b1;
  border: #2271b1;
  box-shadow: inset 0 0 15px rgba(255, 255, 255, .25);
}

.minitoggle.active .toggle-handle {
  transform: translate3d(28px,0,0);
  top: 2px;
}

.minitoggle.blue.active {
  background-color: #69BDFE;
  border-color: #69BDFE;
}

.minitoggle.blue.active .toggle-handle {
  border-color: #3C78A5;
}